
import React, { useState, useEffect } from 'react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { TouchButton } from '@/components/ui/touch-button';
import { Settings, BarChart3, TrendingUp, DollarSign, Lightbulb } from 'lucide-react';
import { SummaryTab } from './tabs/SummaryTab';
import { AnalysisTab } from './tabs/AnalysisTab';
import { CashFlowTab } from './tabs/CashFlowTab';
import { InsightsTab } from './tabs/InsightsTab';
import { DashboardCustomizer } from './DashboardCustomizer';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export const DashboardTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState('summary');
  const [showCustomizer, setShowCustomizer] = useState(false);
  const { isMobile, isTablet } = useBreakpoint();

  // Guardar tab activo en localStorage
  useEffect(() => {
    const savedTab = localStorage.getItem('dashboard-active-tab');
    if (savedTab) {
      setActiveTab(savedTab);
    }
  }, []);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    localStorage.setItem('dashboard-active-tab', value);
  };

  const tabItems = [
    { value: 'summary', label: isMobile ? 'Inicio' : 'Resumen', icon: BarChart3 },
    { value: 'analysis', label: isMobile ? 'Análisis' : 'Análisis', icon: TrendingUp },
    { value: 'cashflow', label: isMobile ? 'Flujo' : 'Flujo de Caja', icon: DollarSign },
    { value: 'insights', label: isMobile ? 'Tips' : 'Insights', icon: Lightbulb },
  ];

  return (
    <div className={`space-y-3 md:space-y-6 ${isMobile ? 'px-1' : ''}`}>
      {/* Header optimizado para móvil */}
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="space-y-1">
          <h1 className={`
            font-bold text-gray-900 dark:text-white
            ${isMobile ? 'text-lg' : isTablet ? 'text-xl' : 'text-3xl'}
          `}>
            {isMobile ? 'Dashboard' : 'Dashboard Profesional'}
          </h1>
          <p className={`
            text-gray-600 dark:text-gray-300
            ${isMobile ? 'text-xs' : 'text-sm'}
          `}>
            {isMobile ? 'Vista financiera' : 'Vista optimizada de tu situación financiera'}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <TouchButton
            variant="outline"
            size={isMobile ? "sm" : "default"}
            onClick={() => setShowCustomizer(true)}
            className="flex items-center"
          >
            <Settings className="w-4 h-4" />
            {!isMobile && <span className="ml-2">Personalizar</span>}
          </TouchButton>
        </div>
      </div>

      {/* Tabs principales - optimizados para móvil */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className={`
          grid w-full mb-3 md:mb-6
          ${isMobile ? 'grid-cols-4 h-auto p-1 gap-0.5' : 'grid-cols-4 h-10'}
        `}>
          {tabItems.map((tab) => (
            <TabsTrigger 
              key={tab.value}
              value={tab.value} 
              className={`
                flex items-center transition-all duration-200
                ${isMobile ? 
                  'flex-col gap-0.5 py-2 px-1 text-xs min-h-[50px] font-medium' : 
                  isTablet ?
                    'gap-1 px-3 py-2 text-xs' :
                    'gap-2 px-3 py-2 text-sm'
                }
                ${activeTab === tab.value ? 'text-primary' : 'text-muted-foreground'}
              `}
            >
              <tab.icon className={`
                ${isMobile ? 'w-4 h-4' : 'w-4 h-4'}
                ${activeTab === tab.value ? 'text-primary' : ''}
              `} />
              <span className={`${isMobile ? 'leading-tight text-center' : ''} truncate whitespace-nowrap`}>
                {tab.label}
              </span>
            </TabsTrigger>
          ))}
        </TabsList>

        <div className={`
          ${isMobile ? 'min-h-[calc(100vh-200px)] px-1' : ''}
        `}>
          <TabsContent value="summary" className="space-y-3 md:space-y-6 mt-0">
            <SummaryTab />
          </TabsContent>

          <TabsContent value="analysis" className="space-y-3 md:space-y-6 mt-0">
            <AnalysisTab />
          </TabsContent>

          <TabsContent value="cashflow" className="space-y-3 md:space-y-6 mt-0">
            <CashFlowTab />
          </TabsContent>

          <TabsContent value="insights" className="space-y-3 md:space-y-6 mt-0">
            <InsightsTab />
          </TabsContent>
        </div>
      </Tabs>

      {/* Modal de personalización responsivo */}
      {showCustomizer && (
        <DashboardCustomizer 
          isOpen={showCustomizer} 
          onClose={() => setShowCustomizer(false)} 
        />
      )}
    </div>
  );
};
