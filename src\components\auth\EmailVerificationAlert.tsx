
import React, { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Mail, AlertTriangle, Loader2, CheckCircle, X } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface EmailVerificationAlertProps {
  userEmail?: string;
  isVerified: boolean;
  onDismiss?: () => void;
  showDismiss?: boolean;
}

export const EmailVerificationAlert: React.FC<EmailVerificationAlertProps> = ({ 
  userEmail, 
  isVerified, 
  onDismiss,
  showDismiss = false 
}) => {
  const [isResending, setIsResending] = useState(false);
  const [lastSentAt, setLastSentAt] = useState<Date | null>(null);

  if (isVerified) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800 flex items-center justify-between">
          <div>
            <p className="font-medium">Email verificado</p>
            <p className="text-sm">Tu cuenta está completamente activada.</p>
          </div>
          {showDismiss && onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  const handleResendVerification = async () => {
    if (!userEmail) {
      toast.error('No se encontró el email del usuario');
      return;
    }

    setIsResending(true);
    
    try {
      const redirectUrl = `${window.location.origin}/`;
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: userEmail,
        options: {
          emailRedirectTo: redirectUrl
        }
      });

      if (error) {
        console.error('Resend verification error:', error);
        toast.error('Error al reenviar email de verificación');
        return;
      }

      setLastSentAt(new Date());
      toast.success('Email de verificación reenviado');
    } catch (error) {
      console.error('Resend verification error:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
    } finally {
      setIsResending(false);
    }
  };

  const canResend = !lastSentAt || (Date.now() - lastSentAt.getTime()) > 60000; // 1 minuto

  return (
    <Alert className="border-amber-200 bg-amber-50">
      <AlertTriangle className="h-4 w-4 text-amber-600" />
      <AlertDescription className="text-amber-800">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <p className="font-medium">Email no verificado</p>
            <p className="text-sm mt-1">
              Revisa tu bandeja de entrada y haz clic en el enlace de verificación para activar tu cuenta.
            </p>
            {userEmail && (
              <p className="text-xs mt-1 opacity-75">
                Email enviado a: {userEmail}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleResendVerification}
              disabled={isResending || !canResend}
              className="text-amber-700 border-amber-300 hover:bg-amber-100"
            >
              {isResending ? (
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              ) : (
                <Mail className="w-3 h-3 mr-1" />
              )}
              {isResending ? 'Enviando...' : 'Reenviar'}
            </Button>
            
            {showDismiss && onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-6 w-6 p-0 text-amber-600 hover:text-amber-700"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        
        {!canResend && (
          <p className="text-xs mt-2 opacity-75">
            Espera {60 - Math.floor((Date.now() - lastSentAt!.getTime()) / 1000)} segundos para reenviar
          </p>
        )}
      </AlertDescription>
    </Alert>
  );
};
