
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, CheckCircle, AlertTriangle, XCircle, Calendar } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';

interface KeyMetricsCardsProps {
  netBalance: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
  paymentCoverage?: number; // Nuevo: porcentaje de ingresos que cubren los pagos
}

export function KeyMetricsCards({ 
  netBalance, 
  savingsRate, 
  debtToIncomeRatio, 
  emergencyFundMonths,
  paymentCoverage = 0
}: KeyMetricsCardsProps) {
  const getHealthStatus = (value: number, goodThreshold: number, warningThreshold: number) => {
    if (value >= goodThreshold) return { status: 'good', color: 'text-green-500', icon: CheckCircle };
    if (value >= warningThreshold) return { status: 'warning', color: 'text-yellow-500', icon: AlertTriangle };
    return { status: 'poor', color: 'text-red-500', icon: XCircle };
  };

  const savingsHealth = getHealthStatus(savingsRate, 20, 10);
  const debtHealth = getHealthStatus(100 - debtToIncomeRatio, 60, 40);
  const emergencyHealth = getHealthStatus(emergencyFundMonths, 6, 3);
  const paymentHealth = getHealthStatus(100 - paymentCoverage, 40, 20); // Invertido: menos cobertura es mejor

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <Card className="bg-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground">Balance Neto</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            {netBalance >= 0 ? (
              <TrendingUp className="w-5 h-5 text-green-500" />
            ) : (
              <TrendingDown className="w-5 h-5 text-red-500" />
            )}
            <span className={`text-xl font-bold ${netBalance >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {formatCurrency(netBalance, 'DOP')}
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Después de pagos programados
          </p>
        </CardContent>
      </Card>

      <Card className="bg-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground">Cobertura de Pagos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Calendar className={`w-5 h-5 ${paymentHealth.color}`} />
            <span className={`text-xl font-bold ${paymentHealth.color}`}>
              {paymentCoverage.toFixed(1)}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            De tus ingresos van a pagos
          </p>
        </CardContent>
      </Card>

      <Card className="bg-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground">Tasa de Ahorro</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <savingsHealth.icon className={`w-5 h-5 ${savingsHealth.color}`} />
            <span className={`text-xl font-bold ${savingsHealth.color}`}>
              {savingsRate.toFixed(1)}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Meta: 20% o más
          </p>
        </CardContent>
      </Card>

      <Card className="bg-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground">Ratio Deuda/Ingreso</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <debtHealth.icon className={`w-5 h-5 ${debtHealth.color}`} />
            <span className={`text-xl font-bold ${debtHealth.color}`}>
              {debtToIncomeRatio.toFixed(1)}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Meta: 30% o menos
          </p>
        </CardContent>
      </Card>

      <Card className="bg-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm text-muted-foreground">Fondo de Emergencia</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <emergencyHealth.icon className={`w-5 h-5 ${emergencyHealth.color}`} />
            <span className={`text-xl font-bold ${emergencyHealth.color}`}>
              {emergencyFundMonths.toFixed(1)} meses
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Meta: 6 meses o más
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
