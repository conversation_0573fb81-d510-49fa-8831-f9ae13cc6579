
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { NumericInput } from '@/components/ui/numeric-input';
import { DatePicker } from '@/components/ui/date-picker';
import { Switch } from '@/components/ui/switch';
import { defaultCategories, Expense } from '@/types';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface ExpenseFormFieldsProps {
  formData: Partial<Expense>;
  onFormDataChange: (updates: Partial<Expense>) => void;
  disabled?: boolean;
}

export const ExpenseFormFields = React.memo(function ExpenseFormFields({ formData, onFormDataChange, disabled = false }: ExpenseFormFieldsProps) {
  const isDarkMode = useIsDarkMode();
  const { isMobile } = useBreakpoint();
  
  // Función para convertir string a Date
  const parseDate = (dateString: string): Date | undefined => {
    if (!dateString) return undefined;
    return new Date(dateString);
  };

  // Función para convertir Date a string
  const formatDate = (date: Date | undefined): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // Función para manejar el cambio de categoría sin generar descripción automática
  const handleCategoryChange = (categoryId: string) => {
    const category = defaultCategories.find(cat => cat.id === categoryId);
    onFormDataChange({
      categoryId,
      categoryName: category?.name || ''
    });
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expense-form-date">Fecha *</Label>
          {isDarkMode ? (
            <DatePicker
              id="expense-form-date"
              date={parseDate(formData.date || '')}
              onSelect={(date) => onFormDataChange({ date: formatDate(date) })}
              placeholder="Selecciona una fecha"
              disabled={disabled}
              name="date"
              autoComplete="off"
            />
          ) : (
            <Input
              id="expense-form-date"
              type="date"
              value={formData.date}
              onChange={(e) => onFormDataChange({ date: e.target.value })}
              required
              disabled={disabled}
              name="date"
              autoComplete="off"
            />
          )}
        </div>
        <div>
          <Label htmlFor={formData.isRecurring ? "expense-form-payment-day" : "expense-form-payment-date"}>{formData.isRecurring ? 'Día de Pago' : 'Fecha de Pago'}</Label>
          {formData.isRecurring ? (
            <>
              <Select
                value={formData.paymentDate ? String(Number(formData.paymentDate.split('-')[2] || 1)) : ''}
                onValueChange={(value) => {
                  onFormDataChange({ paymentDate: value.padStart(2, '0') });
                }}
                disabled={disabled}
              >
                <SelectTrigger id="expense-form-payment-day" name="payment_date" autoComplete="off">
                  <SelectValue placeholder="Selecciona el día de pago" />
                </SelectTrigger>
                <SelectContent>
                  {[...Array(31)].map((_, i) => (
                    <SelectItem key={i+1} value={String(i+1).padStart(2, '0')}>
                      {i+1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-finanz-text-secondary mt-1">
                El gasto se generará automáticamente el día seleccionado de cada mes.
              </p>
            </>
          ) : (
            isDarkMode ? (
              <DatePicker
                id="expense-form-payment-date"
                date={parseDate(formData.paymentDate || '')}
                onSelect={(date) => onFormDataChange({ paymentDate: formatDate(date) })}
                placeholder="Selecciona fecha de pago"
                disabled={disabled}
                name="payment_date"
                autoComplete="off"
              />
            ) : (
              <Input
                id="expense-form-payment-date"
                type="date"
                value={formData.paymentDate}
                onChange={(e) => onFormDataChange({ paymentDate: e.target.value })}
                disabled={disabled}
                name="payment_date"
                autoComplete="off"
              />
            )
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expense-form-currency">Moneda</Label>
          <Select
            value={formData.currency}
            onValueChange={(value: 'DOP' | 'USD') => 
              onFormDataChange({ currency: value })
            }
            disabled={disabled}
          >
            <SelectTrigger id="expense-form-currency" name="currency" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DOP">Pesos Dominicanos (RD$)</SelectItem>
              <SelectItem value="USD">Dólares (USD$)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="expense-form-amount">Monto *</Label>
          <NumericInput
            id="expense-form-amount"
            value={formData.amount || 0}
            onChange={(value) => onFormDataChange({ amount: value })}
            currency={formData.currency}
            showCurrency={true}
            required
            disabled={disabled}
            name="amount"
            autoComplete="off"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expense-form-type">Tipo</Label>
          <Select
            value={formData.type}
            onValueChange={(value: 'Fijo' | 'Variable') => 
              onFormDataChange({ type: value })
            }
            disabled={disabled}
          >
            <SelectTrigger id="expense-form-type" name="type" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Fijo">Fijo</SelectItem>
              <SelectItem value="Variable">Variable</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="expense-form-category">Categoría *</Label>
          <Select
            value={formData.categoryId}
            onValueChange={handleCategoryChange}
            disabled={disabled}
          >
            <SelectTrigger id="expense-form-category" name="category_id" autoComplete="off">
              <SelectValue placeholder="Seleccionar categoría" />
            </SelectTrigger>
            <SelectContent>
              {defaultCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expense-form-payment-method">Método de Pago</Label>
          <Select
            value={formData.paymentMethod}
            onValueChange={(value: 'cash' | 'debit-card' | 'credit-card' | 'transfer') => 
              onFormDataChange({ paymentMethod: value })
            }
            disabled={disabled}
          >
            <SelectTrigger id="expense-form-payment-method" name="payment_method" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cash">Efectivo</SelectItem>
              <SelectItem value="debit-card">Tarjeta de Débito</SelectItem>
              <SelectItem value="credit-card">Tarjeta de Crédito</SelectItem>
              <SelectItem value="transfer">Transferencia</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="expense-form-status">Estado</Label>
          <Select
            value={formData.status}
            onValueChange={(value: 'pending' | 'paid') => 
              onFormDataChange({ status: value })
            }
            disabled={disabled}
          >
            <SelectTrigger id="expense-form-status" name="status" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="paid">Pagado</SelectItem>
              <SelectItem value="pending">Pendiente</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className={`flex items-center ${isMobile ? 'space-x-1' : 'space-x-2'}`}>
        <Switch
          id="isRecurring"
          checked={formData.isRecurring ?? false}
          onCheckedChange={(checked) => onFormDataChange({ isRecurring: checked as boolean })}
          disabled={disabled}
          className={isMobile ? 'scale-75' : ''}
          name="is_recurring"
        />
        <Label 
          htmlFor="isRecurring" 
          className={isMobile ? 'text-sm' : ''}
        >
          Recurrente
        </Label>
      </div>

      <div>
        <Label htmlFor="expense-form-description">Descripción</Label>
        <Textarea
          id="expense-form-description"
          placeholder="Ingresa una descripción personalizada para este gasto (opcional)"
          value={formData.description || ''}
          onChange={(e) => onFormDataChange({ description: e.target.value })}
          disabled={disabled}
          className="min-h-[80px]"
          name="description"
          autoComplete="off"
        />
        <p className="text-xs text-finanz-text-secondary mt-1">
          💡 Escribe una descripción personalizada o déjala en blanco si lo prefieres
        </p>
      </div>
    </>
  );
});
