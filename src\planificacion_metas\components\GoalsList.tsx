import React from 'react';
import { Target } from 'lucide-react';
import { FinancialGoal, GoalContribution } from '@/types';
import { GoalCard } from './GoalCard';

interface GoalsListProps {
  goals: FinancialGoal[];
  getContributionsForGoal: (goalId: string) => GoalContribution[];
  onEdit: (goal: FinancialGoal) => void;
  onDelete: (goalId: string) => void;
  onAddContribution: (goal: FinancialGoal) => void;
  onShowHistory: (goal: FinancialGoal) => void;
}

export function GoalsList({ 
  goals, 
  getContributionsForGoal, 
  onEdit, 
  onDelete, 
  onAddContribution, 
  onShowHistory 
}: GoalsListProps) {
  if (goals.length === 0) {
    return (
      <div className="text-center py-8">
        <Target className="w-12 h-12 text-finanz-text-secondary mx-auto mb-3" />
        <p className="text-finanz-text-secondary">No hay metas en esta categoría</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {goals.map((goal) => (
        <GoalCard
          key={goal.id}
          goal={goal}
          contributions={getContributionsForGoal(goal.id)}
          onEdit={onEdit}
          onDelete={onDelete}
          onAddContribution={onAddContribution}
          onShowHistory={onShowHistory}
        />
      ))}
    </div>
  );
}
