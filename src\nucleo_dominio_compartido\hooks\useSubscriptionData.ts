
// import { useState, useEffect } from 'react'; // Unused imports
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Subscription } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export const useSubscriptionData = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  interface DBSubscription {
    user_id: string;
    name: string;
    amount: number;
    category_id?: string | null;
    category_name?: string | null;
    billing_date: string;
    is_active: boolean;
    currency: "DOP" | "USD";
    id?: string;
    created_at?: string;
    updated_at?: string;
  }

  // Fetch subscriptions from Supabase
  const { data: subscriptions = [], isLoading, error } = useQuery<Subscription[], Error>({
    queryKey: ['subscriptions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data.map((sub: DBSubscription) => ({ // Type 'sub'
        id: sub.id!, // id should exist for fetched records
        name: sub.name,
        amount: sub.amount,
        categoryId: sub.category_id || undefined, // Ensure undefined if null/missing
        categoryName: sub.category_name || undefined,
        billingDate: sub.billing_date,
        isActive: sub.is_active,
        currency: sub.currency,
        createdAt: sub.created_at!, // createdAt should exist
        updatedAt: sub.updated_at!  // updatedAt should exist
      })) as Subscription[];
    },
    enabled: !!user?.id,
  });

  // Add subscription mutation
  const addSubscriptionMutation = useMutation({
    mutationFn: async (subscription: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          name: subscription.name,
          amount: subscription.amount,
          category_id: subscription.categoryId,
          category_name: subscription.categoryName,
          billing_date: subscription.billingDate,
          is_active: subscription.isActive,
          currency: subscription.currency
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions', user?.id] });
      toast({
        title: "Éxito",
        description: "Suscripción agregada correctamente",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "No se pudo agregar la suscripción",
        variant: "destructive"
      });
      console.error('Error adding subscription:', error);
    },
  });

  // Update subscription mutation
  const updateSubscriptionMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Subscription> }) => {
      if (!user?.id) throw new Error('User not authenticated');

      const transformSubscriptionUpdatesToDb = (upds: Partial<Subscription>): Partial<DBSubscription> => {
        const dbUpdates: Partial<DBSubscription> = {};
        if (upds.name !== undefined) dbUpdates.name = upds.name;
        if (upds.amount !== undefined) dbUpdates.amount = upds.amount;
        if (upds.categoryId !== undefined) dbUpdates.category_id = upds.categoryId;
        if (upds.categoryName !== undefined) dbUpdates.category_name = upds.categoryName;
        if (upds.billingDate !== undefined) dbUpdates.billing_date = upds.billingDate;
        if (upds.isActive !== undefined) dbUpdates.is_active = upds.isActive;
        if (upds.currency !== undefined) dbUpdates.currency = upds.currency;
        return dbUpdates;
      };

      const updateData: Partial<DBSubscription> = transformSubscriptionUpdatesToDb(updates);

      if (Object.keys(updateData).length === 0) {
        // Avoid update if no actual data changed
        const existingSubscription = subscriptions.find(s => s.id === id);
        if (existingSubscription) return existingSubscription; // Or fetch from DB if more robust
        throw new Error("No changes to apply and subscription not found locally.");
      }

      const { data, error } = await supabase
        .from('subscriptions')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions', user?.id] });
      toast({
        title: "Éxito",
        description: "Suscripción actualizada correctamente",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "No se pudo actualizar la suscripción",
        variant: "destructive"
      });
      console.error('Error updating subscription:', error);
    },
  });

  // Delete subscription mutation
  const deleteSubscriptionMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('subscriptions')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions', user?.id] });
      toast({
        title: "Éxito",
        description: "Suscripción eliminada correctamente",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "No se pudo eliminar la suscripción",
        variant: "destructive"
      });
      console.error('Error deleting subscription:', error);
    },
  });

  const addSubscription = (subscription: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>) => {
    addSubscriptionMutation.mutate(subscription);
  };

  const updateSubscription = (id: string, updates: Partial<Subscription>) => {
    updateSubscriptionMutation.mutate({ id, updates });
  };

  const deleteSubscription = (id: string) => {
    deleteSubscriptionMutation.mutate(id);
  };

  const toggleSubscriptionStatus = (id: string) => {
    const subscription = subscriptions.find(sub => sub.id === id);
    if (subscription) {
      updateSubscription(id, { isActive: !subscription.isActive });
    }
  };

  return {
    subscriptions,
    isLoading,
    error,
    addSubscription,
    updateSubscription,
    deleteSubscription,
    toggleSubscriptionStatus
  };
};
