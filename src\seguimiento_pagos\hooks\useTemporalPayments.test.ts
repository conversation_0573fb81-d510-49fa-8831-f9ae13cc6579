import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useTemporalPayments } from './useTemporalPayments';
import type { TemporalPeriod } from './useTemporalNavigation';
import { usePaymentsLogic } from './usePaymentsLogic';
import { useExchangeRate } from '@/hooks/useExchangeRate';
import type { PaymentItem } from '../types/paymentTypes';

// Mocking the hooks
vi.mock('./usePaymentsLogic');
vi.mock('@/hooks/useExchangeRate');

const mockAllCategorizedPayments = {
  pending: [
    { id: 'p1', type: 'expense', name: 'Pending July', amount: 100, currency: 'DOP', dueDate: '2024-07-10', status: 'pending', referenceId: 'r_p1' },
    { id: 'p2', type: 'loan', name: 'Pending August', amount: 200, currency: 'USD', dueDate: '2024-08-05', status: 'pending', referenceId: 'r_p2' },
  ] as PaymentItem[],
  paid: [
    { id: 'pa1', type: 'subscription', name: 'Paid July', amount: 50, currency: 'DOP', dueDate: '2024-07-15', paidDate: '2024-07-14', status: 'paid', referenceId: 'r_pa1' },
    { id: 'pa2', type: 'expense', name: 'Paid August', amount: 75, currency: 'USD', dueDate: '2024-08-20', paidDate: '2024-08-19', status: 'paid', referenceId: 'r_pa2' },
  ] as PaymentItem[],
  overdue: [
    { id: 'o1', type: 'personal-debt', name: 'Overdue June', amount: 120, currency: 'DOP', dueDate: '2024-06-25', status: 'overdue', referenceId: 'r_o1' },
    { id: 'o2', type: 'credit-card', name: 'Overdue July', amount: 150, currency: 'USD', dueDate: '2024-07-01', status: 'overdue', referenceId: 'r_o2' },
  ] as PaymentItem[],
};

describe('useTemporalPayments Hook', () => {
  beforeEach(() => {
    // Reset mocks before each test
    (usePaymentsLogic as any).mockReset();
    (useExchangeRate as any).mockReset();
  });

  it('filters payments correctly for a specific month period (displayMode: month)', () => {
    (usePaymentsLogic as any).mockReturnValue({
      payments: mockAllCategorizedPayments,
      totals: {} // Base totals not directly used by useTemporalPayments's filtering logic
    });
    (useExchangeRate as any).mockReturnValue({ rate: 58.0 }); // Example exchange rate

    const julyPeriod: TemporalPeriod = {
      year: 2024,
      month: 7, // July
      startDate: '2024-07-01',
      endDate: '2024-07-31',
      displayName: 'July 2024',
      isCurrentMonth: true, // Assuming July is current for this test scenario
      isPastMonth: false,
      isFutureMonth: false,
      displayMode: 'month',
    };

    const { result } = renderHook(() => useTemporalPayments(julyPeriod));

    expect(result.current.payments.pending).toEqual([mockAllCategorizedPayments.pending[0]]); // Pending July
    expect(result.current.payments.paid).toEqual([mockAllCategorizedPayments.paid[0]]);       // Paid July
    expect(result.current.payments.overdue).toEqual([mockAllCategorizedPayments.overdue[1]]); // Overdue July

    // Test totals for July (1 USD = 58 DOP)
    // Pending: 100 DOP
    // Paid: 50 DOP
    // Overdue: 150 USD * 58 = 8700 DOP
    expect(result.current.totals.pending).toBe(100);
    expect(result.current.totals.paidThisMonth).toBe(50);
    expect(result.current.totals.overdue).toBe(150 * 58);

    // Test periodMetrics for July
    expect(result.current.periodMetrics.totalPayments).toBe(3); // 1 pending, 1 paid, 1 overdue in July
    expect(result.current.periodMetrics.completedPayments).toBe(1);
    expect(result.current.periodMetrics.pendingPayments).toBe(1);
    expect(result.current.periodMetrics.overduePayments).toBe(1);
    expect(result.current.periodMetrics.completionRate).toBeCloseTo((1 / 3) * 100);
    expect(result.current.periodMetrics.totalAmount).toBe(100 + 50 + (150 * 58));
    expect(result.current.periodMetrics.isProjection).toBe(julyPeriod.isFutureMonth); // false in this case
  });

  it('filters payments correctly for a custom date range (displayMode: range)', () => {
    (usePaymentsLogic as any).mockReturnValue({ payments: mockAllCategorizedPayments });
    (useExchangeRate as any).mockReturnValue({ rate: 58.0 });

    const customRangePeriod: TemporalPeriod = {
      year: 2024, // Year of start date
      month: 7,   // Month of start date
      startDate: '2024-07-05', // July 5th
      endDate: '2024-08-10',   // August 10th
      displayName: '05/07/2024 - 10/08/2024',
      isCurrentMonth: false,
      isPastMonth: false,
      isFutureMonth: false, // For ranges, these flags are typically false
      displayMode: 'range',
    };

    const { result } = renderHook(() => useTemporalPayments(customRangePeriod));

    // Expected in range 2024-07-05 to end of July (due to month-end clamp):
    // Pending: Pending July (07-10)
    // Paid: Paid July (07-15)
    // Overdue: (None, Overdue July was 07-01, outside range start)
    expect(result.current.payments.pending).toEqual([
      mockAllCategorizedPayments.pending[0], // Pending July
    ]);
    expect(result.current.payments.paid).toEqual([mockAllCategorizedPayments.paid[0]]); // Paid July
    expect(result.current.payments.overdue).toEqual([]);

    // Test totals for the custom range
    // Pending: 100 DOP
    // Paid: 50 DOP
    // Overdue: 0
    expect(result.current.totals.pending).toBe(100);
    expect(result.current.totals.paidThisMonth).toBe(50); // 'paidThisMonth' key is used, but it means paid in period
    expect(result.current.totals.overdue).toBe(0);

    // Test periodMetrics for the custom range
    expect(result.current.periodMetrics.totalPayments).toBe(2); // 1 pending, 1 paid
    expect(result.current.periodMetrics.completedPayments).toBe(1);
    expect(result.current.periodMetrics.pendingPayments).toBe(1);
    expect(result.current.periodMetrics.overduePayments).toBe(0);
    expect(result.current.periodMetrics.completionRate).toBeCloseTo((1 / 2) * 100);
    expect(result.current.periodMetrics.totalAmount).toBe(100 + 50 + 0);
    expect(result.current.periodMetrics.isProjection).toBe(customRangePeriod.isFutureMonth); // false
  });

  it('handles empty payments list', () => {
    (usePaymentsLogic as any).mockReturnValue({
      payments: { pending: [], paid: [], overdue: [] },
      totals: { pending: 0, overdue: 0, paidThisMonth: 0 }
    });
    (useExchangeRate as any).mockReturnValue({ rate: 1 });

    const period: TemporalPeriod = {
      year: 2024, month: 1, startDate: '2024-01-01', endDate: '2024-01-31',
      displayName: 'Jan 2024', isCurrentMonth: false, isPastMonth: true, isFutureMonth: false,
      displayMode: 'month',
    };

    const { result } = renderHook(() => useTemporalPayments(period));

    expect(result.current.payments.pending).toEqual([]);
    expect(result.current.payments.paid).toEqual([]);
    expect(result.current.payments.overdue).toEqual([]);
    expect(result.current.totals.pending).toBe(0);
    expect(result.current.totals.paidThisMonth).toBe(0);
    expect(result.current.totals.overdue).toBe(0);
    expect(result.current.periodMetrics.totalPayments).toBe(0);
    expect(result.current.periodMetrics.completionRate).toBe(0);
  });
});
