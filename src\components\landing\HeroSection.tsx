
import { logger } from "@/utils/logger";
import React from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  TrendingUp, 
  BarChart3, 
  ArrowRight,
  Users,
  Star,
  Shield,
  Server,
  CheckCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface PublicStatistics {
  total_registered_users: number;
  uptime_percentage: number;
  rating: number;
  support_hours: string;
  app_features: number;
  satisfaction_score: number;
  data_security_level: string;
  api_response_time: number;
  daily_active_sessions: number;
  app_version: string;
  server_locations: number;
  security_updates: number;
}

interface HeroSectionProps {
  statistics: PublicStatistics | null;
  loading: boolean;
  lastUpdate?: Date;
  isConnected?: boolean;
}

export function HeroSection({ statistics, loading, lastUpdate, isConnected = true }: HeroSectionProps) {
  logger.debug('HeroSection rendering with app statistics:', { statistics, loading, lastUpdate, isConnected });
  
  const { isMobile, isTablet } = useBreakpoint();

  const formatNumber = (num: number) => {
    if (typeof num !== 'number' || num === 0 || num === null || num === undefined) return '0';
    
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const stats = [
    { 
      value: loading ? null : formatNumber(statistics?.total_registered_users || 0), 
      label: "Usuarios", 
      icon: Users,
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50",
      iconColor: "text-blue-600"
    },
    { 
      value: loading ? null : `${statistics?.uptime_percentage || 99.9}%`, 
      label: "Uptime", 
      icon: CheckCircle,
      color: "from-emerald-500 to-green-500",
      bgColor: "bg-gradient-to-br from-emerald-50 to-green-50",
      iconColor: "text-emerald-600"
    },
    { 
      value: loading ? null : `${statistics?.rating || 4.9}/5`, 
      label: "Rating", 
      icon: Star,
      color: "from-purple-500 to-violet-500",
      bgColor: "bg-gradient-to-br from-purple-50 to-violet-50",
      iconColor: "text-purple-600"
    },
    { 
      value: loading ? null : `${statistics?.api_response_time || 35}ms`, 
      label: "Speed", 
      icon: TrendingUp,
      color: "from-amber-500 to-orange-500",
      bgColor: "bg-gradient-to-br from-amber-50 to-orange-50",
      iconColor: "text-amber-600"
    }
  ];

  return (
    <section className="relative overflow-hidden">
      {/* Enhanced background with multiple gradients */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-emerald-400/30 to-blue-400/30 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
      </div>
      
      <div className={`
        container mx-auto relative z-10 text-center
        ${isMobile ? 'px-4 pt-16 pb-12' : isTablet ? 'px-4 pt-20 pb-14' : 'px-4 pt-24 pb-16'}
      `}>
        <div className="max-w-5xl mx-auto">
          {/* Status badge */}
          <div className={`
            inline-flex items-center space-x-3 bg-white/80 backdrop-blur-lg rounded-full border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 mb-6
            ${isMobile ? 'px-4 py-2 mb-4' : 'px-6 py-3 mb-8'}
          `}>
            <BarChart3 className="w-4 h-4 text-blue-600" />
            <span className={`
              font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent
              ${isMobile ? 'text-xs' : 'text-sm'}
            `}>
              Plataforma Financiera Profesional
            </span>
          </div>
          
          {/* Main headline - Optimizado para móvil */}
          <h1 className={`
            font-bold text-gray-900 leading-tight tracking-tight mb-4
            ${isMobile ? 'text-2xl' : isTablet ? 'text-3xl' : 'text-4xl md:text-5xl lg:text-6xl mb-6'}
          `}>
            Gestiona tus{' '}
            <span className="relative">
              <span className="bg-gradient-to-r from-blue-600 via-blue-700 to-purple-700 bg-clip-text text-transparent">
                Finanzas
              </span>
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-xl rounded-lg -z-10"></div>
            </span>
            <br />
            de forma{' '}
            <span className="relative">
              <span className="bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 bg-clip-text text-transparent">
                Profesional
              </span>
              <div className="absolute -inset-2 bg-gradient-to-r from-emerald-600/20 to-blue-600/20 blur-xl rounded-lg -z-10"></div>
            </span>
          </h1>
          
          {/* Subtitle - Optimizado para móvil */}
          <p className={`
            text-gray-600 leading-relaxed font-medium max-w-3xl mx-auto mb-8
            ${isMobile ? 'text-sm mb-6' : isTablet ? 'text-base mb-8' : 'text-lg md:text-xl mb-10'}
          `}>
            La plataforma más <span className="font-semibold text-gray-800">completa y eficiente</span> para gestionar 
            tus finanzas personales y alcanzar tus objetivos económicos.
          </p>
          
          {/* CTA buttons - Optimizados para móvil */}
          <div className={`
            flex justify-center mb-12
            ${isMobile ? 'flex-col gap-3 mb-8' : 'flex-col sm:flex-row gap-4 mb-16'}
          `}>
            <Link to="/auth">
              <Button 
                size={isMobile ? "default" : "lg"} 
                className={`
                  group bg-gradient-to-r from-blue-600 via-blue-700 to-purple-700 hover:from-blue-700 hover:via-blue-800 hover:to-purple-800 shadow-2xl hover:shadow-3xl transition-all duration-500 text-white border-0 transform hover:scale-105 rounded-xl
                  ${isMobile ? 'w-full py-3 text-base font-bold px-8' : 'px-12 py-4 text-lg font-bold'}
                `}
              >
                <TrendingUp className={`
                  mr-2 group-hover:rotate-12 transition-transform duration-300
                  ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                `} />
                Comenzar Ahora
                <ArrowRight className={`
                  ml-2 group-hover:translate-x-1 transition-transform duration-300
                  ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                `} />
              </Button>
            </Link>
            <Link to="/app/dashboard">
              <Button 
                size={isMobile ? "default" : "lg"} 
                variant="outline" 
                className={`
                  group border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 rounded-xl bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transform hover:scale-105
                  ${isMobile ? 'w-full py-3 text-base font-bold px-8' : 'px-12 py-4 text-lg font-bold'}
                `}
              >
                <Server className={`
                  mr-2 group-hover:scale-110 transition-transform duration-300
                  ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                `} />
                Ver Dashboard
              </Button>
            </Link>
          </div>

          {/* Statistics grid - Compacto para móvil */}
          <div className={`
            grid max-w-5xl mx-auto
            ${isMobile ? 'grid-cols-2 gap-3' : isTablet ? 'grid-cols-2 gap-4' : 'grid-cols-2 md:grid-cols-4 gap-6'}
          `}>
            {stats.map((stat) => (
              <div key={stat.label} className="group relative">
                <div className={`
                  bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 transform
                  ${isMobile ? 'p-3' : isTablet ? 'p-4' : 'p-6'}
                `}>
                  <div className="flex items-center justify-center mb-3">
                    <div className={`
                      rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300
                      ${stat.bgColor}
                      ${isMobile ? 'w-8 h-8' : isTablet ? 'w-10 h-10' : 'w-12 h-12'}
                    `}>
                      <stat.icon className={`
                        ${stat.iconColor}
                        ${isMobile ? 'w-4 h-4' : isTablet ? 'w-5 h-5' : 'w-6 h-6'}
                      `} />
                    </div>
                  </div>
                  <div className={`
                    font-black text-gray-900 mb-1
                    ${isMobile ? 'text-lg' : isTablet ? 'text-xl' : 'text-2xl mb-2'}
                  `}>
                    {loading ? (
                      <Skeleton className={`
                        mx-auto bg-gray-200
                        ${isMobile ? 'h-6 w-12' : 'h-8 w-16'}
                      `} />
                    ) : (
                      <span className="transition-all duration-500 ease-in-out bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        {stat.value || '0'}
                      </span>
                    )}
                  </div>
                  <div className={`
                    text-gray-600 font-semibold tracking-wide uppercase
                    ${isMobile ? 'text-xs' : 'text-xs'}
                  `}>
                    {stat.label}
                  </div>
                </div>
                
                {/* Hover effect glow */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-xl -z-10`}></div>
              </div>
            ))}
          </div>

          {/* Trust indicators - Compacto para móvil */}
          <div className={`
            border-t border-white/30
            ${isMobile ? 'mt-8 pt-4' : 'mt-12 pt-6'}
          `}>
            <p className={`
              text-gray-500 font-medium mb-4
              ${isMobile ? 'text-xs mb-3' : 'text-sm mb-6'}
            `}>
              Tecnología confiable y segura
            </p>
            <div className={`
              opacity-80
              ${isMobile ? 'grid grid-cols-1 gap-3' : 'grid grid-cols-2 md:grid-cols-3 gap-6'}
            `}>
              <div className="flex flex-col items-center space-y-2">
                <Shield className={`text-green-600 ${isMobile ? 'w-4 h-4' : 'w-6 h-6'}`} />
                <span className={`
                  font-semibold text-gray-700
                  ${isMobile ? 'text-xs' : 'text-sm'}
                `}>
                  Seguridad {statistics?.data_security_level || 'Bancaria'}
                </span>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <Server className={`text-blue-600 ${isMobile ? 'w-4 h-4' : 'w-6 h-6'}`} />
                <span className={`
                  font-semibold text-gray-700
                  ${isMobile ? 'text-xs' : 'text-sm'}
                `}>
                  Soporte {statistics?.support_hours || '24/7'}
                </span>
              </div>
              <div className={`
                flex flex-col items-center space-y-2
                ${isMobile ? 'col-span-1' : ''}
              `}>
                <CheckCircle className={`text-emerald-600 ${isMobile ? 'w-4 h-4' : 'w-6 h-6'}`} />
                <span className={`
                  font-semibold text-gray-700
                  ${isMobile ? 'text-xs' : 'text-sm'}
                `}>
                  Versión {statistics?.app_version || '2.1.5'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
