import React from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PaymentsList } from './lists/PaymentsList';
import { EmptyState } from './states/EmptyState';
import { AlertTriangle, CheckCircle, Calendar } from 'lucide-react'; // Icons for EmptyState and Overdue warning
import type { PaymentItem } from '../types/paymentTypes';
import type { CurrentPeriod } from '../hooks/useTemporalNavigation'; // Assuming type location

interface PaymentTabsViewProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  getPaymentsForTab: (tab: string) => PaymentItem[];
  onPaymentClick: (payment: PaymentItem) => void;
  onUnmarkAsPaid: (payment: PaymentItem) => void;
  currentPeriod: CurrentPeriod; // For isProjection prop in PaymentsList
  hasActiveFilters: boolean; // For EmptyState messages
}

export function PaymentTabsView({
  activeTab,
  onTabChange,
  getPaymentsForTab,
  onPaymentClick,
  onUnmarkAsPaid,
  currentPeriod,
  hasActiveFilters,
}: PaymentTabsViewProps) {
  const pendingPayments = getPaymentsForTab('pending');
  const overduePayments = getPaymentsForTab('overdue');
  const paidPayments = getPaymentsForTab('paid');

  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="pending" className="relative">
          Pendientes ({pendingPayments.length})
          {pendingPayments.length > 0 && (
            <span className="absolute -top-1 -right-1 w-2 h-2 bg-finanz-warning rounded-full"></span>
          )}
        </TabsTrigger>
        <TabsTrigger value="overdue" className="relative">
          Vencidos ({overduePayments.length})
          {overduePayments.length > 0 && (
            <span className="absolute -top-1 -right-1 w-2 h-2 bg-finanz-danger rounded-full"></span>
          )}
        </TabsTrigger>
        <TabsTrigger value="paid">
          Completados ({paidPayments.length})
        </TabsTrigger>
      </TabsList>

      <TabsContent value="pending">
        {pendingPayments.length === 0 ? (
          <EmptyState
            icon={CheckCircle}
            title={hasActiveFilters ? "No hay pagos que coincidan" : "No hay pagos pendientes"}
            subtitle={hasActiveFilters ? "Ajusta los filtros para ver más resultados" : currentPeriod.isFutureMonth ? "No hay proyecciones para este período" : "¡Excelente trabajo manteniendo tus pagos al día!"}
          />
        ) : (
          <PaymentsList
            paymentsList={pendingPayments}
            onMarkAsPaid={onPaymentClick}
            isProjection={currentPeriod.isFutureMonth}
            useEnhancedItem={true}
          />
        )}
      </TabsContent>

      <TabsContent value="overdue">
        {overduePayments.length === 0 ? (
          <EmptyState
            icon={CheckCircle}
            title={hasActiveFilters ? "No hay pagos vencidos que coincidan" : "No hay pagos vencidos"}
            subtitle={hasActiveFilters ? "Ajusta los filtros para ver más resultados" : "Mantén este buen ritmo de pagos"}
          />
        ) : (
          <>
            <div className="bg-finanz-danger/10 border border-finanz-danger/20 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2 text-finanz-danger">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-medium">Atención: Tienes pagos vencidos</span>
              </div>
              <p className="text-sm text-finanz-text-secondary mt-1">
                Es importante ponerse al día con estos pagos para evitar cargos adicionales.
              </p>
            </div>
            <PaymentsList
              paymentsList={overduePayments}
              onMarkAsPaid={onPaymentClick}
              isProjection={currentPeriod.isFutureMonth}
              useEnhancedItem={true}
            />
          </>
        )}
      </TabsContent>

      <TabsContent value="paid">
        {paidPayments.length === 0 ? (
          <EmptyState
            icon={Calendar}
            title={hasActiveFilters ? "No hay pagos completados que coincidan" : "No hay pagos completados registrados"}
            subtitle={hasActiveFilters ? "Ajusta los filtros para ver más resultados" : "Los pagos marcados como completados aparecerán aquí"}
          />
        ) : (
          <PaymentsList
            paymentsList={paidPayments}
            onUnmarkAsPaid={onUnmarkAsPaid}
            isProjection={false} // Paid payments are never projections
            useEnhancedItem={true}
          />
        )}
      </TabsContent>
    </Tabs>
  );
}
