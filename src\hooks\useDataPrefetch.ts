
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { QUERY_KEYS } from '@/constants/queryKeys';

// Prefetch de datos para navegación rápida
export const useDataPrefetch = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  useEffect(() => {
    if (!user?.id) return;

    // Prefetch incomes
    queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.INCOMES, user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('incomes')
            .select('*')
            .eq('user_id', user.id)
            .order('month', { ascending: false });
          return data || [];
        },
        staleTime: 1000 * 60 * 10,
      });

      // Prefetch expenses
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.EXPENSES, user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('expenses')
            .select('*')
            .eq('user_id', user.id)
            .order('date', { ascending: false });
          return data || [];
        },
        staleTime: 1000 * 60 * 10,
      });

      // Prefetch debts
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.DEBTS, user.id],
        queryFn: async () => {
          const [creditCards, loans, personalDebts] = await Promise.all([
            supabase.from('credit_cards').select('*').eq('user_id', user.id),
            supabase.from('loans').select('*').eq('user_id', user.id),
            supabase.from('personal_debts').select('*').eq('user_id', user.id),
          ]);
          return {
            creditCards: creditCards.data || [],
            loans: loans.data || [],
            personalDebts: personalDebts.data || [],
          };
        },
        staleTime: 1000 * 60 * 10,
      });

      // Prefetch subscriptions
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.SUBSCRIPTIONS, user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
          return data || [];
        },
        staleTime: 1000 * 60 * 10,
      });

      // Prefetch reimbursements
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.REIMBURSEMENTS, user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('reimbursements')
            .select('*')
            .eq('user_id', user.id)
            .order('date', { ascending: false });
          return data || [];
        },
        staleTime: 1000 * 60 * 10,
      });

      // Prefetch financial goals
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.FINANCIAL_GOALS_ALIAS, user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('financial_goals')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
          return data || [];
        },
        staleTime: 1000 * 60 * 10,
      });

      // Prefetch payment records
      queryClient.prefetchQuery({
        queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user.id],
        queryFn: async () => {
          const { data } = await supabase
            .from('payment_records')
            .select('*')
            .eq('user_id', user.id)
            .order('due_date', { ascending: true });
          return data || [];
        },
        staleTime: 1000 * 60 * 10,
      });
  }, [user?.id, queryClient]);
};
