import { logger } from "@/utils/logger";

import { useState, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { auditLog } from '@/utils/securityUtils';

interface SecurityEvent {
  type: 'login' | 'logout' | 'data_access' | 'form_submission' | 'suspicious_activity';
  message: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: Date;
  userId?: string;
  metadata?: Record<string, any>;
}

interface SecurityHealthCheck {
  isHealthy: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  issues: string[];
}

export const useEnhancedSecurity = () => {
  const { user, refreshSession } = useAuth();
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [suspiciousActivity, setSuspiciousActivity] = useState(false);
  const [sessionWarning, setSessionWarning] = useState(false);
  const lastActivity = useRef<Date>(new Date());

  const logEnhancedSecurityEvent = useCallback((
    type: SecurityEvent['type'],
    message: string,
    severity: SecurityEvent['severity'],
    metadata?: Record<string, any>
  ) => {
    const event: SecurityEvent = {
      type,
      message,
      severity,
      timestamp: new Date(),
      userId: user?.id,
      metadata
    };

    setSecurityEvents(prev => [...prev.slice(-49), event]); // Keep last 50 events

    // Log to audit system
    auditLog(`security_${type}`, {
      message,
      severity,
      metadata
    }, user?.id);

    // Trigger alerts for high severity events
    if (severity === 'high') {
      setSuspiciousActivity(true);
    }

    logger.debug(`[SECURITY ${severity.toUpperCase()}] ${type}: ${message}`, metadata);
  }, [user?.id]);

  const performSecurityHealthCheck = useCallback((): SecurityHealthCheck => {
    const issues: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Check for recent high-severity events
    const recentHighSeverityEvents = securityEvents.filter(
      event => event.severity === 'high' && 
      Date.now() - event.timestamp.getTime() < 30 * 60 * 1000 // Last 30 minutes
    );

    if (recentHighSeverityEvents.length >= 3) {
      issues.push('Múltiples eventos de alta severidad detectados');
      riskLevel = 'high';
    }

    // Check for failed login attempts
    const failedLogins = securityEvents.filter(
      event => event.type === 'login' && 
      event.message.includes('failed') &&
      Date.now() - event.timestamp.getTime() < 15 * 60 * 1000 // Last 15 minutes
    );

    if (failedLogins.length >= 3) {
      issues.push('Múltiples intentos de login fallidos');
      riskLevel = riskLevel === 'high' ? 'high' : 'medium';
    }

    // Check session age
    const sessionAge = Date.now() - lastActivity.current.getTime();
    if (sessionAge > 30 * 60 * 1000) { // 30 minutes
      issues.push('Sesión inactiva por tiempo prolongado');
      if (riskLevel === 'low') riskLevel = 'medium';
    }

    // Check for suspicious data access patterns
    const dataAccessEvents = securityEvents.filter(
      event => event.type === 'data_access' &&
      Date.now() - event.timestamp.getTime() < 10 * 60 * 1000 // Last 10 minutes
    );

    if (dataAccessEvents.length > 20) {
      issues.push('Patrón de acceso a datos sospechoso');
      riskLevel = 'high';
    }

    return {
      isHealthy: issues.length === 0,
      riskLevel,
      issues
    };
  }, [securityEvents]);

  const clearSecurityWarning = useCallback(() => {
    setSuspiciousActivity(false);
    setSessionWarning(false);
    logEnhancedSecurityEvent('suspicious_activity', 'Security warning cleared by user', 'low');
  }, [logEnhancedSecurityEvent]);

  const updateActivity = useCallback(() => {
    lastActivity.current = new Date();
    localStorage.setItem('last_activity', lastActivity.current.getTime().toString());
  }, []);

  const checkSessionHealth = useCallback(() => {
    const now = new Date();
    const timeSinceActivity = now.getTime() - lastActivity.current.getTime();
    
    // Warn if session is close to expiring (25 minutes of inactivity)
    if (timeSinceActivity > 25 * 60 * 1000 && timeSinceActivity < 30 * 60 * 1000) {
      setSessionWarning(true);
      logEnhancedSecurityEvent('data_access', 'Session approaching timeout', 'medium');
    }
  }, [logEnhancedSecurityEvent]);

  return {
    securityEvents,
    suspiciousActivity,
    sessionWarning,
    logEnhancedSecurityEvent,
    performSecurityHealthCheck,
    clearSecurityWarning,
    refreshSession,
    updateActivity,
    checkSessionHealth
  };
};
