
import React from 'react';
import { Button } from '@/components/ui/button';
import { CreditCard, Edit, Trash2 } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Expense } from '@/types';

interface ExpenseItemProps {
  expense: Expense;
  onEdit: () => void;
  onDelete: () => void;
}

export const ExpenseItem = React.memo(function ExpenseItem({ expense, onEdit, onDelete }: ExpenseItemProps) {
  const getPaymentMethodLabel = (paymentMethod: string) => {
    switch (paymentMethod) {
      case 'cash':
        return 'Efectivo';
      case 'debit-card':
        return 'Tarjeta de Débito';
      case 'credit-card':
        return 'Tarjeta de Crédito';
      case 'transfer':
        return 'Transferencia';
      default:
        return paymentMethod;
    }
  };

  // Función para manejar descripciones vacías o nulas de manera elegante
  const getDisplayDescription = (description: string | null | undefined) => {
    if (!description || description.trim() === '') {
      return `Gasto de ${expense.categoryName}`;
    }
    return description;
  };

  // Función para formar la línea principal (fecha • descripción) o solo descripción si es recurrente
  const getPrimaryText = () => {
    const desc = getDisplayDescription(expense.description);
    // Si es recurrente, no mostrar la fecha original (puede confundir al usuario)
    if (expense.isRecurring) {
      return desc;
    }
    const formatted = new Date(expense.date).toLocaleDateString('es-ES');
    return `${formatted} • ${desc}`;
  };

  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between md:space-y-0 space-y-3">
      <div className="flex items-center space-x-4">
        <div className={`p-3 rounded-lg ${
          expense.type === 'Fijo' ? 'bg-finanz-purple/10' : 'bg-finanz-warning/10'
        }`}>
          <CreditCard className={`w-5 h-5 ${
            expense.type === 'Fijo' ? 'text-finanz-purple' : 'text-finanz-warning'
          }`} />
        </div>
        <div>
          <p className="font-medium text-lg">{expense.categoryName}</p>
          <p className="text-sm text-finanz-text-secondary">
            {getPrimaryText()}
          </p>
          {expense.paymentDate && (
            <p className="text-xs text-finanz-text-secondary">
              {expense.isRecurring
                ? `Día de pago: ${(() => {
                    // paymentDate puede ser 'DD' o 'YYYY-MM-DD'
                    if (expense.paymentDate.includes('-')) {
                      try {
                        return new Date(expense.paymentDate).getDate();
                      } catch {
                        return expense.paymentDate;
                      }
                    }
                    return Number(expense.paymentDate).toString();
                  })()} de cada mes`
                : `Fecha de pago: ${new Date(expense.paymentDate).toLocaleDateString('es-ES')}`}
             </p>
          )}
          <div className="flex items-center space-x-2 mt-1">
            <span className={`text-xs px-2 py-1 rounded-full ${
              expense.type === 'Fijo' 
                ? 'bg-finanz-purple/10 text-finanz-purple' 
                : 'bg-finanz-warning/10 text-finanz-warning'
            }`}>
              {expense.type}
            </span>
            <span className={`text-xs px-2 py-1 rounded-full ${
              expense.status === 'paid'
                ? 'bg-finanz-success/10 text-finanz-success'
                : 'bg-finanz-warning/10 text-finanz-warning'
            }`}>
              {expense.status === 'paid' ? 'Pagado' : 'Pendiente'}
            </span>
            {expense.isRecurring && (
              <span className="text-xs px-2 py-1 rounded-full bg-finanz-primary/10 text-finanz-primary">
                Recurrente
              </span>
            )}
          </div>
        </div>
      </div>
              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-3 w-full md:w-auto">
        <div className="text-left md:text-right w-full md:w-auto">
          <p className="font-semibold text-lg text-finanz-danger">
            {formatCurrency(expense.amount, expense.currency)}
          </p>
          <p className="text-xs text-finanz-text-secondary">
            {getPaymentMethodLabel(expense.paymentMethod)}
          </p>
        </div>
        <div className="flex space-x-1">
          <Button
            onClick={onEdit}
            variant="outline"
            size="sm"
            className="p-2"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            onClick={onDelete}
            variant="outline"
            size="sm"
            className="p-2 text-finanz-danger hover:text-finanz-danger"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
});
