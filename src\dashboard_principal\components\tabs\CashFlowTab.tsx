
import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { CashFlowAnalysis } from '../CashFlowAnalysis';
import { ComprehensivePaymentsView } from '../ComprehensivePaymentsView';
import { FinancialProjections } from '../FinancialProjections';
import { useDashboardData } from '../../hooks/useDashboardData';

export const CashFlowTab: React.FC = () => {
  const { netIncome, totalMonthlyPayments, savingsRate } = useDashboardData();

  return (
    <div className="space-y-6">
      {/* Análisis de flujo de caja */}
      <CashFlowAnalysis />

      {/* Vista comprehensiva de pagos */}
      <ComprehensivePaymentsView />

      {/* Proyecciones financieras con lazy loading */}
      <Suspense fallback={<Skeleton className="h-96 w-full" />}>
        <FinancialProjections 
          netIncome={netIncome}
          totalExpenses={totalMonthlyPayments}
          savingsRate={savingsRate}
        />
      </Suspense>
    </div>
  );
};
