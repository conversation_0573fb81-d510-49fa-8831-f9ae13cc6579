
-- OPTIMIZACIÓN DE RENDIMIENTO Y SEGURIDAD PARA METAS FINANCIERAS

-- 1. Optimizar políticas de seguridad a nivel de fila (RLS)
-- Se eliminan las políticas existentes para recrearlas de forma optimizada.
DROP POLICY IF EXISTS "Users can view their own contributions" ON public.goal_contributions;
DROP POLICY IF EXISTS "Users can insert their own contributions" ON public.goal_contributions;
DROP POLICY IF EXISTS "Users can update their own contributions" ON public.goal_contributions;
DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.goal_contributions;

-- Se recrean las políticas usando (SELECT auth.uid()) para mejorar el rendimiento,
-- evitando que la función se ejecute por cada fila.
CREATE POLICY "Users can view their own contributions"
ON public.goal_contributions FOR SELECT
USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own contributions"
ON public.goal_contributions FOR INSERT
WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own contributions"
ON public.goal_contributions FOR UPDATE
USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own contributions"
ON public.goal_contributions FOR DELETE
USING ((SELECT auth.uid()) = user_id);


-- 2. Mejorar la seguridad de la función de contribuciones
-- Se actualiza la función para definir un search_path explícito,
-- lo que previene posibles vulnerabilidades de seguridad.
CREATE OR REPLACE FUNCTION public.add_goal_contribution(
    p_goal_id UUID,
    p_user_id UUID,
    p_amount NUMERIC,
    p_note TEXT
)
RETURNS VOID AS $$
BEGIN
    -- Insertar la nueva contribución
    INSERT INTO public.goal_contributions(goal_id, user_id, amount, note, date)
    VALUES (p_goal_id, p_user_id, p_amount, p_note, now());

    -- Actualizar el monto actual en la meta financiera
    UPDATE public.financial_goals
    SET current_amount = current_amount + p_amount,
        updated_at = now()
    WHERE id = p_goal_id AND user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = 'public';
