
import React, { useState, useCallback } from 'react'; // Added useCallback
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Unused
// import { Button } from '@/components/ui/button'; // Unused
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea'; // Textarea is used
// import { Separator } from '@/components/ui/separator'; // Unused
import { /*Calculator,*/ AlertTriangle, DollarSign } from 'lucide-react'; // Calculator unused
import { formatCurrency } from '@/components/ui/numeric-input';
import { PaymentItem } from '../types/paymentTypes';

interface PersonalDebtPaymentDetails {
  totalAmount: number;
  interestAmount: number;
  principalAmount: number;
  notes: string | null;
  paymentType: 'minimum' | 'custom' | 'interest-only';
}

interface PersonalDebtPaymentOptionsProps {
  payment: PaymentItem;
  onPaymentChange: (paymentData: PersonalDebtPaymentDetails) => void;
}

export const PersonalDebtPaymentOptions = ({ 
  payment, 
  onPaymentChange
}: PersonalDebtPaymentOptionsProps) => {
  const [paymentType, setPaymentType] = useState<'minimum' | 'custom' | 'interest-only'>('minimum');
  const [customAmount, setCustomAmount] = useState(payment.amount);
  const [interestAmount, setInterestAmount] = useState(0);
  const [principalAmount, setPrincipalAmount] = useState(payment.amount);
  const [notes, setNotes] = useState('');

  const handleAmountChange = (newAmount: number) => {
    setCustomAmount(newAmount);
    if (paymentType === 'custom') {
      // Calcular automáticamente el split interés/principal
      const estimatedInterest = Math.min(newAmount * 0.1, interestAmount || newAmount * 0.1);
      setInterestAmount(estimatedInterest);
      setPrincipalAmount(Math.max(0, newAmount - estimatedInterest));
    }
    updatePaymentData();
  };

  const handleInterestOnlyToggle = () => {
    if (paymentType === 'interest-only') {
      setInterestAmount(payment.amount);
      setPrincipalAmount(0);
    } else {
      setInterestAmount(0);
      setPrincipalAmount(customAmount);
    }
    updatePaymentData();
  };

  const updatePaymentData = useCallback(() => {
    const currentPaymentData: PersonalDebtPaymentDetails = {
      totalAmount: paymentType === 'minimum' ? payment.amount : customAmount,
      interestAmount: paymentType === 'interest-only' ? customAmount : interestAmount,
      principalAmount: paymentType === 'interest-only' ? 0 : principalAmount,
      notes: notes.trim() || null,
      paymentType
    };
    onPaymentChange(currentPaymentData);
  }, [paymentType, customAmount, interestAmount, principalAmount, notes, onPaymentChange, payment.amount]);

  React.useEffect(() => {
    updatePaymentData();
  }, [updatePaymentData]);

  return (
    <div className="space-y-4">
      {/* Información de la deuda */}
      <div className="bg-finanz-background rounded-lg p-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-finanz-text-secondary">Monto sugerido:</span>
          <span className="font-semibold">{formatCurrency(payment.amount)}</span>
        </div>
      </div>

      {/* Opciones de pago */}
      <div className="space-y-3">
        <span className="text-base font-medium text-finanz-text-primary">Tipo de Pago</span>
        
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="radio"
              id="minimum"
              name="paymentType"
              checked={paymentType === 'minimum'}
              onChange={() => setPaymentType('minimum')}
              className="w-4 h-4"
              autoComplete="off"
            />
            <Label htmlFor="minimum" className="flex-1">
              Pago Mínimo ({formatCurrency(payment.amount)})
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="radio"
              id="custom"
              name="paymentType"
              checked={paymentType === 'custom'}
              onChange={() => setPaymentType('custom')}
              className="w-4 h-4"
              autoComplete="off"
            />
            <Label htmlFor="custom" className="flex-1">
              Monto Personalizado
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="radio"
              id="interest-only"
              name="paymentType"
              checked={paymentType === 'interest-only'}
              onChange={() => {
                setPaymentType('interest-only');
                handleInterestOnlyToggle();
              }}
              className="w-4 h-4"
              autoComplete="off"
            />
            <Label htmlFor="interest-only" className="flex-1 flex items-center gap-2">
              Solo Intereses
              <AlertTriangle className="w-4 h-4 text-finanz-warning" />
            </Label>
          </div>
        </div>
      </div>

      {/* Monto personalizado */}
      {paymentType === 'custom' && (
        <div className="space-y-2">
          <Label htmlFor="customAmount">Monto a Pagar</Label>
          <Input
            id="customAmount"
            type="number"
            value={customAmount}
            onChange={(e) => handleAmountChange(Number(e.target.value))}
            min={0}
            step={0.01}
            name="custom_amount"
            autoComplete="off"
          />
        </div>
      )}

      {/* Desglose de pago personalizado */}
      {(paymentType === 'custom' || paymentType === 'interest-only') && (
        <div className="bg-finanz-background rounded-lg p-3 space-y-2">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="w-4 h-4" />
            <span className="font-medium">Desglose del Pago</span>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="interestAmount" className="text-xs">Intereses</Label>
              <Input
                id="interestAmount"
                type="number"
                value={interestAmount}
                onChange={(e) => setInterestAmount(Number(e.target.value))}
                min={0}
                step={0.01}
                disabled={paymentType === 'interest-only'}
                name="interest_amount"
                autoComplete="off"
              />
            </div>
            <div>
              <Label htmlFor="principalAmount" className="text-xs">Capital</Label>
              <Input
                id="principalAmount"
                type="number"
                value={principalAmount}
                onChange={(e) => setPrincipalAmount(Number(e.target.value))}
                min={0}
                step={0.01}
                disabled={paymentType === 'interest-only'}
                name="principal_amount"
                autoComplete="off"
              />
            </div>
          </div>

          {paymentType === 'interest-only' && (
            <div className="bg-finanz-warning/10 border border-finanz-warning/20 rounded p-2 mt-2">
              <p className="text-xs text-finanz-warning">
                ⚠️ Este pago solo cubrirá intereses. El balance principal no se reducirá.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Notas */}
      <div className="space-y-2">
        <Label htmlFor="notes">Notas (Opcional)</Label>
        <Textarea
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Agregar comentarios sobre este pago..."
          rows={2}
          name="notes"
          autoComplete="off"
        />
      </div>
    </div>
  );
};
