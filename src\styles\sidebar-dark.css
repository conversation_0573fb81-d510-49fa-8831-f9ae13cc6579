
@layer base {
  /* Light mode sidebar styles */
  .sidebar {
    @apply bg-white border-gray-200;
  }

  [data-sidebar] {
    @apply bg-white;
  }

  [data-sidebar-header] {
    @apply bg-white border-b border-gray-200;
  }

  [data-sidebar-content] {
    @apply bg-white;
  }

  [data-sidebar-footer] {
    @apply bg-white border-t border-gray-200;
  }

  [data-sidebar-menu-button] {
    @apply text-gray-700 hover:text-gray-900 hover:bg-gray-100;
  }

  [data-sidebar-menu-button][data-active="true"] {
    @apply text-gray-900 bg-gray-100;
  }

  [data-sidebar-group-label] {
    @apply text-gray-500;
  }

  .sidebar a {
    @apply text-gray-700 hover:text-gray-900;
  }

  .sidebar a.active {
    @apply text-gray-900 bg-gray-100;
  }

  /* Dark mode sidebar styles */
  .dark .sidebar {
    @apply bg-black border-neutral-700;
  }

  .dark [data-sidebar] {
    @apply bg-black;
  }

  .dark [data-sidebar-header] {
    @apply bg-black border-b border-neutral-700;
  }

  .dark [data-sidebar-content] {
    @apply bg-black;
  }

  .dark [data-sidebar-footer] {
    @apply bg-black border-t border-neutral-700;
  }

  .dark [data-sidebar-menu-button] {
    @apply text-neutral-200 hover:text-white hover:bg-neutral-800;
  }

  .dark [data-sidebar-menu-button][data-active="true"] {
    @apply text-white bg-neutral-800;
  }

  .dark [data-sidebar-group-label] {
    @apply text-neutral-400;
  }

  .dark .sidebar a {
    @apply text-neutral-200 hover:text-white;
  }

  .dark .sidebar a.active {
    @apply text-white bg-neutral-800;
  }
}
