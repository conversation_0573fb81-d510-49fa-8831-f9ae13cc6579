
@layer base {
  /* Optimized Light mode form styles */
  input {
    background-color: white;
    border: 1px solid rgb(209 213 219);
    color: rgb(17 24 39);
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  input::placeholder {
    color: rgb(107 114 128);
    font-weight: 400;
  }

  input:focus {
    border-color: rgb(59 130 246);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
  }

  input:hover:not(:focus) {
    border-color: rgb(156 163 175);
  }

  /* Date input specific styles for light mode */
  input[type="date"] {
    position: relative;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23374151' viewBox='0 0 16 16'%3e%3cpath d='M8 1a.5.5 0 0 1 .5.5V3h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H4.5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3V1.5A.5.5 0 0 1 8 1zM4.5 4A1 1 0 0 0 3.5 5v1h9V5a1 1 0 0 0-1-1h-7zM3.5 7v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V7h-9z'/%3e%3c/svg%3e");
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    opacity: 1;
  }

  textarea {
    background-color: white;
    border: 1px solid rgb(209 213 219);
    color: rgb(17 24 39);
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  textarea::placeholder {
    color: rgb(107 114 128);
    font-weight: 400;
  }

  textarea:focus {
    border-color: rgb(59 130 246);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
  }

  textarea:hover:not(:focus) {
    border-color: rgb(156 163 175);
  }

  select {
    background-color: white;
    border: 1px solid rgb(209 213 219);
    color: rgb(17 24 39);
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  select:focus {
    border-color: rgb(59 130 246);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
  }

  select:hover:not(:focus) {
    border-color: rgb(156 163 175);
  }

  label {
    color: rgb(55 65 81);
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  /* Optimized Dark mode form styles */
  .dark input {
    background-color: rgb(31 41 55);
    border: 1px solid rgb(75 85 99);
    color: white;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  .dark input::placeholder {
    color: rgb(156 163 175);
    font-weight: 400;
  }

  .dark input:focus {
    border-color: rgb(96 165 250);
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    outline: none;
  }

  .dark input:hover:not(:focus) {
    border-color: rgb(107 114 128);
  }

  /* Date input specific styles for dark mode */
  .dark input[type="date"] {
    color-scheme: dark;
    position: relative;
  }

  .dark input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23e5e7eb' viewBox='0 0 16 16'%3e%3cpath d='M8 1a.5.5 0 0 1 .5.5V3h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H4.5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3V1.5A.5.5 0 0 1 8 1zM4.5 4A1 1 0 0 0 3.5 5v1h9V5a1 1 0 0 0-1-1h-7zM3.5 7v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V7h-9z'/%3e%3c/svg%3e");
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    opacity: 1;
    filter: brightness(1.2) contrast(1.1);
  }

  .dark input[type="date"]::-webkit-calendar-picker-indicator:hover {
    filter: brightness(1.4) contrast(1.2);
  }

  /* Additional fallback for better visibility */
  .dark input[type="date"]::-webkit-inner-spin-button,
  .dark input[type="date"]::-webkit-clear-button {
    filter: invert(1) brightness(1.2);
  }

  .dark textarea {
    background-color: rgb(31 41 55);
    border: 1px solid rgb(75 85 99);
    color: white;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  .dark textarea::placeholder {
    color: rgb(156 163 175);
    font-weight: 400;
  }

  .dark textarea:focus {
    border-color: rgb(96 165 250);
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    outline: none;
  }

  .dark textarea:hover:not(:focus) {
    border-color: rgb(107 114 128);
  }

  .dark select {
    background-color: rgb(31 41 55);
    border: 1px solid rgb(75 85 99);
    color: white;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  .dark select:focus {
    border-color: rgb(96 165 250);
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    outline: none;
  }

  .dark select:hover:not(:focus) {
    border-color: rgb(107 114 128);
  }

  .dark label {
    color: rgb(229 231 235);
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  /* Optimized button improvements for both modes */
  button {
    font-weight: 600;
    transition: all 0.2s ease-in-out;
    letter-spacing: 0.025em;
  }

  .dark button {
    color: white;
  }

  button[type="submit"] {
    background-color: rgb(59 130 246);
    color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  button[type="submit"]:hover:not(:disabled) {
    background-color: rgb(37 99 235);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .dark button[type="submit"] {
    background-color: rgb(59 130 246);
    color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  }

  .dark button[type="submit"]:hover:not(:disabled) {
    background-color: rgb(37 99 235);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }

  button[type="submit"]:disabled {
    background-color: rgb(75 85 99);
    color: rgb(156 163 175);
    cursor: not-allowed;
    transform: none;
  }

  .dark button[type="submit"]:disabled {
    background-color: rgb(75 85 99);
    color: rgb(156 163 175);
    cursor: not-allowed;
    transform: none;
  }

  /* Enhanced error states */
  input:invalid:not(:focus):not(:placeholder-shown) {
    border-color: rgb(239 68 68);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  .dark input:invalid:not(:focus):not(:placeholder-shown) {
    border-color: rgb(248 113 113);
    box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.2);
  }

  /* Enhanced focus indicators for accessibility */
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  button:focus-visible {
    outline: 2px solid rgb(59 130 246);
    outline-offset: 2px;
  }

  .dark input:focus-visible,
  .dark textarea:focus-visible,
  .dark select:focus-visible,
  .dark button:focus-visible {
    outline: 2px solid rgb(96 165 250);
    outline-offset: 2px;
  }
}
