
import React from 'react';
import { CheckCircle, XCircle } from 'lucide-react';

interface PasswordMatchIndicatorProps {
  password: string;
  confirmPassword: string;
}

export const PasswordMatchIndicator: React.FC<PasswordMatchIndicatorProps> = ({
  password,
  confirmPassword
}) => {
  if (!confirmPassword) return null;

  return (
    <div className="flex items-center space-x-1 text-sm">
      {password === confirmPassword ? (
        <>
          <CheckCircle className="w-4 h-4 text-green-600" />
          <span className="text-green-600">Las contraseñas coinciden</span>
        </>
      ) : (
        <>
          <XCircle className="w-4 h-4 text-red-600" />
          <span className="text-red-600">Las contraseñas no coinciden</span>
        </>
      )}
    </div>
  );
};
