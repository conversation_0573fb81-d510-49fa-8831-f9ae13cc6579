
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { PercentageInput, formatCurrency } from '@/components/ui/numeric-input';
import { Income as IncomeType } from '@/types';

interface VariableComponentSectionProps {
  formData: Partial<IncomeType>;
  setFormData: React.Dispatch<React.SetStateAction<Partial<IncomeType>>>;
  calculations: any;
  isSubmitting?: boolean;
}

export function VariableComponentSection({ formData, setFormData, calculations, isSubmitting = false }: VariableComponentSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg text-finanz-primary">COMPONENTE VARIABLE</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="variablePercentage">Porcentaje Variable (%)</Label>
            <PercentageInput
              id="variablePercentage"
              value={formData.variablePercentage || 0}
              onChange={(value) => setFormData(prev => ({ ...prev, variablePercentage: value || 0 }))}
              placeholder="Ej: 125 para 125%"
              disabled={isSubmitting}
              autoComplete="off"
            />
            <p className="text-xs text-finanz-text-secondary mt-1">
              Puede exceder el 100% según las políticas de la empresa
            </p>
          </div>
          <div>
            <p className="text-base font-semibold mb-3 block">Sueldo Variable</p>
            <div className="h-10 px-3 py-2 bg-gray-100 border rounded-md flex items-center">
              <span className="text-finanz-success font-medium">
                {formatCurrency(calculations?.variableAmount || 0, formData.currency)}
              </span>
            </div>
          </div>
          <div>
            <Label htmlFor="performancePercentage">Porcentaje de Cumplimiento (%)</Label>
            <PercentageInput
              id="performancePercentage"
              value={formData.performancePercentage || 0}
              onChange={(value) => setFormData(prev => ({ ...prev, performancePercentage: value || 0 }))}
              placeholder="Ej: 120 para 120%"
              disabled={isSubmitting}
              autoComplete="off"
            />
            <p className="text-xs text-finanz-text-secondary mt-1">
              Puede exceder el 100% por excelencia en el desempeño
            </p>
          </div>
          <div>
            <p className="text-base font-semibold mb-3 block">Incentivo Trimestral</p>
            <div className="h-10 px-3 py-2 bg-gray-100 border rounded-md flex items-center">
              <span className="text-finanz-success font-medium">
                {formatCurrency(calculations?.quarterlyIncentive || 0, formData.currency)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
