
import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { History, X } from 'lucide-react';

interface SmartEmailInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  label?: string;
  error?: string;
}

export const SmartEmailInput: React.FC<SmartEmailInputProps> = ({
  value,
  onChange,
  placeholder = "<EMAIL>",
  required = false,
  disabled = false,
  label = "Correo Electrónico",
  error
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentEmails, setRecentEmails] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionRef = useRef<HTMLDivElement>(null);

  // Cargar emails recientes del localStorage
  useEffect(() => {
    const stored = localStorage.getItem('finanz_recent_emails');
    if (stored) {
      try {
        setRecentEmails(JSON.parse(stored));
      } catch (error) {
        console.error('Error loading recent emails:', error);
      }
    }
  }, []);

  // Guardar email cuando cambia el valor
  const saveEmail = (email: string) => {
    if (email && email.includes('@') && email.includes('.')) {
      const updated = [email, ...recentEmails.filter(e => e !== email)].slice(0, 5);
      setRecentEmails(updated);
      localStorage.setItem('finanz_recent_emails', JSON.stringify(updated));
    }
  };

  // Generar sugerencias basadas en el input
  useEffect(() => {
    if (value.length > 0) {
      const filtered = recentEmails.filter(email => 
        email.toLowerCase().includes(value.toLowerCase())
      );
      
      // Agregar sugerencias de dominios comunes
      const commonDomains = [
        '@gmail.com',
        '@hotmail.com',
        '@yahoo.com',
        '@outlook.com',
        '@icloud.com'
      ];

      const domainSuggestions = [];
      if (value.includes('@') && !value.includes('.')) {
        const [localPart] = value.split('@');
        domainSuggestions.push(
          ...commonDomains.map(domain => localPart + domain)
        );
      } else if (!value.includes('@')) {
        domainSuggestions.push(
          ...commonDomains.map(domain => value + domain)
        );
      }

      setSuggestions([...filtered, ...domainSuggestions].slice(0, 5));
      setShowSuggestions(filtered.length > 0 || domainSuggestions.length > 0);
    } else {
      setSuggestions(recentEmails.slice(0, 3));
      setShowSuggestions(false);
    }
  }, [value, recentEmails]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  const handleInputFocus = () => {
    if (recentEmails.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      if (!suggestionRef.current?.contains(e.relatedTarget as Node)) {
        setShowSuggestions(false);
      }
    }, 150);
  };

  const handleSuggestionClick = (email: string) => {
    onChange(email);
    saveEmail(email);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const clearRecentEmails = () => {
    setRecentEmails([]);
    localStorage.removeItem('finanz_recent_emails');
    setShowSuggestions(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' || e.key === 'Enter') {
      if (value.includes('@') && value.includes('.')) {
        saveEmail(value);
      }
    }
  };

  return (
    <div className="space-y-2 relative">
      {label && (
        <Label htmlFor="smart-email" className={required ? "after:content-['*'] after:text-red-500" : ""}>
          {label}
        </Label>
      )}
      <Input
        ref={inputRef}
        id="smart-email"
        type="email"
        value={value}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className={error ? 'border-red-500' : ''}
        autoComplete="email"
      />
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      {showSuggestions && (suggestions.length > 0 || recentEmails.length > 0) && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 border shadow-lg">
          <CardContent className="p-0" ref={suggestionRef}>
            <div className="max-h-48 overflow-y-auto">
              {suggestions.length > 0 ? (
                <div className="py-2">
                  {suggestions.map((email, index) => (
                    <button
                      key={index}
                      type="button"
                      className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm"
                      onClick={() => handleSuggestionClick(email)}
                    >
                      {recentEmails.includes(email) ? (
                        <History className="w-4 h-4 text-gray-400" />
                      ) : (
                        <div className="w-4 h-4" />
                      )}
                      {email}
                    </button>
                  ))}
                </div>
              ) : recentEmails.length > 0 && (
                <div className="py-2">
                  <div className="px-3 py-2 text-xs text-gray-500 font-medium border-b">
                    Emails recientes
                  </div>
                  {recentEmails.slice(0, 3).map((email, index) => (
                    <button
                      key={index}
                      type="button"
                      className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm"
                      onClick={() => handleSuggestionClick(email)}
                    >
                      <History className="w-4 h-4 text-gray-400" />
                      {email}
                    </button>
                  ))}
                </div>
              )}
              
              {recentEmails.length > 0 && (
                <div className="border-t p-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={clearRecentEmails}
                    className="w-full text-xs text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-3 h-3 mr-2" />
                    Limpiar historial
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
