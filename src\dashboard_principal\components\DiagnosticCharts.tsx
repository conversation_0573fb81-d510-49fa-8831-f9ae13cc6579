import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { TrendingUp, AlertTriangle, Target, Calendar } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';

interface TooltipPayload {
  name: string;
  value: number;
  color: string;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: TooltipPayload[];
  label?: string;
}

interface DiagnosticChartsProps {
  netIncome: number;
  totalMonthlyPayments: number;
  totalDebt: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
  paymentBreakdown?: {
    pending: number;
    overdue: number;
    paid: number;
  };
}

export const DiagnosticCharts: React.FC<DiagnosticChartsProps> = ({
  netIncome,
  totalMonthlyPayments,
  totalDebt,
  savingsRate,
  debtToIncomeRatio,
  emergencyFundMonths,
  paymentBreakdown = { pending: 0, overdue: 0, paid: 0 }
}) => {
  const isDark = useIsDarkMode();
  const { isMobile } = useBreakpoint();
  // Datos para el gráfico de distribución financiera
  const distributionData = [
    { name: 'Pagos Programados', value: totalMonthlyPayments, color: '#ef4444' },
    { name: 'Balance Disponible', value: Math.max(0, netIncome - totalMonthlyPayments), color: '#22c55e' },
    { name: 'Deuda Total', value: totalDebt, color: '#f59e0b' }
  ].filter(item => item.value > 0);

  // Datos para desglose de pagos
  const paymentStatusData = [
    { name: 'Pendientes', value: paymentBreakdown.pending, color: '#f59e0b' },
    { name: 'Vencidos', value: paymentBreakdown.overdue, color: '#ef4444' },
    { name: 'Pagados', value: paymentBreakdown.paid, color: '#22c55e' }
  ].filter(item => item.value > 0);

  // Datos para indicadores de salud
  const healthIndicators = [
    { 
      name: 'Tasa de Ahorro', 
      value: savingsRate, 
      target: 20, 
      status: savingsRate >= 20 ? 'good' : savingsRate >= 10 ? 'warning' : 'danger' 
    },
    { 
      name: 'Ratio Deuda/Ingreso', 
      value: debtToIncomeRatio, 
      target: 30, 
      status: debtToIncomeRatio <= 30 ? 'good' : debtToIncomeRatio <= 50 ? 'warning' : 'danger' 
    },
    { 
      name: 'Fondo de Emergencia', 
      value: emergencyFundMonths, 
      target: 6, 
      status: emergencyFundMonths >= 6 ? 'good' : emergencyFundMonths >= 3 ? 'warning' : 'danger' 
    }
  ];

  // Datos para comparación ingreso vs pagos programados
  const comparisonData = [
    {
      category: 'Ingresos Netos',
      amount: netIncome,
      color: '#22c55e'
    },
    {
      category: 'Pagos Programados',
      amount: totalMonthlyPayments,
      color: '#ef4444'
    },
    {
      category: 'Balance Neto',
      amount: netIncome - totalMonthlyPayments,
      color: netIncome - totalMonthlyPayments >= 0 ? '#22c55e' : '#ef4444'
    }
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-neutral-800 text-black dark:text-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {
                entry.name.includes('%') || entry.name.includes('Tasa') || entry.name.includes('Ratio') ? 
                `${entry.value.toFixed(1)}%` :
                entry.name.includes('Meses') || entry.name.includes('Fondo') ?
                `${entry.value.toFixed(1)} meses` :
                formatCurrency(entry.value, 'DOP')
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const paymentEfficiency = netIncome > 0 ? ((netIncome - totalMonthlyPayments) / netIncome * 100) : 0;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Distribución Financiera */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-finanz-primary" />
            Distribución Financiera Mensual
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="chart-container-responsive">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={isMobile ? 60 : 80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 space-y-2">
            {distributionData.map((item, index) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm">{item.name}</span>
                </div>
                <span className="text-sm font-medium">
                  {formatCurrency(item.value, 'DOP')}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Estado de Pagos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-finanz-warning" />
            Estado de Pagos del Mes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="chart-container-responsive">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={paymentStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={isMobile ? 60 : 80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {paymentStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 space-y-2">
            {paymentStatusData.map((item, index) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm">{item.name}</span>
                </div>
                <span className="text-sm font-medium">
                  {formatCurrency(item.value, 'DOP')}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Comparación Ingresos vs Pagos Programados */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-finanz-warning" />
            Ingresos vs Pagos Programados
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="chart-container-responsive">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={comparisonData}>
                <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
                <XAxis dataKey="category" stroke={isDark ? '#e5e7eb' : '#374151'} />
                <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="amount" name="Monto">
                  {comparisonData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Eficiencia de Pagos:</span>
              <span className={`text-sm font-bold ${
                paymentEfficiency >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {paymentEfficiency.toFixed(1)}%
              </span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Porcentaje de ingresos disponibles después de pagos programados
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Indicadores de Salud Financiera */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-finanz-success" />
            Indicadores de Salud Financiera
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4">
            {healthIndicators.map((indicator, index) => (
              <div key={indicator.name} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-sm">{indicator.name}</h4>
                  <div className={`w-3 h-3 rounded-full ${
                    indicator.status === 'good' ? 'bg-green-500' :
                    indicator.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-500">Actual:</span>
                    <span className="text-sm font-medium">
                      {indicator.name.includes('Fondo') ? 
                        `${indicator.value.toFixed(1)} meses` : 
                        `${indicator.value.toFixed(1)}%`
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-500">Meta:</span>
                    <span className="text-xs">
                      {indicator.name.includes('Fondo') ? 
                        `${indicator.target} meses` : 
                        `${indicator.target}%`
                      }
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        indicator.status === 'good' ? 'bg-green-500' :
                        indicator.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ 
                        width: `${Math.min(100, (indicator.value / indicator.target) * 100)}%` 
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
