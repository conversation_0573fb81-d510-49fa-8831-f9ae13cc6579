import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';
import {
  CreditCard,
  Building2,
  Repeat,
  User,
  ShoppingCart,
  Check,
  Clock,
  AlertTriangle,
  Calendar,
  Eye,
  Undo
} from 'lucide-react';
import { PaymentItem as PaymentItemType } from '../types/paymentTypes';
import { format, differenceInCalendarDays, isToday } from 'date-fns';
import { es } from 'date-fns/locale';

// Payment type helpers
const getPaymentTypeLabel = (type: string) => {
  switch (type) {
    case 'credit-card': return 'Tarjeta de Crédito';
    case 'loan': return 'Préstamo';
    case 'subscription': return 'Suscripción';
    case 'personal-debt': return 'Deuda Personal';
    case 'expense': return 'Gasto';
    default: return 'Pago';
  }
};

const getPaymentTypeIcon = (type: string) => {
  switch (type) {
    case 'credit-card': return <CreditCard className="w-4 h-4" />;
    case 'loan': return <Building2 className="w-4 h-4" />;
    case 'subscription': return <Repeat className="w-4 h-4" />;
    case 'personal-debt': return <User className="w-4 h-4" />;
    case 'expense': return <ShoppingCart className="w-4 h-4" />;
    default: return <Calendar className="w-4 h-4" />;
  }
};

const getStatusBadgeStyle = (status: string) => {
  switch (status) {
    case 'paid': return 'bg-finanz-success text-white';
    case 'overdue': return 'bg-finanz-danger text-white';
    case 'pending': return 'bg-finanz-warning text-white';
    default: return 'bg-gray-500 text-white';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'paid': return <Check className="w-3 h-3" />;
    case 'overdue': return <AlertTriangle className="w-3 h-3" />;
    case 'pending': return <Clock className="w-3 h-3" />;
    default: return <Clock className="w-3 h-3" />;
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'paid': return 'Pagado';
    case 'overdue': return 'Vencido';
    case 'pending': return 'Pendiente';
    default: return 'Desconocido';
  }
};

const getProgressColor = (status: string) => {
  switch (status) {
    case 'paid': return 'bg-finanz-success';
    case 'overdue': return 'bg-finanz-danger';
    case 'pending': return 'bg-finanz-warning';
    default: return 'bg-gray-300';
  }
};

interface PaymentItemProps {
  payment: PaymentItemType;
  onMarkAsPaid?: (payment: PaymentItemType) => void;
  onUnmarkAsPaid?: (payment: PaymentItemType) => void;
  isProjection?: boolean;
}

const PaymentItemEnhancedComponent: React.FC<PaymentItemProps> = ({
  payment,
  onMarkAsPaid,
  onUnmarkAsPaid,
  isProjection = false
}) => {
  const dueDate = new Date(payment.dueDate);
  const paidDate = payment.paidDate ? new Date(payment.paidDate) : null;

  const actualStatus = payment.status;
  const isPaid = actualStatus === 'paid';

  const today = new Date();
  const daysDifference = differenceInCalendarDays(dueDate, today);

  let dueDateDisplay: string;
  let dueDateClass = 'text-sm text-finanz-text-secondary';

  if (isPaid && paidDate) {
    dueDateDisplay = `Pagado el ${format(paidDate, 'dd MMM', { locale: es })}`;
    dueDateClass = 'text-sm text-finanz-success';
  } else if (isToday(dueDate) && actualStatus === 'pending') {
    dueDateDisplay = 'Vence hoy';
    dueDateClass = 'text-sm text-finanz-warning font-medium';
  } else if (actualStatus === 'overdue') {
    dueDateDisplay = `Vencido hace ${Math.abs(daysDifference)} día${Math.abs(daysDifference) !== 1 ? 's' : ''}`;
    dueDateClass = 'text-sm text-finanz-danger font-medium';
  } else if (daysDifference > 0 && daysDifference <= 7 && actualStatus === 'pending') {
    dueDateDisplay = `Vence en ${daysDifference} día${daysDifference > 1 ? 's' : ''}`;
    dueDateClass = 'text-sm text-finanz-warning font-medium';
  } else {
    dueDateDisplay = `Vence el ${format(dueDate, 'dd MMM yyyy', { locale: es })}`;
  }

  const maxDays = 30;
  let progressPercent = 0;
  if (daysDifference <= 0) {
    progressPercent = 100;
  } else if (daysDifference >= maxDays) {
    progressPercent = 0;
  } else {
    progressPercent = ((maxDays - daysDifference) / maxDays) * 100;
  }

  return (
    <Card className={`transition-all hover:shadow-md ${
      actualStatus === 'overdue' ? 'border-l-4 border-l-finanz-danger' :
      actualStatus === 'paid' ? 'border-l-4 border-l-finanz-success' :
      'border-l-4 border-l-finanz-warning'
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-start gap-3 flex-1">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-finanz-background flex-shrink-0 mt-1">
              {getPaymentTypeIcon(payment.type)}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-0.5">
                <h4 className="font-medium text-finanz-text-primary truncate">
                  {payment.name}
                </h4>
                {isProjection && (
                  <Badge variant="outline" className="text-xs py-0.5">
                    Proyección
                  </Badge>
                )}
                {payment.type === 'personal-debt' && !isProjection && (
                  <Badge variant="outline" className="text-xs py-0.5 border-finanz-blue/50 text-finanz-blue">
                    Abono Sugerido
                  </Badge>
                )}
              </div>

              <p className={dueDateClass}>{dueDateDisplay}</p>
              <p className="text-xs text-finanz-text-secondary mt-0.5">
                {getPaymentTypeLabel(payment.type)}
              </p>
              {payment.notes && (
                <p className="text-xs text-finanz-text-secondary mt-1 truncate" title={payment.notes}>
                  Nota: {payment.notes}
                </p>
              )}
            </div>
          </div>

          <div className="flex flex-col items-end gap-2 flex-shrink-0">
            <div className="text-right">
              <div className="text-lg font-semibold text-finanz-text-primary">
                {formatCurrency(payment.amount, payment.currency)}
              </div>
              <Badge className={`text-xs px-2 py-0.5 ${getStatusBadgeStyle(actualStatus)}`}>
                <span className="flex items-center gap-1">
                  {getStatusIcon(actualStatus)}
                  {getStatusText(actualStatus)}
                </span>
              </Badge>
            </div>

            <div className="flex flex-col gap-1 items-end">
              {!isProjection && actualStatus === 'pending' && onMarkAsPaid && (
                <Button size="xs" onClick={() => onMarkAsPaid(payment)} className="text-xs">
                  Marcar Pagado
                </Button>
              )}

              {!isProjection && actualStatus === 'overdue' && onMarkAsPaid && (
                <Button size="xs" variant="destructive" onClick={() => onMarkAsPaid(payment)} className="text-xs">
                  Pagar Ahora
                </Button>
              )}

              {!isProjection && actualStatus === 'paid' && onUnmarkAsPaid && (
                <Button size="xs" variant="outline" onClick={() => onUnmarkAsPaid(payment)} className="text-xs">
                  <Undo className="w-3 h-3 mr-1" /> Desmarcar
                </Button>
              )}

              {isProjection && (
                <Button size="xs" variant="outline" className="text-xs" disabled>
                  <Eye className="w-3 h-3 mr-1" />
                  Ver Proyección
                </Button>
              )}
            </div>
          </div>
        </div>
        <div className="mt-3 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
          <div
            className={`h-full ${getProgressColor(actualStatus)}`}
            style={{ width: `${progressPercent}%` }}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export const PaymentItemEnhanced = React.memo(PaymentItemEnhancedComponent);

