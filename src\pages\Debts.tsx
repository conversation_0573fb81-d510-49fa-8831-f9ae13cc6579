import { logger } from "@/utils/logger";

import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CreditCard } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFinanceData } from '@/hooks/useFinanceData';
import { CreditCardsSection } from '@/debt-management/components/CreditCardsSection';
import { LoansSection } from '@/debt-management/components/LoansSection';
import { PersonalDebtsSection } from '@/debt-management/components/PersonalDebtsSection';
import { DebtSummary } from '@/debt-management/components/DebtSummary';
import { formatCurrency, getAmountInDOP } from '@/components/ui/numeric-input';

export default function DebtsPage() {
  const [searchParams] = useSearchParams();
  // Solo cargar datos de deudas
  const { getTotalDebt, creditCards, loans, personalDebts, isLoading } = useFinanceData(['debts']);
  const [activeTab, setActiveTab] = useState(
    searchParams.get('cardId') ? 'credit-cards' : 'overview'
  );

  const totalDebt = getTotalDebt();
  logger.debug('DebtsPage - Total debt calculated:', totalDebt);
  
  const activeCreditCards = creditCards.filter(card => card.isActive);
  const activeLoans = loans.filter(loan => loan.isActive);
  const activePersonalDebts = personalDebts.filter(debt => debt.isActive);

  const creditCardDebt = activeCreditCards.reduce((total, card) => 
    total + getAmountInDOP(card.currentBalance, card.currency), 0);
  const loanDebt = activeLoans.reduce((total, loan) => 
    total + getAmountInDOP(loan.totalAmount, loan.currency), 0);
  const personalDebt = activePersonalDebts.reduce((total, debt) =>
    total + getAmountInDOP(debt.amount, debt.currency), 0);

  if (
    isLoading &&
    creditCards.length === 0 &&
    loans.length === 0 &&
    personalDebts.length === 0
  ) {
    return (
      <div className="p-6 space-y-6 animate-pulse">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-48"></div>
          </div>
          <div className="w-8 h-8 bg-gray-200 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestión de Deudas</h1>
          <p className="text-finanz-text-secondary">
            Controla tus tarjetas de crédito, préstamos y deudas personales
          </p>
        </div>
        <CreditCard className="w-8 h-8 text-finanz-purple" />
      </div>

      <DebtSummary totalDebt={totalDebt} />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="credit-cards">
            Tarjetas ({activeCreditCards.length})
          </TabsTrigger>
          <TabsTrigger value="loans">
            Préstamos ({activeLoans.length})
          </TabsTrigger>
          <TabsTrigger value="personal-debts">
            Deudas Personales ({activePersonalDebts.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Tarjetas de Crédito</CardTitle>
                <CardDescription>
                  {activeCreditCards.length} tarjeta(s) activa(s)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-finanz-danger">
                  {formatCurrency(creditCardDebt)}
                </div>
                <p className="text-sm text-finanz-text-secondary mt-1">
                  Balance total usado
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Préstamos</CardTitle>
                <CardDescription>
                  {activeLoans.length} préstamo(s) activo(s)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-finanz-warning">
                  {formatCurrency(loanDebt)}
                </div>
                <p className="text-sm text-finanz-text-secondary mt-1">
                  Total adeudado
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Deudas Personales</CardTitle>
                <CardDescription>
                  {activePersonalDebts.length} deuda(s) activa(s)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-finanz-purple">
                  {formatCurrency(personalDebt)}
                </div>
                <p className="text-sm text-finanz-text-secondary mt-1">
                  Total adeudado
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="credit-cards">
          <CreditCardsSection />
        </TabsContent>

        <TabsContent value="loans">
          <LoansSection />
        </TabsContent>

        <TabsContent value="personal-debts">
          <PersonalDebtsSection />
        </TabsContent>
      </Tabs>
    </div>
  );
}
