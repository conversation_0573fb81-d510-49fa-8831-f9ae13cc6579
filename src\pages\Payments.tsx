
import { Calendar } from 'lucide-react';
import { EnhancedPaymentsSection } from '@/seguimiento_pagos/components/EnhancedPaymentsSection';
// import { useFinanceData } from '@/hooks/useFinanceData'; // Removed
import { usePaymentsSynchronization } from '@/seguimiento_pagos/hooks/usePaymentsSynchronization';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export default function PaymentsPage() {
  // const { paymentRecords, isLoading } = useFinanceData(); // Removed
  const { isMobile } = useBreakpoint();
  
  // Habilitar sincronización automática de pagos
  usePaymentsSynchronization();

  // Loading state is now handled by EnhancedPaymentsSection

  return (
    <div className={`space-y-4 md:space-y-6 ${isMobile ? 'p-3' : 'p-6'}`}>
      <div className={`flex items-center justify-between ${isMobile ? 'flex-col space-y-2' : ''}`}>
        <div className={`${isMobile ? 'text-center' : ''}`}>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-3xl'}`}>
            {isMobile ? 'Pagos' : 'Seguimiento de Pagos'}
          </h1>
          <p className={`text-finanz-text-secondary ${isMobile ? 'text-xs' : ''}`}>
            {isMobile ? 'Gestiona tus pagos mes tras mes' : 'Controla y proyecta tus pagos con navegación temporal completa'}
          </p>
        </div>
        <Calendar className={`text-finanz-indigo ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`} />
      </div>

      <EnhancedPaymentsSection />
    </div>
  );
}
