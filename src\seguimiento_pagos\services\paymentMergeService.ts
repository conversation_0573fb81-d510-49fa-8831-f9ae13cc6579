
import { logger } from "@/utils/logger";
import { PaymentRecord } from '@/types';
import { PaymentItem } from '../types/paymentTypes';

export class PaymentMergeService {
  mergePaymentsWithRecords(
    generatedPayments: PaymentItem[],
    paymentRecords: PaymentRecord[]
  ): PaymentItem[] {
    logger.debug('Starting payment merge process:', {
      generatedCount: generatedPayments.length,
      recordsCount: paymentRecords.length
    });

    // Crear mapa de registros con clave compuesta: type-referenceId-dueDate
    const recordedPaymentsMap = new Map<string, PaymentRecord>();
    
    paymentRecords.forEach(record => {
      if (!record.paymentType || !record.referenceId || !record.dueDate) {
        logger.warn('Invalid payment record found (missing type, refId, or dueDate):', record);
        return;
      }
      
      // Clave primaria: type-referenceId-dueDate
      const primaryKey = `${record.paymentType}-${record.referenceId}-${record.dueDate}`;
      
      // Mantener el registro más reciente si hay duplicados
      const existing = recordedPaymentsMap.get(primaryKey);
      if (!existing || new Date(record.updatedAt || record.createdAt) > new Date(existing.updatedAt || existing.createdAt)) {
        recordedPaymentsMap.set(primaryKey, record);
        logger.debug('Added payment record to map:', {
          key: primaryKey,
          recordId: record.id,
          status: record.status,
          paidDate: record.paidDate
        });
      }
    });

    logger.debug('Payment records map created with keys:', Array.from(recordedPaymentsMap.keys()));

    // Fusionar pagos generados con registros
    const mergedPayments: PaymentItem[] = [];
    const processedKeys = new Set<string>();

    generatedPayments.forEach(payment => {
      if (!payment.type || !payment.referenceId || !payment.dueDate) {
        logger.warn('Invalid generated payment found (missing type, refId, or dueDate):', payment);
        return;
      }

      const key = `${payment.type}-${payment.referenceId}-${payment.dueDate}`;
      const record = recordedPaymentsMap.get(key);
      
      logger.debug('Processing payment:', {
        paymentId: payment.id,
        paymentName: payment.name,
        paymentType: payment.type,
        key,
        hasRecord: !!record,
        recordStatus: record?.status,
        recordPaidDate: record?.paidDate
      });
      
      if (record) {
        // CRÍTICO: Fusionar correctamente garantizando que el estado se respete
        const mergedPayment: PaymentItem = {
          ...payment,
          status: record.status, // Estado del registro tiene prioridad absoluta
          paidDate: record.paidDate,
          notes: record.notes,
          recordId: record.id,
          amount: record.amount || payment.amount,
          createdAt: payment.createdAt || record.createdAt // Asegurar que la fecha de creación esté presente
        };
        
        mergedPayments.push(mergedPayment);
        processedKeys.add(key);
        
        logger.debug('Payment merged with record (DEBT TYPE):', {
          paymentId: payment.id,
          paymentType: payment.type,
          finalStatus: mergedPayment.status,
          finalPaidDate: mergedPayment.paidDate,
          recordId: record.id,
          isDebtPayment: payment.type === 'personal-debt' || payment.type === 'credit-card' || payment.type === 'loan'
        });
        
        // Log adicional para pagos de deuda que se marcan como pagados
        if ((payment.type === 'personal-debt' || payment.type === 'credit-card' || payment.type === 'loan') && record.status === 'paid') {
          logger.debug('DEBT PAYMENT MARKED AS PAID - should appear in completed section:', {
            paymentName: payment.name,
            paymentType: payment.type,
            status: record.status,
            paidDate: record.paidDate
          });
        }
      } else {
        // Pago sin registro - mantener estado original
        mergedPayments.push(payment);
        processedKeys.add(key);
        
        logger.debug('Payment added without record:', {
          paymentId: payment.id,
          paymentType: payment.type,
          status: payment.status
        });
      }
    });

    // Agregar registros huérfanos como pagos
    paymentRecords.forEach(record => {
      const key = `${record.paymentType}-${record.referenceId}-${record.dueDate}`;
      
      if (!processedKeys.has(key)) {
        // Generar un nombre más descriptivo basado en el tipo
        let paymentName = '';
        
        switch (record.paymentType) {
          case 'expense':
            paymentName = record.notes && !record.notes.includes('Gasto recurrente') 
              ? record.notes 
              : 'Gasto sin descripción';
            break;
          case 'credit-card':
            paymentName = 'Pago de Tarjeta de Crédito';
            break;
          case 'loan':
            paymentName = 'Pago de Préstamo';
            break;
          case 'subscription':
            paymentName = 'Suscripción';
            break;
          case 'personal-debt':
            paymentName = 'Deuda Personal';
            break;
          default:
            paymentName = `Pago ${record.paymentType}`;
        }
        
        const orphanPayment: PaymentItem = {
          id: `record-${record.id}`,
          type: record.paymentType as PaymentItem['type'],
          name: paymentName,
          amount: record.amount,
          currency: record.currency,
          dueDate: record.dueDate,
          status: record.status,
          paidDate: record.paidDate,
          notes: record.notes,
          recordId: record.id,
          referenceId: record.referenceId,
          createdAt: record.createdAt // Propagar fecha de creación para validaciones temporales
        };
        
        mergedPayments.push(orphanPayment);
        
        logger.debug('Orphan record added as payment:', {
          recordId: record.id,
          paymentType: record.paymentType,
          status: record.status,
          paidDate: record.paidDate
        });
      }
    });

    // Validar estado final con foco en pagos de deuda
    const finalStats = {
      totalMerged: mergedPayments.length,
      paidCount: mergedPayments.filter(p => p.status === 'paid').length,
      pendingCount: mergedPayments.filter(p => p.status === 'pending').length,
      overdueCount: mergedPayments.filter(p => p.status === 'overdue').length,
      debtPaidCount: mergedPayments.filter(p => 
        (p.type === 'personal-debt' || p.type === 'credit-card' || p.type === 'loan') && 
        p.status === 'paid'
      ).length
    };

    logger.debug('Payment merge completed with debt focus:', finalStats);

    // Log específico de pagos de deuda completados
    const paidDebtPayments = mergedPayments.filter(p => 
      (p.type === 'personal-debt' || p.type === 'credit-card' || p.type === 'loan') && 
      p.status === 'paid'
    );
    
    if (paidDebtPayments.length > 0) {
      logger.debug('PAID DEBT PAYMENTS found (should appear in Completados):', paidDebtPayments.map(p => ({
        id: p.id,
        name: p.name,
        type: p.type,
        paidDate: p.paidDate,
        recordId: p.recordId
      })));
    }

    return mergedPayments;
  }
}
