import { describe, it, expect, vi } from 'vitest'
import { PaymentGenerationService } from '@/seguimiento_pagos/services/paymentGenerationService'
import type { Expense } from '@/types'

vi.mock('@/utils/logger', () => ({
  logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }
}))

describe('generateNextPaymentDate', () => {
  it('handles full date paymentDate for recurring expenses', () => {
    const service = new PaymentGenerationService({ year: 2024, month: 5 })
    const expenses: Expense[] = [
      {
        id: 'exp1',
        date: '2024-03-01',
        month: '2024-03',
        type: 'Fijo',
        categoryId: 'cat',
        categoryName: 'Category',
        amount: 10,
        description: 'desc',
        paymentMethod: 'cash',
        status: 'pending',
        currency: 'USD',
        paymentDate: '2024-03-05',
        isRecurring: true,
        createdAt: '2024-03-01',
        updatedAt: '2024-03-01'
      }
    ]

    const payments = service.generateFromExpenses(expenses)
    expect(payments).toHaveLength(1)
    expect(payments[0].dueDate).toBe('2024-05-05')
  })

  it('marks non-recurring expenses as overdue when created mid-month with past due date', () => {
    const service = new PaymentGenerationService({ year: 2024, month: 5 })
    const expenses: Expense[] = [
      {
        id: 'exp-overdue',
        date: '2024-05-01',
        month: '2024-05',
        type: 'Fijo',
        categoryId: 'cat',
        categoryName: 'Category',
        amount: 10,
        description: 'desc',
        paymentMethod: 'cash',
        status: 'pending',
        currency: 'USD',
        paymentDate: '2024-05-05',
        isRecurring: false,
        createdAt: '2024-05-10',
        updatedAt: '2024-05-10'
      }
    ]

    const payments = service.generateFromExpenses(expenses)
    expect(payments).toHaveLength(1)
    expect(payments[0].status).toBe('overdue')
    expect(payments[0].dueDate).toBe('2024-05-05')
  })

  it('excludes overdue expense from periods before its creation', () => {
    const service = new PaymentGenerationService({ year: 2024, month: 4 })
    const expenses: Expense[] = [
      {
        id: 'exp-overdue',
        date: '2024-05-01',
        month: '2024-05',
        type: 'Fijo',
        categoryId: 'cat',
        categoryName: 'Category',
        amount: 10,
        description: 'desc',
        paymentMethod: 'cash',
        status: 'pending',
        currency: 'USD',
        paymentDate: '2024-05-05',
        isRecurring: false,
        createdAt: '2024-05-10',
        updatedAt: '2024-05-10'
      }
    ]

    const payments = service.generateFromExpenses(expenses)
    expect(payments).toHaveLength(0)
  })
})
