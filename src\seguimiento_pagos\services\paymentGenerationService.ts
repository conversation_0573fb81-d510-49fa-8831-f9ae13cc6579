import { logger } from "@/utils/logger";
import { stripTime, endOfMonth } from '../utils/temporalUtils';
import { CreditCard, Loan, Subscription, PersonalDebt, Expense } from '@/types';
import { PaymentItem } from '../types/paymentTypes';
import { TemporalPeriod } from "../hooks/useTemporalNavigation"; // Import TemporalPeriod

export class PaymentGenerationService {
  private targetYear: number;
  private targetMonth: number; // 0-indexed (0 for January, 11 for December)
  private today: Date; // Keep today for reference if needed for other logic, like overdue status

  constructor(currentPeriod?: TemporalPeriod) {
    if (currentPeriod) {
      // TemporalPeriod.month es 1-12, pero JavaScript Date.getMonth() es 0-11
      this.targetYear = currentPeriod.year;
      this.targetMonth = currentPeriod.month - 1; // Convertir de 1-12 a 0-11
    } else {
      const now = new Date();
      this.targetYear = now.getFullYear();
      this.targetMonth = now.getMonth(); // Ya es 0-11
    }
    this.today = new Date();
    this.today.setHours(0, 0, 0, 0);
    logger.debug(`PaymentGenerationService initialized for period: Year ${this.targetYear}, Month ${this.targetMonth + 1}`);
  }

  // Helper function to parse payment date string and generate next payment date for the target period
  private generateNextPaymentDate = (paymentDateStr: string, entityId: string): string => {
    try {
      let dayOfMonth: number;

      if (paymentDateStr.includes('-')) {
        const parsedDate = new Date(paymentDateStr + "T00:00:00");
        if (isNaN(parsedDate.getTime())) {
          logger.warn(`Invalid full paymentDate '${paymentDateStr}' for entity ${entityId}. Defaulting to 1st.`);
          dayOfMonth = 1;
        } else {
          dayOfMonth = parsedDate.getDate();
        }
      } else {
        const parsedDay = parseInt(paymentDateStr, 10);
        if (isNaN(parsedDay) || parsedDay < 1 || parsedDay > 31) {
          logger.warn(`Invalid dayOfMonth '${paymentDateStr}' for entity ${entityId}. Defaulting to 1st of target month.`);
          dayOfMonth = 1;
        } else {
          dayOfMonth = parsedDay;
        }
      }

      // Clamp the day to the number of days in the target month
      const daysInMonth = new Date(this.targetYear, this.targetMonth + 1, 0).getDate();
      const validDay = Math.min(dayOfMonth, daysInMonth);

      const nextPaymentDate = new Date(this.targetYear, this.targetMonth, validDay);
      return nextPaymentDate.toISOString().split('T')[0];

    } catch (error) {
      logger.error('Error generating payment date for entity:', entityId, 'input:', paymentDateStr, error);
      // Fallback to the 1st of the target month if error
      return new Date(this.targetYear, this.targetMonth, 1).toISOString().split('T')[0];
    }
  };

  generateFromCreditCards(creditCards: CreditCard[]): PaymentItem[] {
    const payments: PaymentItem[] = [];
    creditCards.filter(card => card.isActive).forEach(card => {
      // Assuming card.paymentDueDate is a full "YYYY-MM-DD" string
      const dueDate = new Date(card.paymentDueDate + "T00:00:00"); // Ensure local timezone

      // VALIDACIÓN DE FECHA DE CREACIÓN: no generar pago si la tarjeta fue creada después de la fecha
      // de vencimiento o después de que termina el período.
      if (card.createdAt) {
        const createdAtDate = new Date(card.createdAt);
        const periodEnd = new Date(this.targetYear, this.targetMonth + 1, 0, 23, 59, 59, 999);

        // Evitar generación en meses anteriores a la creación
        if (createdAtDate > periodEnd) {
          logger.debug(`CreditCard ${card.id} creada el ${card.createdAt} posterior al período ${this.targetYear}-${this.targetMonth + 1}. Se omite.`);
          return;
        }

        // Evitar marcar como vencido el mismo mes de creación si la cuota es anterior al createdAt
        if (createdAtDate > dueDate) {
          logger.debug(`CreditCard ${card.id} – cuota ${card.paymentDueDate} anterior a createdAt (${card.createdAt}). Se omite generación para evitar falso vencido.`);
          return;
        }
      }
      if (dueDate.getFullYear() === this.targetYear && dueDate.getMonth() === this.targetMonth) {
        payments.push({
          id: `cc-${card.id}-${this.targetYear}-${this.targetMonth + 1}`,
          type: 'credit-card',
          name: card.name,
          amount: card.minimumPayment,
          currency: card.currency,
          dueDate: card.paymentDueDate,
          status: 'pending',
          referenceId: card.id,
          createdAt: (card as any).createdAt
        });
      }
    });
    return payments;
  }

  generateFromLoans(loans: Loan[]): PaymentItem[] {
    const payments: PaymentItem[] = [];

    loans.filter(loan => loan.isActive).forEach(loan => {
      /*
       * Normalizamos el campo paymentDate para admitir:
       *  - Formato completo "YYYY-MM-DD"
       *  - Solo día del mes "DD"
       * Independientemente del formato, siempre programamos el pago para el
       *  período objetivo (this.targetYear, this.targetMonth) utilizando el día del mes.
       */

      let dayOfMonth: number | null = null;

      if (loan.paymentDate.includes('-')) {
        // Extraemos el día del mes de la fecha completa
        const parsedDate = new Date(loan.paymentDate + "T00:00:00");
        if (isNaN(parsedDate.getTime())) {
          logger.warn(`Invalid full paymentDate '${loan.paymentDate}' for loan ${loan.id}. Skipping.`);
          return;
        }
        dayOfMonth = parsedDate.getDate();
      } else {
        const dayParsed = parseInt(loan.paymentDate, 10);
        if (!isNaN(dayParsed) && dayParsed >= 1 && dayParsed <= 31) {
          dayOfMonth = dayParsed;
      } else {
        logger.warn(`Unsupported loan.paymentDate format '${loan.paymentDate}' for loan ${loan.id}. Skipping.`);
        return;
        }
      }

      // Aseguramos que el día existe en el mes objetivo
      const daysInTargetMonth = new Date(this.targetYear, this.targetMonth + 1, 0).getDate();
      const validDay = Math.min(dayOfMonth!, daysInTargetMonth);
      const dueDate = new Date(this.targetYear, this.targetMonth, validDay);
      const dueDateStr = dueDate.toISOString().split('T')[0];

      // NUEVA VALIDACIÓN: si el préstamo tiene una paymentDate completa (YYYY-MM-DD)
      // y la fecha de vencimiento calculada para el período objetivo es anterior a esa
      // primera fecha de pago, omitimos la generación para evitar falsos vencimientos.
      if (loan.paymentDate.includes('-')) {
        const firstPaymentDate = new Date(loan.paymentDate + "T00:00:00");
        if (dueDate < firstPaymentDate) {
          logger.debug(`Loan ${loan.id} – se omite generación para ${this.targetYear}-${this.targetMonth + 1} ya que la primera cuota es ${loan.paymentDate}`);
          return; // Salir del forEach y no añadir pago para este período
        }
      }

      // VALIDACIÓN DE CREACIÓN
      const periodEnd = new Date(this.targetYear, this.targetMonth + 1, 0, 23, 59, 59, 999);
      if (loan.createdAt) {
        const createdLoanDate = new Date(loan.createdAt);
        if (createdLoanDate > periodEnd) {
          logger.debug(`Loan ${loan.id} creado el ${loan.createdAt} posterior al período. Omitido.`);
          return;
        }
      }

      payments.push({
        id: `loan-${loan.id}-${this.targetYear}-${this.targetMonth + 1}`,
        type: 'loan',
        name: loan.name,
        amount: loan.monthlyPayment,
        currency: loan.currency,
        dueDate: dueDateStr,
        status: 'pending',
        referenceId: loan.id,
        createdAt: loan.createdAt
      });
    });

    return payments;
  }

  generateFromSubscriptions(subscriptions: Subscription[]): PaymentItem[] {
    const payments: PaymentItem[] = [];
    subscriptions.filter(sub => sub.isActive).forEach(subscription => {
      const billingDay = parseInt(subscription.billingDate); // Assuming this is "DD"
      if (isNaN(billingDay) || billingDay < 1 || billingDay > 31) {
        logger.warn(`Invalid billingDay '${subscription.billingDate}' for subscription ${subscription.id}. Skipping.`);
        return;
      }

      const daysInTargetMonth = new Date(this.targetYear, this.targetMonth + 1, 0).getDate();
      const validBillingDay = Math.min(billingDay, daysInTargetMonth);

      const nextBillingDate = new Date(this.targetYear, this.targetMonth, validBillingDay);
      
      // VALIDACIÓN DE CREACIÓN
      const periodEnd = new Date(this.targetYear, this.targetMonth + 1, 0, 23, 59, 59, 999);
      if (subscription.createdAt) {
        const createdDate = new Date(subscription.createdAt);
        if (createdDate > periodEnd) return;
      }

      payments.push({
        // Ensure unique ID for each payment instance (e.g., by including year/month)
        id: `sub-${subscription.id}-${this.targetYear}-${this.targetMonth + 1}`,
        type: 'subscription',
        name: subscription.name,
        amount: subscription.amount,
        currency: subscription.currency,
        dueDate: nextBillingDate.toISOString().split('T')[0],
        status: 'pending',
        referenceId: subscription.id,
        createdAt: subscription.createdAt
      });
    });
    return payments;
  }

  generateFromPersonalDebts(personalDebts: PersonalDebt[]): PaymentItem[] {
    const payments: PaymentItem[] = [];
    logger.debug(`Generating personal debt payments for ${this.targetYear}-${this.targetMonth + 1}`);

    personalDebts.forEach(debt => {
      const remainingBalance = debt.remainingBalance !== undefined ? debt.remainingBalance : debt.amount;
      if (!debt.isActive || remainingBalance <= 0) {
        return;
      }

      // Use the period-aware generateNextPaymentDate helper
      const nextPaymentDateStr = this.generateNextPaymentDate(debt.paymentDate, debt.id);
      const nextPaymentDateObj = new Date(nextPaymentDateStr + "T00:00:00");

      // Only generate if the calculated date falls within the target period
      if (nextPaymentDateObj.getFullYear() !== this.targetYear || nextPaymentDateObj.getMonth() !== this.targetMonth) {
        logger.debug(`Personal debt ${debt.name} payment date ${nextPaymentDateStr} is outside target period ${this.targetYear}-${this.targetMonth + 1}. Skipping.`);
        return;
      }
      
      // --- VALIDACIÓN DE FECHA DE CREACIÓN ---
      const targetPeriodEnd = new Date(this.targetYear, this.targetMonth + 1, 0, 23, 59, 59, 999);

      // Si es el mes de creación y la cuota calculada cae antes del createdAt, omitir para evitar falso vencido
      const isCreationMonthPD = debt.createdAt ? (() => {
        const created = new Date(debt.createdAt);
        return created.getFullYear() === this.targetYear && created.getMonth() === this.targetMonth;
      })() : false;

      if (isCreationMonthPD) {
        const createdDatePD = new Date(debt.createdAt!);
        if (nextPaymentDateObj < createdDatePD) {
          logger.debug(`PersonalDebt ${debt.id}: primera cuota ${nextPaymentDateStr} anterior a createdAt (${debt.createdAt}). Se omite para este período.`);
          return;
        }
      }

      if (debt.createdAt) {
        const createdAtDate = new Date(debt.createdAt);
        if (isNaN(createdAtDate.getTime())) {
          logger.warn(`Personal debt ${debt.id} tiene createdAt inválido (${debt.createdAt}). Se omite generación en este período.`);
          return;
        }

        // Si la deuda se creó después de que termina el período, no generar pago
        if (createdAtDate > targetPeriodEnd) {
          logger.debug(`Personal debt ${debt.id} creada el ${debt.createdAt} después del período ${this.targetYear}-${this.targetMonth + 1}. Se omite.`);
          return;
        }
      }

      let suggestedPayment: number;
      if (debt.monthlyBudget && debt.monthlyBudget > 0) {
        suggestedPayment = Math.min(debt.monthlyBudget, remainingBalance);
      } else {
        const minPayment = debt.currency === 'USD' ? 50 : 1000; // Example minimums
        const calculatedPayment = Math.max(remainingBalance * 0.05, minPayment);
        suggestedPayment = Math.min(calculatedPayment, remainingBalance);
      }

      payments.push({
        id: `debt-${debt.id}-${this.targetYear}-${this.targetMonth + 1}`,
        type: 'personal-debt',
        name: `Abono - ${debt.name}`,
        amount: suggestedPayment,
        currency: debt.currency,
        dueDate: nextPaymentDateStr,
        status: 'pending',
        referenceId: debt.id,
        createdAt: debt.createdAt
      });
    });
    logger.debug(`Personal debt payments generated for ${this.targetYear}-${this.targetMonth + 1}: ${payments.length}`);
    return payments;
  }

  generateFromExpenses(expenses: Expense[]): PaymentItem[] {
    const payments: PaymentItem[] = [];

    logger.debug('Generating expense payments (including paid and pending)...');
    logger.debug('Expenses found:', expenses.length);
    logger.debug('Target period:', { year: this.targetYear, month: this.targetMonth + 1 });
    logger.debug('First 3 expenses:', expenses.slice(0, 3).map(e => ({
      id: e.id,
      date: e.date,
      paymentDate: e.paymentDate,
      status: e.status,
      amount: e.amount,
      createdAt: e.createdAt
    })));

    expenses.forEach(expense => {
      const baseDueDate = expense.paymentDate ?? expense.date;

      if (!baseDueDate) {
        logger.warn(`Expense ${expense.id} (${expense.description}) lacks payment/date information. Skipping.`);
        return;
      }

      // --- CRITICAL TEMPORAL VALIDATION ---
      const targetYearMonth = `${this.targetYear}-${String(this.targetMonth + 1).padStart(2, '0')}`;
      const targetPeriodStart = new Date(this.targetYear, this.targetMonth, 1);
      const targetPeriodEnd = endOfMonth(this.targetYear, this.targetMonth);
      
      // VALIDATION 1: Check if expense has a creation date
      if (!expense.createdAt) {
        logger.warn(`TEMPORAL VALIDATION: Expense ${expense.id} (${expense.description}) lacks createdAt field. This is a data integrity issue. Skipping.`);
        return;
      }
      
      // VALIDATION 2: Ensure expense creation date is never after the target period
      const createdAtDate = stripTime(expense.createdAt);
      if (isNaN(createdAtDate.getTime())) {
        logger.warn(`TEMPORAL VALIDATION: Expense ${expense.id} has invalid createdAt date: ${expense.createdAt}. Skipping.`);
        return;
      }
      
      // Critical fix: A payment cannot exist in a month BEFORE its creation
      if (createdAtDate > targetPeriodEnd) {
        logger.debug(`TEMPORAL VALIDATION: Expense ${expense.id} (${expense.description}) created on ${expense.createdAt} cannot appear in period ending ${targetPeriodEnd.toISOString().slice(0,10)}. Skipping.`);
        return;
      }
      
      // Additional validation for creation month vs target month
      const createdYear = createdAtDate.getFullYear();
      const createdMonth = createdAtDate.getMonth(); // 0-11
      
      // If the expense was created in a later year/month than the target period, skip it
      if (createdYear > this.targetYear || (createdYear === this.targetYear && createdMonth > this.targetMonth)) {
        logger.debug(`TEMPORAL VALIDATION: Expense ${expense.id} created in ${createdYear}-${createdMonth + 1} cannot appear in ${this.targetYear}-${this.targetMonth + 1}. Skipping.`);
        return;
      }

      let dueDateStr: string;
      let paymentStatus = expense.status;
      const isCreationMonth = createdYear === this.targetYear && createdMonth === this.targetMonth;

      if (expense.isRecurring) {
        // For recurring expenses: only generate if creation date <= target period end
        // Additional check: ensure the expense existed before or during the target month
        const firstPossiblePayment = stripTime(`${createdYear}-${String(createdMonth+1).padStart(2,'0')}-${baseDueDate.slice(-2)}`);
        
        // If the first possible payment is after the target period, skip
        if (firstPossiblePayment > targetPeriodEnd) {
          logger.debug(`TEMPORAL VALIDATION: Recurring expense ${expense.id} first possible payment ${firstPossiblePayment.toISOString().slice(0,10)} is after target period. Skipping.`);
          return;
        }
        
        const candidateDateStr = this.generateNextPaymentDate(baseDueDate, expense.id);
        const candidateDateObj = stripTime(candidateDateStr);

        // Si estamos en el mes de creación y la fecha de pago propuesta ocurre antes de la creación,
        // no generar cuota para evitar mostrarse como vencida inmediatamente.
        if (isCreationMonth && candidateDateObj < createdAtDate) {
          logger.debug(`Recurring expense ${expense.id} – la cuota del mes de creación (${candidateDateStr}) es anterior a createdAt (${expense.createdAt}). Se aplaza al siguiente mes.`);
          return;
        }

        dueDateStr = candidateDateStr;
      } else {
        const dueDateObj = stripTime(baseDueDate);

        if (isCreationMonth) {
          // If created this month, allow due dates before creation but mark overdue
          if (dueDateObj > targetPeriodEnd) {
            logger.debug(`TEMPORAL VALIDATION: Non-recurring expense ${expense.id} due ${baseDueDate} occurs after creation month. Skipping for this period.`);
            return;
          }

          if (dueDateObj < createdAtDate) {
            paymentStatus = 'overdue';
          }

          dueDateStr = baseDueDate;
        } else {
          // Standard behaviour for months after creation
          if (dueDateObj.getFullYear() !== this.targetYear || dueDateObj.getMonth() !== this.targetMonth) {
            logger.debug(`TEMPORAL VALIDATION: Non-recurring expense ${expense.id} with due date ${baseDueDate} is outside target period ${this.targetYear}-${this.targetMonth + 1}. Skipping.`);
            return;
          }

          if (dueDateObj < createdAtDate) {
            logger.warn(`TEMPORAL VALIDATION: Non-recurring expense ${expense.id} has due date ${baseDueDate} before its creation date ${expense.createdAt}. This is a data integrity issue. Skipping.`);
            return;
          }

          dueDateStr = baseDueDate;
        }
      }

      // Final validation: Ensure the generated payment date makes sense
      const finalDueDate = stripTime(dueDateStr);
      if (!isCreationMonth && finalDueDate < createdAtDate) {
        logger.warn(`TEMPORAL VALIDATION: Final due date ${dueDateStr} is before creation date ${expense.createdAt} for expense ${expense.id}. Skipping.`);
        return;
      }

      payments.push({
        id: `expense-${expense.id}-${this.targetYear}-${this.targetMonth + 1}`,
        type: 'expense',
        name: `${expense.categoryName}${expense.description ? ' - ' + expense.description : ''}`,
        amount: expense.amount,
        currency: expense.currency,
        dueDate: dueDateStr,
        status: paymentStatus,
        referenceId: expense.id,
        createdAt: expense.createdAt // Propagar fecha de creación para validaciones posteriores
      });
    });

    logger.debug('Expense payments generated after enhanced temporal validation:', payments.length);
    return payments;
  }

  generateAllPayments(
    creditCards: CreditCard[],
    loans: Loan[],
    subscriptions: Subscription[],
    personalDebts: PersonalDebt[],
    expenses: Expense[]
  ): PaymentItem[] {
    const payments: PaymentItem[] = [
      ...this.generateFromCreditCards(creditCards),
      ...this.generateFromLoans(loans),
      ...this.generateFromSubscriptions(subscriptions),
      ...this.generateFromPersonalDebts(personalDebts),
      ...this.generateFromExpenses(expenses)
    ];

    logger.debug('Total payments generated:', payments.length);
    logger.debug('Personal debt payments:', payments.filter(p => p.type === 'personal-debt'));

    return payments;
  }
}
