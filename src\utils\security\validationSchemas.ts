import { z } from 'zod';
import { sanitizeEmail, sanitizeInput, sanitizeFinancialAmount, sanitizeCurrency, sanitizeDate } from './sanitization';
import { SecureErrorHandler } from './errorHandler';

export const emailSecuritySchema = z
  .string()
  .email('Email inválido')
  .min(5, 'Email demasiado corto')
  .max(254, 'Email demasiado largo')
  .transform(sanitizeEmail)
  .refine(
    (email) => !email.includes('..') && !email.startsWith('.') && !email.endsWith('.'),
    'Formato de email inválido'
  );

export const passwordSecuritySchema = z
  .string()
  .min(12, 'La contraseña debe tener al menos 12 caracteres')
  .max(128, 'La contraseña es demasiado larga')
  .refine((password) => /[a-z]/.test(password), 'Debe contener al menos una letra minúscula')
  .refine((password) => /[A-Z]/.test(password), 'Debe contener al menos una letra mayúscula')
  .refine((password) => /\d/.test(password), 'Debe contener al menos un número')
  .refine((password) => /[!@#$%^&*(),.?":{}|<>]/.test(password), 'Debe contener al menos un carácter especial')
  .refine((password) => !/(.)\1{2,}/.test(password), 'No debe contener caracteres repetidos consecutivos');

export const validateFinancialInput = (data: {
  amount?: number;
  description?: string;
  category?: string;
  currency?: string;
  date?: string;
}): { isValid: boolean; sanitized?: any; errors?: string[] } => {
  try {
    const sanitized = {
      amount: data.amount ? sanitizeFinancialAmount(data.amount) : 0,
      description: data.description ? sanitizeInput(data.description) : '',
      category: data.category ? sanitizeInput(data.category) : '',
      currency: data.currency ? sanitizeCurrency(data.currency) : 'DOP',
      date: data.date ? sanitizeDate(data.date) : new Date().toISOString().split('T')[0]
    };

    const errors: string[] = [];

    if (sanitized.amount <= 0) {
      errors.push('El monto debe ser mayor a 0');
    }

    if (sanitized.description.length < 1) {
      errors.push('La descripción es requerida');
    }

    if (sanitized.category.length < 1) {
      errors.push('La categoría es requerida');
    }

    return {
      isValid: errors.length === 0,
      sanitized: errors.length === 0 ? sanitized : undefined,
      errors: errors.length > 0 ? errors : undefined
    };
  } catch (error) {
    SecureErrorHandler.logError(error, 'financial_input_validation');
    return {
      isValid: false,
      errors: ['Error en la validación de datos']
    };
  }
};
