
import { logger } from "@/utils/logger";

import React, { useEffect } from 'react';
import { Navigate, useLocation, useNavigate, Link } from 'react-router-dom';
import { ROUTES } from '@/constants/routes';
import { DollarSign, Loader2 } from 'lucide-react';
import { AuthForm } from '@/components/auth/AuthForm';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';

// Internal component that uses useAuth
const AuthPageContent: React.FC = () => {
  const { user, loading, session } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  
  const intendedRedirectPath = location.state?.from?.pathname || ROUTES.APP_DASHBOARD;

  useEffect(() => {
    logger.debug('AuthPage: State changed - user:', !!user, 'session:', !!session, 'loading:', loading);
    
    // If authenticated and not loading, redirect immediately
    if (!loading && user && session) {
      // If the intended redirect path is the landing page, go to dashboard instead
      const finalRedirectPath = intendedRedirectPath === ROUTES.ROOT ? ROUTES.APP_DASHBOARD : intendedRedirectPath;
      logger.debug('AuthPage: User authenticated, redirecting to:', finalRedirectPath, '(intended:', intendedRedirectPath, ')');
      navigate(finalRedirectPath, { replace: true });
    }
  }, [user, session, loading, intendedRedirectPath, navigate]);

  // If authenticated, redirect (double check)
  // This logic should also respect the landing page override
  if (!loading && user && session) {
    const finalRedirectPath = intendedRedirectPath === ROUTES.ROOT ? ROUTES.APP_DASHBOARD : intendedRedirectPath;
    logger.debug('AuthPage: User authenticated in render, redirecting to:', finalRedirectPath, '(intended:', intendedRedirectPath, ')');
    return <Navigate to={finalRedirectPath} replace />;
  }

  // Show loading only if really loading
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center space-y-4">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 dark:text-blue-400 mx-auto" />
          <p className="text-gray-600 dark:text-gray-300 font-medium">Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Left Panel - Optimizado y movido hacia arriba */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 dark:from-blue-800 dark:via-blue-900 dark:to-gray-900 p-8 flex-col justify-start relative overflow-hidden">
        {/* Background Pattern optimizado */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-40 h-40 bg-white rounded-full -translate-x-20 -translate-y-20"></div>
          <div className="absolute top-1/4 right-0 w-32 h-32 bg-white rounded-full translate-x-16"></div>
          <div className="absolute bottom-1/4 left-1/4 w-24 h-24 bg-white rounded-full"></div>
          <div className="absolute bottom-0 right-0 w-48 h-48 bg-white rounded-full translate-x-24 translate-y-24"></div>
        </div>

        <div className="relative z-10 text-white pt-16">
          {/* Logo en la parte superior */}
          <div className="flex items-center space-x-4 mb-16">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-xl">
              <DollarSign className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">FinanzApp</h1>
              <p className="text-blue-100 font-medium">Tu gestor financiero</p>
            </div>
          </div>

          {/* Hero Content optimizado y movido arriba */}
          <div className="space-y-8">
            <h2 className="text-5xl font-bold leading-tight text-white">
              Controla tus finanzas
              <br />
              <span className="text-blue-200">como un profesional</span>
            </h2>
            <p className="text-xl text-blue-100 leading-relaxed max-w-lg">
              La plataforma más completa para gestionar tus ingresos, gastos, deudas y metas financieras con inteligencia artificial.
            </p>

            {/* Features optimizadas */}
            <div className="space-y-4 pt-8">
              {[
                'Gestión completa de ingresos y gastos',
                'Control inteligente de deudas',
                'Análisis financiero avanzado',
                'Metas y proyecciones automáticas'
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
                  <span className="text-blue-100 text-lg font-medium">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Auth Form optimizado */}
      <div className="flex-1 flex items-center justify-center p-6 bg-white dark:bg-gray-900">
        <div className="w-full max-w-md">
          {/* Mobile Logo optimizado */}
          <div className="lg:hidden text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">FinanzApp</h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">Tu gestor financiero</p>
              </div>
            </div>
          </div>

          <AuthForm />

          {/* Footer optimizado */}
          <div className="mt-8 text-center space-y-4">
            <div className="flex items-center justify-center space-x-4 text-xs text-gray-400 dark:text-gray-500">
              <Link to="/terms-of-service" className="hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                Términos de servicio
              </Link>
              <span>•</span>
              <Link to="/privacy-policy" className="hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                Política de privacidad
              </Link>
              <span>•</span>
              <Link to="/support" className="hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                Soporte
              </Link>
            </div>
            <p className="text-xs text-gray-400 dark:text-gray-500">
              © 2024 FinanzApp. Todos los derechos reservados.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main component that wraps with AuthProvider
export default function AuthPage() {
  return (
    <AuthProvider>
      <AuthPageContent />
    </AuthProvider>
  );
}
