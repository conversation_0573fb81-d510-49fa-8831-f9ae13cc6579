
import { useMemo, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabaseIncomes } from './useSupabaseIncomes';
import { useSupabaseExpenses } from './useSupabaseExpenses';
import { useSupabaseDebts } from './useSupabaseDebts';
import { useSupabaseData } from './useSupabaseData';
import { useSupabasePaymentRecords } from './useSupabasePaymentRecords';
import { useFinancialCalculations } from '@/nucleo_dominio_compartido/hooks/useFinancialCalculations';
import { useAuth } from '@/contexts/AuthContext';

// Cache keys para datos financieros
export const FINANCE_CACHE_KEYS = {
  incomes: 'finance-incomes',
  expenses: 'finance-expenses', 
  debts: 'finance-debts',
  other: 'finance-other',
  payments: 'finance-payments',
} as const;

// Hook optimizado que carga datos de forma inteligente
export const useOptimizedFinanceData = (requiredData?: string[]) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Determinar qué datos necesitamos cargar
  const needsIncomes = !requiredData || requiredData.includes('incomes');
  const needsExpenses = !requiredData || requiredData.includes('expenses');
  const needsDebts = !requiredData || requiredData.includes('debts');
  const needsOther = !requiredData || requiredData.some(d => ['subscriptions', 'reimbursements', 'goals'].includes(d));
  const needsPayments = !requiredData || requiredData.includes('payments');

  // Hooks de datos con cache optimizado
  const incomeData = useSupabaseIncomes({ 
    enabled: needsIncomes && !!user?.id
  });

  const expenseData = useSupabaseExpenses({ 
    enabled: needsExpenses && !!user?.id
  });

  const debtData = useSupabaseDebts({ 
    enabled: needsDebts && !!user?.id
  });

  const otherData = useSupabaseData({ 
    enabled: needsOther && !!user?.id
  });

  const paymentData = useSupabasePaymentRecords({ 
    enabled: needsPayments && !!user?.id
  });

  // Cálculos financieros memoizados
  const calculations = useFinancialCalculations(
    incomeData.incomes || [],
    expenseData.expenses || [],
    debtData.creditCards || [],
    debtData.loans || [],
    debtData.personalDebts || [],
    otherData.reimbursements || []
  );

  // Prefetch de datos relacionados en background
  const prefetchRelatedData = useMemo(() => {
    return () => {
      if (!user?.id) return;

      // Si estamos en dashboard, prefetch datos de otras páginas
      if (!requiredData || requiredData.includes('dashboard')) {
        queryClient.prefetchQuery({
          queryKey: ['expenses', user.id],
          staleTime: 1000 * 60 * 10,
        });
        queryClient.prefetchQuery({
          queryKey: ['debts', user.id],
          staleTime: 1000 * 60 * 10,
        });
      }
    };
  }, [user?.id, requiredData, queryClient]);

  // Ejecutar prefetch utilizando useEffect
  useEffect(() => {
    const timer = setTimeout(prefetchRelatedData, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [prefetchRelatedData]);

  // Datos memoizados para evitar re-renders
  const memoizedData = useMemo(() => ({
    // Datos principales
    incomes: incomeData.incomes || [],
    expenses: expenseData.expenses || [],
    creditCards: debtData.creditCards || [],
    loans: debtData.loans || [],
    personalDebts: debtData.personalDebts || [],
    personalDebtPayments: debtData.personalDebtPayments || [],
    subscriptions: otherData.subscriptions || [],
    reimbursements: otherData.reimbursements || [],
    financialGoals: otherData.financialGoals || [],
    paymentRecords: paymentData.paymentRecords || [],
    defaultCurrency: otherData.defaultCurrency,

    // Estados de carga
    isLoading: incomeData.isLoading || expenseData.isLoading || debtData.isLoading || otherData.isLoading || paymentData.isLoading,
    
    // Cálculos
    ...calculations,

    // Acciones
    ...incomeData,
    ...expenseData,
    ...debtData,
    ...otherData,
    ...paymentData,
  }), [
    incomeData,
    expenseData,
    debtData,
    otherData,
    paymentData,
    calculations
  ]);

  return memoizedData;
};
