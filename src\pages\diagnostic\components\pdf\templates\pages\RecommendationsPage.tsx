
import React from 'react';
import { usePDFDataEngine } from '../../core/PDFDataEngine';
import { createPageStyle, createHeaderStyle, createCardStyle, createBaseStyle, pdfStyles } from '../../styles/pdfStyles';

export const RecommendationsPage: React.FC = () => {
  const { groupedRecommendations /*, formatCurrency*/ } = usePDFDataEngine(); // formatCurrency removed

  const getTimeframeConfig = (timeframe: string) => {
    const configs = {
      immediate: {
        title: '🚨 Acción Inmediata (0-30 días)',
        backgroundColor: '#FEF2F2',
        borderColor: '#FECACA',
        titleColor: '#DC2626',
        textColor: '#7F1D1D'
      },
      short: {
        title: '🎯 <PERSON><PERSON><PERSON> (1-6 meses)',
        backgroundColor: '#FFFBEB',
        borderColor: '#FED7AA',
        titleColor: '#D97706',
        textColor: '#78350F'
      },
      medium: {
        title: '🌟 Mediano Plazo (6-18 meses)',
        backgroundColor: '#EFF6FF',
        borderColor: '#BFDBFE',
        titleColor: '#2563EB',
        textColor: '#1E3A8A'
      }
    };
    return configs[timeframe as keyof typeof configs] || configs.medium;
  };

  return (
    <div style={createPageStyle()}>
      {/* Header */}
      <div style={createHeaderStyle({ textAlign: 'center' })}>
        <h1 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize['3xl'], 
          fontWeight: '700',
          margin: '0 0 6px 0'
        })}>
          Plan Estratégico de Recomendaciones
        </h1>
        <p style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.lg,
          color: pdfStyles.colors.neutral[500],
          margin: 0
        })}>
          Hoja de ruta personalizada para optimizar su salud financiera
        </p>
      </div>

      {/* Recomendaciones por Marco Temporal */}
      {Object.entries(groupedRecommendations).map(([timeframe, recommendations]) => {
        if (recommendations.length === 0) return null;
        
        const config = getTimeframeConfig(timeframe);
        
        return (
          <div key={timeframe} style={{ marginBottom: pdfStyles.spacing['4xl'] }}>
            <h2 style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.xl, 
              fontWeight: '700', 
              color: config.titleColor,
              marginBottom: pdfStyles.spacing.lg,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px'
            })}>
              {config.title}
            </h2>
            
            {recommendations.map((rec, index) => (
              <div key={index} style={{
                backgroundColor: config.backgroundColor,
                border: `2px solid ${config.borderColor}`,
                borderRadius: '8px',
                padding: pdfStyles.spacing.lg,
                marginBottom: pdfStyles.spacing.md
              }}>
                <h3 style={createBaseStyle({ 
                  fontSize: pdfStyles.typography.fontSize.lg, 
                  fontWeight: '600',
                  color: config.titleColor,
                  marginBottom: '6px'
                })}>
                  {rec.title}
                </h3>
                <p style={createBaseStyle({ 
                  color: config.textColor,
                  marginBottom: '8px',
                  lineHeight: pdfStyles.typography.lineHeight.normal
                })}>
                  {rec.description}
                </p>
                <div style={{ display: 'flex', gap: pdfStyles.spacing['2xl'], fontSize: pdfStyles.typography.fontSize.xs }}>
                  <span style={createBaseStyle({ color: pdfStyles.colors.success, fontWeight: '600' })}>
                    💰 Impacto: {rec.impact}
                  </span>
                  <span style={createBaseStyle({ color: '#7C2D12', fontWeight: '600' })}>
                    ⚡ Esfuerzo: {rec.effort}
                  </span>
                </div>
              </div>
            ))}
          </div>
        );
      })}

      {/* Métricas de Éxito */}
      <div style={createCardStyle({
        backgroundColor: '#F0FDF4',
        borderColor: '#10B981',
        borderWidth: '2px',
        borderRadius: '12px'
      })}>
        <h3 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700', 
          color: '#065F46',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center'
        })}>
          📈 Métricas de Éxito Proyectadas
        </h3>
        
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(3, 1fr)', 
          gap: pdfStyles.spacing.lg,
          textAlign: 'center'
        }}>
          <div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize['4xl'], fontWeight: '700', color: '#065F46' })}>
              92%
            </div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.sm, color: '#047857', fontWeight: '500' })}>
              Probabilidad de Éxito
            </div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.xs, color: '#059669', marginTop: '3px' })}>
              Siguiendo el plan completo
            </div>
          </div>
          <div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize['4xl'], fontWeight: '700', color: '#065F46' })}>
              RD$180K
            </div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.sm, color: '#047857', fontWeight: '500' })}>
              Ahorro Proyectado
            </div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.xs, color: '#059669', marginTop: '3px' })}>
              En los próximos 18 meses
            </div>
          </div>
          <div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize['4xl'], fontWeight: '700', color: '#065F46' })}>
              15 meses
            </div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.sm, color: '#047857', fontWeight: '500' })}>
              Tiempo al Objetivo
            </div>
            <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.xs, color: '#059669', marginTop: '3px' })}>
              Puntuación financiera 85+
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
