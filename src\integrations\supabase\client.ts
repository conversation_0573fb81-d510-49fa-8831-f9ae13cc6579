import { logger } from "@/utils/logger";

import { createClient } from '@supabase/supabase-js';
import { configManager } from '@/utils/config/environment';
import { setupGlobalErrorHandler } from '@/utils/security/errorHandler';

// Initialize configuration
const config = configManager.getConfig();

// Create Supabase client sin headers problemáticos que causan CORS
export const supabase = createClient(
  config.supabase.url,
  config.supabase.anonKey,
  {
    auth: {
      // Store auth session in sessionStorage so closing the browser clears it
      storage: sessionStorage,
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true
    },
    global: {
      headers: {
        // Solo headers esenciales para evitar problemas de CORS
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    }
  }
);

// Setup global error handling
setupGlobalErrorHandler();

// Enhanced session monitoring
supabase.auth.onAuthStateChange((event) => {
  if (event === 'SIGNED_OUT') {
    // Clear all client-side data on sign out
    localStorage.removeItem('finanz_last_activity');
    sessionStorage.clear();
  }
  
  if (event === 'TOKEN_REFRESHED') {
    logger.debug('Session token refreshed successfully');
  }
});
