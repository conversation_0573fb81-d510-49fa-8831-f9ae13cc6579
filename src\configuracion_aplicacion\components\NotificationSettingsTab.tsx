
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Form, FormControl, FormField, FormLabel } from '@/components/ui/form';
import { Bell, Loader2 } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { NotificationFormData } from '../schemas/settingsSchemas';

interface NotificationSettingsTabProps {
  notificationForm: UseFormReturn<NotificationFormData>;
  saveNotifications: (data: NotificationFormData) => Promise<void>;
  isLoading: boolean;
}

export const NotificationSettingsTab: React.FC<NotificationSettingsTabProps> = ({
  notificationForm,
  saveNotifications,
  isLoading,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Configuración de Notificaciones
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...notificationForm}>
          <form onSubmit={notificationForm.handleSubmit(saveNotifications)} className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Recordatorios de Pagos</h3>
              <div className="space-y-3">
                <FormField
                  control={notificationForm.control}
                  name="paymentReminders"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Recordatorios de Pagos Pendientes</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Recibe notificaciones sobre pagos próximos a vencer</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
                
                <FormField
                  control={notificationForm.control}
                  name="overduePayments"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Pagos Vencidos</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Alertas sobre pagos que han vencido</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Presupuesto y Metas</h3>
              <div className="space-y-3">
                <FormField
                  control={notificationForm.control}
                  name="budgetAlerts"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Alertas de Presupuesto</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Notificaciones cuando te acerques a límites de gastos</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
                
                <FormField
                  control={notificationForm.control}
                  name="goalProgress"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Progreso de Metas</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Actualizaciones sobre el progreso de tus metas financieras</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Reportes</h3>
              <div className="space-y-3">
                <FormField
                  control={notificationForm.control}
                  name="weeklyReports"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Reportes Semanales</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Resumen semanal de tu actividad financiera</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
                
                <FormField
                  control={notificationForm.control}
                  name="monthlyReports"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Reportes Mensuales</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Análisis mensual completo de tus finanzas</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />

                <FormField
                  control={notificationForm.control}
                  name="emailReports"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Reportes por Email</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Recibe reportes detallados en tu correo electrónico</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />

                <FormField
                  control={notificationForm.control}
                  name="pushNotifications"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Notificaciones Push</FormLabel>
                        <p className="text-sm text-finanz-text-secondary">Permitir notificaciones del navegador</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
              </div>
            </div>

            <Button type="submit" disabled={isLoading} className="w-full md:w-auto">
              {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Guardar Configuración
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
