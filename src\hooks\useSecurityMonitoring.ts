
import { useEffect, useRef } from 'react';
import { useEnhancedSecurity } from './useEnhancedSecurity';
import { useAuth } from '@/contexts/AuthContext';

export const useSecurityMonitoring = () => {
  const { user } = useAuth();
  const { logEnhancedSecurityEvent, performSecurityHealthCheck } = useEnhancedSecurity();
  const lastHealthCheck = useRef<Date>(new Date());

  // Monitor for security events
  useEffect(() => {
    if (!user) return;

    // Log page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        logEnhancedSecurityEvent('data_access', 'Page hidden/minimized', 'low');
      } else {
        logEnhancedSecurityEvent('data_access', 'Page visible/restored', 'low');
      }
    };

    // Monitor failed network requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        if (!response.ok && response.status === 401) {
          logEnhancedSecurityEvent('data_access', `Unauthorized request to ${args[0]}`, 'high');
        }
        return response;
      } catch (error) {
        logEnhancedSecurityEvent('data_access', `Failed request to ${args[0]}`, 'medium');
        throw error;
      }
    };

    // Periodic security health checks
    const healthCheckInterval = setInterval(() => {
      const now = new Date();
      const timeSinceLastCheck = now.getTime() - lastHealthCheck.current.getTime();
      
      // Only check every 5 minutes
      if (timeSinceLastCheck > 5 * 60 * 1000) {
        const healthCheck = performSecurityHealthCheck();
        if (!healthCheck.isHealthy) {
          logEnhancedSecurityEvent(
            'data_access', 
            `Security health check failed: ${healthCheck.issues.join(', ')}`, 
            healthCheck.riskLevel as 'low' | 'medium' | 'high'
          );
        }
        lastHealthCheck.current = now;
      }
    }, 60000); // Check every minute

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(healthCheckInterval);
      window.fetch = originalFetch;
    };
  }, [user, logEnhancedSecurityEvent, performSecurityHealthCheck]);
};
