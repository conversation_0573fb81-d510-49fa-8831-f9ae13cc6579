
import { describe, it, expect, vi } from 'vitest'
import { PaymentActionService } from '@/seguimiento_pagos/services/paymentActionService'
import { PaymentItem } from '@/seguimiento_pagos/types/paymentTypes'
import { PersonalDebt } from '@/types'

vi.mock('@/utils/logger', () => ({
  logger: { debug: vi.fn(), error: vi.fn(), info: vi.fn(), warn: vi.fn() }
}))

const basePayment: PaymentItem = {
  id: 'p1',
  type: 'expense',
  name: 'Test',
  amount: 100,
  currency: 'DOP',
  dueDate: '2024-01-01',
  status: 'pending',
  referenceId: 'ref1',
  recordId: 'rec1'
}

const emptyDebts: PersonalDebt[] = []

describe('PaymentActionService.handleMarkAsPaid', () => {
  it('passes notes when provided', async () => {
    const service = new PaymentActionService()
    const markPaymentAsPaid = vi.fn()
    const addPaymentRecord = vi.fn()

    await service.handleMarkAsPaid(
      basePayment,
      emptyDebts,
      vi.fn(),
      vi.fn(),
      markPaymentAsPaid,
      addPaymentRecord,
      { notes: 'hello', totalAmount: 100 }
    )

    expect(markPaymentAsPaid).toHaveBeenCalledWith('rec1', expect.any(String), 'hello')
    expect(addPaymentRecord).not.toHaveBeenCalled()
  })

  it('updates status without notes', async () => {
    const service = new PaymentActionService()
    const markPaymentAsPaid = vi.fn()
    const addPaymentRecord = vi.fn()

    await service.handleMarkAsPaid(
      basePayment,
      emptyDebts,
      vi.fn(),
      vi.fn(),
      markPaymentAsPaid,
      addPaymentRecord
    )

    expect(markPaymentAsPaid).toHaveBeenCalledWith('rec1', expect.any(String), null)
    expect(addPaymentRecord).not.toHaveBeenCalled()
  })
})
