import React from 'react';
import { Calendar, TrendingUp, Edit, Trash2, DollarSign, History } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { formatCurrency } from '@/components/ui/numeric-input';
import { FinancialGoal, GoalContribution } from '@/types';

interface GoalCardProps {
  goal: FinancialGoal;
  contributions: GoalContribution[];
  onEdit: (goal: FinancialGoal) => void;
  onDelete: (goalId: string) => void;
  onAddContribution: (goal: FinancialGoal) => void;
  onShowHistory: (goal: FinancialGoal) => void;
}

export function GoalCard({ goal, contributions, onEdit, onDelete, onAddContribution, onShowHistory }: GoalCardProps) {
  const getGoalProgress = (goal: FinancialGoal) => {
    return (goal.currentAmount / goal.amount) * 100;
  };

  const getGoalStatus = (goal: FinancialGoal) => {
    const progress = getGoalProgress(goal);
    if (progress >= 100) return 'Completada';
    if (progress >= 75) return 'Casi Completada';
    if (progress >= 25) return 'En Progreso';
    return 'Iniciando';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Completada':
        return <Badge className="bg-green-100 text-green-800">Completada</Badge>;
      case 'Casi Completada':
        return <Badge className="bg-blue-100 text-blue-800">Casi Completada</Badge>;
      case 'En Progreso':
        return <Badge className="bg-yellow-100 text-yellow-800">En Progreso</Badge>;
      case 'Iniciando':
        return <Badge variant="secondary">Iniciando</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority?: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">Alta</Badge>;
      case 'medium':
        return <Badge variant="secondary">Media</Badge>;
      case 'low':
        return <Badge variant="outline">Baja</Badge>;
      default:
        return <Badge variant="outline">Media</Badge>;
    }
  };

  const progress = getGoalProgress(goal);
  const goalStatus = getGoalStatus(goal);
  const daysToTarget = goal.targetDate ? 
    Math.ceil((new Date(goal.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : null;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-1 flex-1">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold text-finanz-text-primary">{goal.name}</h4>
                {getStatusBadge(goalStatus)}
                {getPriorityBadge(goal.priority)}
              </div>
              <p className="text-sm text-finanz-text-secondary">{goal.category || 'Sin categoría'}</p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAddContribution(goal)}
                disabled={progress >= 100}
                className="gap-2"
              >
                <DollarSign className="w-4 h-4" />
                Agregar
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onShowHistory(goal)}
                className="gap-2"
              >
                <History className="w-4 h-4" />
                Historial
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(goal)}
                className="gap-2"
              >
                <Edit className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(goal.id)}
                className="gap-2 text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="text-right">
            <div className="text-lg font-bold text-finanz-primary">
              {formatCurrency(goal.currentAmount, goal.currency)}
            </div>
            <div className="text-sm text-finanz-text-secondary">
              de {formatCurrency(goal.amount, goal.currency)}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progreso</span>
              <span>{progress.toFixed(1)}%</span>
            </div>
            <Progress
              value={Math.min(progress, 100)}
              className="h-2"
              indicatorClassName="bg-finanz-primary"
            />
          </div>

          <div className="flex items-center justify-between text-sm text-finanz-text-secondary">
            <div className="flex items-center gap-1">
              <TrendingUp className="w-4 h-4" />
              <span>Restante: {formatCurrency(Math.max(0, goal.amount - goal.currentAmount), goal.currency)}</span>
            </div>
            <div className="flex items-center gap-4">
              {contributions.length > 0 && (
                <span>{contributions.length} contribuciones</span>
              )}
              {goal.targetDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>
                    {daysToTarget !== null && daysToTarget > 0 
                      ? `${daysToTarget} días restantes`
                      : daysToTarget === 0 
                      ? 'Vence hoy'
                      : 'Fecha pasada'
                    }
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
