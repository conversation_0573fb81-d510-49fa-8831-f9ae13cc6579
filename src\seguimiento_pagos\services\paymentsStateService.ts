
import { logger } from "@/utils/logger";
import { QueryClient } from '@tanstack/react-query';
import { PaymentItem } from '../types/paymentTypes';
import { PaymentRecord } from '@/types';
import { QUERY_KEYS } from '@/constants/queryKeys';

export class PaymentsStateService {
  private queryClient: QueryClient;
  private userId: string;

  constructor(queryClient: QueryClient, userId: string) {
    this.queryClient = queryClient;
    this.userId = userId;
  }

  // Invalidar todas las queries relacionadas con pagos de forma optimizada
  async invalidateAllPaymentQueries(): Promise<void> {
    logger.debug('PaymentsStateService: Invalidating all payment-related queries');
    
    const queryKeysToInvalidate = [
      [QUERY_KEYS.PAYMENT_RECORDS, this.userId],
      [QUERY_KEYS.DEBTS, this.userId],
      [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, this.userId],
      [QUERY_KEYS.EXPENSES, this.userId],
      [QUERY_KEYS.LOANS, this.userId],
      [QUERY_KEYS.SUBSCRIPTIONS, this.userId],
      [QUERY_KEYS.OTHER_DATA, this.userId]
    ];

    // Invalidar en paralelo para mejor rendimiento
    await Promise.all(
      queryKeysToInvalidate.map(queryKey =>
        this.queryClient.invalidateQueries({ queryKey, exact: true })
      )
    );

    // Refetch crítico de payment records
    await this.queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.PAYMENT_RECORDS, this.userId],
      exact: true
    });

    logger.debug('PaymentsStateService: All queries invalidated successfully');
  }

  // Actualización optimista para mejorar UX
  updatePaymentOptimistically(payment: PaymentItem, updates: Partial<PaymentRecord>): void {
    if (!payment.recordId) return;

    const queryKey = ['payment-records', this.userId];
    
    this.queryClient.setQueryData<PaymentRecord[]>(queryKey, records => {
      if (!records) return records;
      
      return records.map(record =>
        record.id === payment.recordId
          ? { ...record, ...updates }
          : record
      );
    });

    logger.debug('PaymentsStateService: Optimistic update applied', { paymentId: payment.id, updates });
  }

  // Revertir actualización optimista en caso de error
  revertOptimisticUpdate(previousData: PaymentRecord[] | undefined): void {
    if (!previousData) return;

    const queryKey = ['payment-records', this.userId];
    this.queryClient.setQueryData(queryKey, previousData);
    
    logger.debug('PaymentsStateService: Optimistic update reverted');
  }

  // Obtener datos previos para rollback
  getPreviousPaymentRecords(): PaymentRecord[] | undefined {
    const queryKey = ['payment-records', this.userId];
    return this.queryClient.getQueryData<PaymentRecord[]>(queryKey);
  }
}
