
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle2, 
  AlertTriangle, 
  Info,
  Lightbulb
} from 'lucide-react';

interface AdviceItem {
  id: string;
  title: string;
  description: string;
  type: 'success' | 'warning' | 'danger' | 'info';
  category: 'savings' | 'debt' | 'budget' | 'investment' | 'emergency';
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
}

interface AdviceDetailModalHeaderProps {
  advice: AdviceItem;
}

export const AdviceDetailModalHeader: React.FC<AdviceDetailModalHeaderProps> = ({
  advice
}) => {
  const getTypeIcon = (type: string) => {
    const iconClasses = "w-6 h-6";
    switch (type) {
      case 'success': return <CheckCircle2 className={`${iconClasses} text-green-600 dark:text-green-400`} />;
      case 'warning': return <AlertTriangle className={`${iconClasses} text-yellow-600 dark:text-yellow-400`} />;
      case 'danger': return <AlertTriangle className={`${iconClasses} text-red-600 dark:text-red-400`} />;
      case 'info': return <Info className={`${iconClasses} text-blue-600 dark:text-blue-400`} />;
      default: return <Info className={`${iconClasses} text-gray-600 dark:text-gray-400`} />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high': return <Badge variant="destructive">Alta Prioridad</Badge>;
      case 'medium': return <Badge variant="secondary">Media Prioridad</Badge>;
      case 'low': return <Badge variant="outline">Baja Prioridad</Badge>;
      default: return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <>
      <div className="flex items-center gap-3">
        {getTypeIcon(advice.type)}
        <div>
          <div className="text-xl font-bold text-foreground">{advice.title}</div>
          <div className="flex items-center gap-2 mt-1">
            {getPriorityBadge(advice.priority)}
            <Badge variant="outline" className="capitalize">{advice.category}</Badge>
          </div>
        </div>
      </div>
      <div className="text-base text-foreground">
        {advice.description}
      </div>

      {advice.recommendation && (
        <div className="bg-finanz-primary/5 border border-finanz-primary/20 rounded-lg p-4 mt-4">
          <div className="flex items-start gap-3">
            <Lightbulb className="w-5 h-5 text-finanz-primary dark:text-finanz-primary mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-finanz-primary dark:text-finanz-primary mb-2">Recomendación Principal</h4>
              <p className="text-foreground">{advice.recommendation}</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
