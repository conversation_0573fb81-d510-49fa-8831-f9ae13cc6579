
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Quote, User } from 'lucide-react';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export function TestimonialsSection() {
  const { isMobile, isTablet } = useBreakpoint();
  
  const testimonials = [
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      image: "https://this-person-does-not-exist.com/img/avatar-gen11b8e5f8b5c4c5d8f5e5f5e5f5e5f5e5.jpg",
      content: "FinanzApp transformó completamente mi manera de gestionar las finanzas familiares. Ahora tengo control total de nuestros gastos y hemos logrado ahorrar un 30% más cada mes.",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Empresario", 
      image: "https://this-person-does-not-exist.com/img/avatar-gen115f8e5f5c4c5d8f5e5f5e5f5e5f5e5.jpg",
      content: "La funcionalidad de análisis de deudas me ayudó a desarrollar una estrategia que me permitió liberarme de mis préstamos 2 años antes de lo planeado.",
      rating: 5
    },
    {
      name: "Ana Martínez",
      role: "Profesora",
      image: "https://this-person-does-not-exist.com/img/avatar-gen112f8e5f5c4c5d8f5e5f5e5f5e5f5e5.jpg",
      content: "Las metas financieras automatizadas me motivaron a alcanzar mis objetivos de ahorro. Ya compré mi primera casa gracias a la planificación que logré con la app.",
      rating: 5
    }
  ];

  return (
    <section className={`
      bg-white relative overflow-hidden
      ${isMobile ? 'py-12' : isTablet ? 'py-14' : 'py-16'}
    `}>
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-blue-50/30"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-100/40 to-purple-100/40 rounded-full blur-3xl"></div>
      
      <div className={`
        container mx-auto relative z-10
        ${isMobile ? 'px-4' : 'px-4'}
      `}>
        <div className="text-center mb-8 md:mb-12">
          <div className={`
            inline-flex items-center space-x-2 bg-gradient-to-r from-amber-100 to-orange-100 rounded-full border border-amber-200/50 mb-3
            ${isMobile ? 'px-3 py-1 mb-2' : 'px-5 py-2 mb-4'}
          `}>
            <Star className="w-4 h-4 text-amber-600" />
            <span className={`
              font-semibold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent
              ${isMobile ? 'text-xs' : 'text-sm'}
            `}>
              Testimonios
            </span>
          </div>
          <h2 className={`
            font-bold text-gray-900 mb-3
            ${isMobile ? 'text-2xl mb-2' : isTablet ? 'text-3xl mb-3' : 'text-3xl md:text-4xl mb-4'}
          `}>
            Lo que dicen nuestros{' '}
            <span className="bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
              usuarios
            </span>
          </h2>
          <p className={`
            text-gray-600 max-w-2xl mx-auto font-medium
            ${isMobile ? 'text-sm' : isTablet ? 'text-base' : 'text-lg'}
          `}>
            Miles de personas ya han transformado su vida financiera con FinanzApp
          </p>
        </div>
        
        <div className={`
          grid max-w-5xl mx-auto
          ${isMobile ? 'grid-cols-1 gap-4' : isTablet ? 'grid-cols-2 gap-5' : 'grid-cols-1 md:grid-cols-3 gap-6'}
        `}>
          {testimonials.map((testimonial, index) => (
            <Card key={index} className={`
              group border-0 shadow-xl hover:shadow-2xl transition-all duration-500 bg-white hover:-translate-y-2 rounded-2xl overflow-hidden relative
              ${isMobile ? 'hover:-translate-y-1' : ''}
            `}>
              {/* Quote icon */}
              <div className="absolute top-4 right-4 w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                <Quote className="w-5 h-5 text-white" />
              </div>
              
              <CardContent className={`
                ${isMobile ? 'p-4' : 'p-6'}
              `}>
                {/* Rating stars */}
                <div className="flex items-center space-x-1 mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-amber-400 fill-current" />
                  ))}
                </div>
                
                {/* Testimonial content */}
                <p className={`
                  text-gray-700 leading-relaxed italic mb-4
                  ${isMobile ? 'text-xs mb-3' : 'text-sm mb-6'}
                `}>
                  "{testimonial.content}"
                </p>
                
                {/* User info */}
                <div className="flex items-center space-x-3">
                  <div className={`
                    rounded-xl bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center ring-2 ring-white shadow-lg overflow-hidden
                    ${isMobile ? 'w-8 h-8' : 'w-10 h-10'}
                  `}>
                    <img 
                      src={testimonial.image} 
                      alt={testimonial.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                    <User className={`
                      text-blue-500 hidden
                      ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                    `} />
                  </div>
                  <div>
                    <h4 className={`
                      font-bold text-gray-900
                      ${isMobile ? 'text-xs' : 'text-sm'}
                    `}>
                      {testimonial.name}
                    </h4>
                    <p className={`
                      text-gray-500 font-medium
                      ${isMobile ? 'text-xs' : 'text-xs'}
                    `}>
                      {testimonial.role}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
