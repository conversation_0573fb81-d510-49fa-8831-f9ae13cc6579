import React, { useState } from 'react';
import { Users, DollarSign, Calendar } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFinanceData } from '@/hooks/useFinanceData';
import { SubscriptionsSection } from '@/gestion_suscripciones/components/SubscriptionsSection';
import { formatCurrency } from '@/components/ui/numeric-input';

export default function SubscriptionsPage() {
  // Solo cargar datos de suscripciones
  const { subscriptions, isLoading } = useFinanceData(['subscriptions']);
  const [activeTab, setActiveTab] = useState('overview');

  const activeSubscriptions = subscriptions.filter(sub => sub.isActive);
  const inactiveSubscriptions = subscriptions.filter(sub => !sub.isActive);
  
  const totalMonthly = activeSubscriptions.reduce((total, sub) => 
    total + (sub.amount || 0), 0
  );

  const totalAnnual = totalMonthly * 12;

  // Próximos vencimientos (próximos 7 días)
  const today = new Date();
  const nextWeek = new Date();
  nextWeek.setDate(today.getDate() + 7);
  
  const upcomingSubscriptions = activeSubscriptions.filter(sub => {
    const billingDay = parseInt(sub.billingDate);
    const nextBilling = new Date();
    nextBilling.setDate(billingDay);
    
    if (nextBilling < today) {
      nextBilling.setMonth(nextBilling.getMonth() + 1);
    }
    
    return nextBilling <= nextWeek;
  });

  if (isLoading && subscriptions.length === 0) {
    return (
      <div className="p-6 space-y-6 animate-pulse">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-48"></div>
          </div>
          <div className="w-8 h-8 bg-gray-200 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Suscripciones</h1>
          <p className="text-finanz-text-secondary">
            Gestiona tus suscripciones y pagos recurrentes
          </p>
        </div>
        <Users className="w-8 h-8 text-finanz-pink" />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="active">
            Activas ({activeSubscriptions.length})
          </TabsTrigger>
          <TabsTrigger value="inactive">
            Inactivas ({inactiveSubscriptions.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-finanz-pink" />
                  Gasto Mensual
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-finanz-pink">
                  {formatCurrency(totalMonthly)}
                </div>
                <p className="text-sm text-finanz-text-secondary mt-1">
                  {activeSubscriptions.length} suscripción(es) activa(s)
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-finanz-warning" />
                  Gasto Anual
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-finanz-warning">
                  {formatCurrency(totalAnnual)}
                </div>
                <p className="text-sm text-finanz-text-secondary mt-1">
                  Proyección anual
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-finanz-danger" />
                  Próximos Pagos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-finanz-danger">
                  {upcomingSubscriptions.length}
                </div>
                <p className="text-sm text-finanz-text-secondary mt-1">
                  En los próximos 7 días
                </p>
              </CardContent>
            </Card>
          </div>

          {upcomingSubscriptions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Próximos Vencimientos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {upcomingSubscriptions.map((subscription) => {
                    const billingDay = parseInt(subscription.billingDate);
                    const nextBilling = new Date();
                    nextBilling.setDate(billingDay);
                    
                    if (nextBilling < today) {
                      nextBilling.setMonth(nextBilling.getMonth() + 1);
                    }
                    
                    const daysUntil = Math.ceil((nextBilling.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                    
                    return (
                      <div key={subscription.id} className="flex justify-between items-center p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{subscription.name}</p>
                          <p className="text-sm text-finanz-text-secondary">
                            {subscription.categoryName}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-finanz-pink">
                            {formatCurrency(subscription.amount, subscription.currency)}
                          </p>
                          <p className="text-sm text-finanz-text-secondary">
                            {daysUntil === 0 ? 'Hoy' : `${daysUntil} día(s)`}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="active">
          <SubscriptionsSection showInactive={false} />
        </TabsContent>

        <TabsContent value="inactive">
          <SubscriptionsSection showInactive={true} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
