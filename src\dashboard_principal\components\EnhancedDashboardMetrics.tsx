
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, DollarSign, CreditCard, Target, AlertTriangle } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface EnhancedDashboardMetricsProps {
  netIncome: number;
  totalMonthlyPayments: number;
  netBalance: number;
  totalDebt: number;
  pendingReimbursements: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
  paymentToIncomeRatio: number;
  paymentBreakdown: {
    pending: number;
    overdue: number;
    paid: number;
  };
  previousMonthData?: {
    netIncome: number;
    totalMonthlyPayments: number;
    netBalance: number;
  };
}

export function EnhancedDashboardMetrics({
  netIncome,
  totalMonthlyPayments,
  netBalance,
  // totalDebt, // Unused
  // pendingReimbursements, // Unused
  savingsRate,
  // debtToIncomeRatio, // Unused
  // emergencyFundMonths, // Unused
  paymentToIncomeRatio,
  paymentBreakdown,
  previousMonthData
}: EnhancedDashboardMetricsProps) {
  
  const { isMobile, isTablet } = useBreakpoint();
  
  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const incomeGrowth = previousMonthData ? calculateGrowth(netIncome, previousMonthData.netIncome) : 0;
  const paymentsGrowth = previousMonthData ? calculateGrowth(totalMonthlyPayments, previousMonthData.totalMonthlyPayments) : 0;
  const balanceGrowth = previousMonthData ? calculateGrowth(netBalance, previousMonthData.netBalance) : 0;

  return (
    <div className={`
      grid gap-2 md:gap-4 lg:gap-6
      ${isMobile ? 'grid-cols-1' : isTablet ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}
    `}>
      {/* Ingresos Netos */}
      <Card className={`
        relative overflow-hidden hover:shadow-lg transition-shadow duration-300
        ${isMobile ? 'border-l-4 border-l-green-500' : ''}
      `}>
        <CardHeader className={`
          flex flex-row items-center justify-between space-y-0
          ${isMobile ? 'pb-1 px-3 pt-3' : 'pb-2'}
        `}>
          <CardTitle className={`
            font-medium text-gray-600 dark:text-gray-300
            ${isMobile ? 'text-xs' : 'text-sm'}
          `}>
            {isMobile ? 'Ingresos' : 'Ingresos Netos'}
          </CardTitle>
          <DollarSign className={`
            text-green-600 
            ${isMobile ? 'h-4 w-4' : 'h-4 w-4'}
          `} />
        </CardHeader>
        <CardContent className={`${isMobile ? 'pt-0 pb-3 px-3' : 'pt-0'}`}>
          <div className={`
            font-bold text-green-600 mb-1
            ${isMobile ? 'text-base' : isTablet ? 'text-lg' : 'text-2xl'}
          `}>
            {formatCurrency(netIncome, 'DOP')}
          </div>
          {previousMonthData && (
            <div className={`
              flex items-center text-gray-600 mt-1
              ${isMobile ? 'space-x-1 text-xs' : 'space-x-2 text-xs'}
            `}>
              {incomeGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={incomeGrowth >= 0 ? 'text-green-500' : 'text-red-500'}>
                {incomeGrowth > 0 ? '+' : ''}{incomeGrowth.toFixed(1)}%{isMobile ? '' : ' vs mes anterior'}
              </span>
            </div>
          )}
          {!isMobile && (
            <div className="mt-2 h-1 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-green-500 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${Math.min((netIncome / 100000) * 100, 100)}%` }}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagos del Mes */}
      <Card className={`
        relative overflow-hidden hover:shadow-lg transition-shadow duration-300
        ${isMobile ? 'border-l-4 border-l-red-500' : ''}
      `}>
        <CardHeader className={`
          flex flex-row items-center justify-between space-y-0
          ${isMobile ? 'pb-1 px-3 pt-3' : 'pb-2'}
        `}>
          <CardTitle className={`
            font-medium text-gray-600 dark:text-gray-300
            ${isMobile ? 'text-xs' : 'text-sm'}
          `}>
            {isMobile ? 'Pagos' : 'Pagos del Mes'}
          </CardTitle>
          <CreditCard className={`
            text-red-600 
            ${isMobile ? 'h-4 w-4' : 'h-4 w-4'}
          `} />
        </CardHeader>
        <CardContent className={`${isMobile ? 'pt-0 pb-3 px-3' : 'pt-0'}`}>
          <div className={`
            font-bold text-red-600 mb-1
            ${isMobile ? 'text-base' : isTablet ? 'text-lg' : 'text-2xl'}
          `}>
            {formatCurrency(totalMonthlyPayments, 'DOP')}
          </div>
          {previousMonthData && (
            <div className={`
              flex items-center text-gray-600 mt-1
              ${isMobile ? 'space-x-1 text-xs' : 'space-x-2 text-xs'}
            `}>
              {paymentsGrowth <= 0 ? (
                <TrendingDown className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingUp className="h-3 w-3 text-red-500" />
              )}
              <span className={paymentsGrowth <= 0 ? 'text-green-500' : 'text-red-500'}>
                {paymentsGrowth > 0 ? '+' : ''}{paymentsGrowth.toFixed(1)}%{isMobile ? '' : ' vs mes anterior'}
              </span>
            </div>
          )}
          <div className="mt-2">
            <div className={`
              flex justify-between text-gray-600 mb-1
              ${isMobile ? 'text-xs' : 'text-xs'}
            `}>
              <span>{isMobile ? 'Del ingreso:' : 'Del ingreso:'}</span>
              <span className="font-medium">{paymentToIncomeRatio.toFixed(1)}%</span>
            </div>
            <Progress
              value={paymentToIncomeRatio}
              className="h-1"
              indicatorClassName="bg-finanz-primary"
            />
          </div>
          {paymentBreakdown.overdue > 0 && (
            <div className="mt-2">
              <Badge variant="destructive" className={`
                ${isMobile ? 'text-xs px-1 py-0.5' : 'text-xs'}
              `}>
                <AlertTriangle className="w-3 h-3 mr-1" />
                {isMobile ? 'Vencidos' : 'Vencidos:'} {formatCurrency(paymentBreakdown.overdue, 'DOP')}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Balance Real Disponible */}
      <Card className={`
        relative overflow-hidden hover:shadow-lg transition-shadow duration-300
        ${isMobile ? 'border-l-4 border-l-blue-500 md:col-span-1' : 'md:col-span-2 lg:col-span-1'}
      `}>
        <CardHeader className={`
          flex flex-row items-center justify-between space-y-0
          ${isMobile ? 'pb-1 px-3 pt-3' : 'pb-2'}
        `}>
          <CardTitle className={`
            font-medium text-gray-600 dark:text-gray-300
            ${isMobile ? 'text-xs' : 'text-sm'}
          `}>
            {isMobile ? 'Balance' : 'Balance Real Disponible'}
          </CardTitle>
          <Target className={`
            text-blue-600 
            ${isMobile ? 'h-4 w-4' : 'h-4 w-4'}
          `} />
        </CardHeader>
        <CardContent className={`${isMobile ? 'pt-0 pb-3 px-3' : 'pt-0'}`}>
          <div className={`
            font-bold mb-1
            ${netBalance >= 0 ? 'text-green-600' : 'text-red-600'}
            ${isMobile ? 'text-base' : isTablet ? 'text-lg' : 'text-2xl'}
          `}>
            {formatCurrency(netBalance, 'DOP')}
          </div>
          {previousMonthData && (
            <div className={`
              flex items-center text-gray-600 mt-1
              ${isMobile ? 'space-x-1 text-xs' : 'space-x-2 text-xs'}
            `}>
              {balanceGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={balanceGrowth >= 0 ? 'text-green-500' : 'text-red-500'}>
                {balanceGrowth > 0 ? '+' : ''}{balanceGrowth.toFixed(1)}%{isMobile ? '' : ' vs mes anterior'}
              </span>
            </div>
          )}
          <div className={`
            mt-2 flex items-center gap-2
            ${isMobile ? 'flex-col items-start space-y-1' : 'justify-between'}
          `}>
            <Badge 
              variant={netBalance >= 0 ? 'default' : 'destructive'} 
              className={`${isMobile ? 'text-xs px-2 py-0.5' : 'text-xs'}`}
            >
              {netBalance >= 0 ? 'Positivo' : 'Déficit'}
            </Badge>
            <span className={`
              text-gray-600 font-medium
              ${isMobile ? 'text-xs' : 'text-xs'}
            `}>
              {isMobile ? `Ahorro: ${savingsRate.toFixed(1)}%` : `Tasa ahorro: ${savingsRate.toFixed(1)}%`}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
