
import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, Loader2 } from 'lucide-react';

interface ConnectivityIndicatorProps {
  className?: string;
}

export const ConnectivityIndicator: React.FC<ConnectivityIndicatorProps> = ({ className }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsConnecting(true);
      setTimeout(() => {
        setIsOnline(true);
        setIsConnecting(false);
      }, 1000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setIsConnecting(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Verificación periódica de conectividad
    const checkConnectivity = async () => {
      try {
        await fetch('/api/ping', { 
          method: 'HEAD',
          cache: 'no-cache',
          mode: 'no-cors'
        });
        if (!isOnline && !isConnecting) {
          handleOnline();
        }
      } catch {
        if (isOnline) {
          handleOffline();
        }
      }
    };

    const interval = setInterval(checkConnectivity, 10000); // Verificar cada 10 segundos

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, [isOnline, isConnecting]);

  if (isConnecting) {
    return (
      <Badge variant="outline" className={`flex items-center gap-1 ${className}`}>
        <Loader2 className="w-3 h-3 animate-spin" />
        <span className="text-xs">Conectando...</span>
      </Badge>
    );
  }

  return (
    <Badge 
      variant={isOnline ? "secondary" : "destructive"} 
      className={`flex items-center gap-1 ${className}`}
    >
      {isOnline ? (
        <>
          <Wifi className="w-3 h-3" />
          <span className="text-xs">En línea</span>
        </>
      ) : (
        <>
          <WifiOff className="w-3 h-3" />
          <span className="text-xs">Sin conexión</span>
        </>
      )}
    </Badge>
  );
};
