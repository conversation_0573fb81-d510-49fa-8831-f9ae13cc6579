<?xml version="1.0" encoding="UTF-8"?>
<!--
  FinanzApp Favicon v2
  Diseño minimalista y elegante: círculo con gradiente corporativo y flecha ascendente nítida.
-->
<svg
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 64 64"
  width="64"
  height="64"
  fill="none"
>
  <defs>
    <!-- Gradiente corporativo simplificado -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2563EB" /> <!-- Azul 600 -->
      <stop offset="100%" stop-color="#10B981" /> <!-- Emerald 500 -->
    </linearGradient>

    <!-- Sombra suave -->
    <filter id="shadow" x="-25%" y="-25%" width="150%" height="150%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.2)" />
    </filter>
  </defs>

  <!-- Fondo circular -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" filter="url(#shadow)" />

  <!-- Flecha ascendente (línea de tendencia) -->
  <g stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round">
    <!-- Línea de tendencia -->
    <path d="M18 40 L28 30 L34 36 L46 22" />
    <!-- Cabeza de flecha -->
    <polyline points="46 30 46 22 38 22" />
  </g>
</svg> 