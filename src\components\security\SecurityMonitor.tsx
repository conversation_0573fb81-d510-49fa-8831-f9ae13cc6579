import React from 'react';
import { useSecurityMonitoring } from '@/hooks/useSecurityMonitoring';
import { SecurityStatusIndicator } from './SecurityStatusIndicator';
import { SecurityAlerts } from './SecurityAlerts';

export const SecurityMonitor: React.FC = () => {
  useSecurityMonitoring();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <SecurityStatusIndicator />
      <SecurityAlerts />
    </div>
  );
};
