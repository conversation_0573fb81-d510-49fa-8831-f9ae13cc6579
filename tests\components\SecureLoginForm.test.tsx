import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { SecureLoginForm } from '@/components/auth/SecureLoginForm'

// mock toast to avoid side effects
vi.mock('sonner', () => ({ toast: { success: vi.fn(), error: vi.fn(), warning: vi.fn() } }))

// mock secure auth actions
const signInMock = vi.fn().mockResolvedValue({})
vi.mock('@/contexts/auth/useSecureAuthActions', () => ({
  useSecureAuthActions: () => ({ signIn: signInMock })
}))

describe('SecureLoginForm', () => {
  it('submits credentials and toggles password visibility', async () => {
    const onForgot = vi.fn()
    const setLoading = vi.fn()

    render(
      <SecureLoginForm
        isLoading={false}
        setIsLoading={setLoading}
        onForgotPassword={onForgot}
      />
    )

    // fill in fields
    fireEvent.change(screen.getByLabelText('Correo Electrónico *'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('Contraseña *'), { target: { value: 'secret' } })

    const passwordInput = screen.getByLabelText('Contraseña *') as HTMLInputElement
    // toggle visibility
    fireEvent.click(passwordInput.nextElementSibling as HTMLElement)
    expect(passwordInput.type).toBe('text')

    // submit form
    fireEvent.click(screen.getByRole('button', { name: /iniciar sesión segura/i }))
    expect(signInMock).toHaveBeenCalledWith('<EMAIL>', 'secret')

    // forgot password
    fireEvent.click(screen.getByRole('button', { name: /¿Olvidaste tu contraseña\?/i }))
    expect(onForgot).toHaveBeenCalled()
  })
})
