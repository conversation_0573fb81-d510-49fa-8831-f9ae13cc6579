
import React from 'react';
import { usePDFDataEngine } from '../../core/PDFDataEngine';
import { createPageStyle, createHeaderStyle, createCardStyle, createBaseStyle, createTitleStyle, createTextStyle, pdfStyles } from '../../styles/pdfStyles';

export const ExecutiveSummaryPage: React.FC = () => {
  const { 
    overallScore, 
    formatCurrency, 
    // getScoreColor, // Unused
    netIncome, 
    totalExpenses, 
    netBalance, 
    insights,
    reportMetadata 
  } = usePDFDataEngine();

  const getScoreStatus = (score: number) => {
    if (score >= 80) return { text: 'Excelente', color: pdfStyles.colors.success };
    if (score >= 60) return { text: 'Bueno', color: pdfStyles.colors.warning };
    return { text: 'Necesita Mejora', color: pdfStyles.colors.danger };
  };

  const roundedScore = Math.round(overallScore);
  const scoreStatus = getScoreStatus(roundedScore);

  return (
    <div style={createPageStyle()}>
      {/* Header Profesional */}
      <div style={createHeaderStyle()}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <h1 style={createTitleStyle('h1')}>
              Estado de Cuenta Financiero
            </h1>
            <p style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.xl,
              fontWeight: pdfStyles.typography.fontWeight.semibold,
              margin: '0 0 6px 0',
              color: pdfStyles.colors.text.secondary
            })}>
              Resumen Ejecutivo Profesional
            </p>
            <p style={createTextStyle('muted')}>
              Generado el {reportMetadata.generatedDate}
            </p>
          </div>
          <div style={{
            background: `linear-gradient(135deg, ${pdfStyles.colors.primary}, ${pdfStyles.colors.secondary})`,
            color: 'white',
            padding: pdfStyles.spacing['2xl'],
            borderRadius: '12px',
            textAlign: 'center',
            minWidth: '140px'
          }}>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.base, 
              fontWeight: pdfStyles.typography.fontWeight.medium, 
              marginBottom: '4px', 
              color: 'white' 
            })}>
              Powered by
            </div>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize['3xl'], 
              fontWeight: pdfStyles.typography.fontWeight.bold, 
              marginBottom: '3px', 
              color: 'white' 
            })}>
              FINANZ
            </div>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.xs, 
              opacity: 0.9, 
              color: 'white' 
            })}>
              Financial Analytics
            </div>
          </div>
        </div>
      </div>

      {/* Puntuación Principal */}
      <div style={createCardStyle({ 
        marginBottom: pdfStyles.spacing['4xl'],
        textAlign: 'center',
        padding: pdfStyles.spacing['4xl']
      })}>
        <h2 style={createTitleStyle('h2')}>
          Puntuación de Salud Financiera
        </h2>
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          gap: '30mm',
          marginTop: pdfStyles.spacing['2xl']
        }}>
          {/* Círculo de puntuación */}
          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              border: `10px solid ${scoreStatus.color}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
              margin: '0 auto 15px'
            }}>
              <span style={createBaseStyle({ 
                fontSize: '32px', 
                fontWeight: pdfStyles.typography.fontWeight.bold, 
                color: scoreStatus.color 
              })}>
                {roundedScore}%
              </span>
            </div>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.xl, 
              fontWeight: pdfStyles.typography.fontWeight.semibold, 
              color: scoreStatus.color,
              marginBottom: '6px'
            })}>
              {scoreStatus.text}
            </div>
            <div style={createTextStyle('muted')}>
              Puntuación General
            </div>
          </div>
          
          {/* Interpretación */}
          <div style={{ flex: 1, maxWidth: '300px', textAlign: 'left' }}>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.lg, 
              fontWeight: pdfStyles.typography.fontWeight.semibold, 
              marginBottom: '12px',
              color: pdfStyles.colors.text.primary
            })}>
              Interpretación de Resultados
            </div>
            <div style={createBaseStyle({ 
              lineHeight: pdfStyles.typography.lineHeight.relaxed,
              color: pdfStyles.colors.text.secondary,
              fontSize: pdfStyles.typography.fontSize.base
            })}>
              {roundedScore >= 80 && 'Su salud financiera es excelente. Mantenga sus hábitos actuales y explore oportunidades de crecimiento avanzado.'}
              {roundedScore >= 60 && roundedScore < 80 && 'Su salud financiera es buena con oportunidades claras de mejora. Implemente las recomendaciones prioritarias.'}
              {roundedScore < 60 && 'Su salud financiera requiere atención inmediata. Siga el plan de acción para estabilizar su situación.'}
            </div>
          </div>
        </div>
      </div>

      {/* Métricas Clave */}
      <div style={{ marginBottom: pdfStyles.spacing['4xl'] }}>
        <h3 style={createTitleStyle('h3')}>
          Métricas Financieras Principales
        </h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: pdfStyles.spacing['3xl'], marginTop: pdfStyles.spacing['2xl'] }}>
          <div style={createCardStyle({
            backgroundColor: '#F0FDF4',
            borderColor: '#22C55E',
            textAlign: 'center',
            minHeight: '90px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          })}>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize['4xl'], 
              fontWeight: pdfStyles.typography.fontWeight.bold, 
              color: pdfStyles.colors.success, 
              marginBottom: '8px' 
            })}>
              {formatCurrency(netIncome)}
            </div>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.base, 
              color: '#047857', 
              fontWeight: pdfStyles.typography.fontWeight.semibold 
            })}>
              Ingresos Netos
            </div>
          </div>
          
          <div style={createCardStyle({
            backgroundColor: '#FEF3C7',
            borderColor: '#F59E0B',
            textAlign: 'center',
            minHeight: '90px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          })}>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize['4xl'], 
              fontWeight: pdfStyles.typography.fontWeight.bold, 
              color: pdfStyles.colors.warning, 
              marginBottom: '8px' 
            })}>
              {formatCurrency(totalExpenses)}
            </div>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.base, 
              color: '#92400E', 
              fontWeight: pdfStyles.typography.fontWeight.semibold 
            })}>
              Gastos Totales
            </div>
          </div>
          
          <div style={createCardStyle({
            backgroundColor: netBalance >= 0 ? '#F0FDF4' : '#FEF2F2',
            borderColor: netBalance >= 0 ? '#22C55E' : '#EF4444',
            textAlign: 'center',
            minHeight: '90px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          })}>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize['4xl'], 
              fontWeight: pdfStyles.typography.fontWeight.bold, 
              color: netBalance >= 0 ? pdfStyles.colors.success : pdfStyles.colors.danger, 
              marginBottom: '8px' 
            })}>
              {formatCurrency(netBalance)}
            </div>
            <div style={createBaseStyle({ 
              fontSize: pdfStyles.typography.fontSize.base, 
              color: netBalance >= 0 ? '#047857' : '#991B1B', 
              fontWeight: pdfStyles.typography.fontWeight.semibold 
            })}>
              Balance Neto
            </div>
          </div>
        </div>
      </div>

      {/* Insights Principales */}
      <div style={{ flex: 1, marginBottom: pdfStyles.spacing['4xl'] }}>
        <h3 style={createTitleStyle('h3')}>
          Análisis y Recomendaciones Prioritarias
        </h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: pdfStyles.spacing.lg, marginTop: pdfStyles.spacing['2xl'] }}>
          {insights.slice(0, 3).map((insight, index) => (
            <div key={index} style={createCardStyle({
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start'
            })}>
              <div style={{ flex: 1 }}>
                <h4 style={createBaseStyle({ 
                  fontSize: pdfStyles.typography.fontSize.lg, 
                  fontWeight: pdfStyles.typography.fontWeight.semibold,
                  margin: '0 0 10px 0',
                  color: pdfStyles.colors.text.primary
                })}>
                  {insight.category}
                </h4>
                <p style={createBaseStyle({ 
                  margin: 0,
                  lineHeight: pdfStyles.typography.lineHeight.relaxed,
                  color: pdfStyles.colors.text.secondary,
                  fontSize: pdfStyles.typography.fontSize.base
                })}>
                  <strong style={{ color: pdfStyles.colors.text.primary }}>Recomendación:</strong> {insight.recommendation}
                </p>
              </div>
              <div style={{
                backgroundColor: insight.status === 'excellent' ? pdfStyles.colors.success : 
                                insight.status === 'good' ? pdfStyles.colors.primary : 
                                insight.status === 'warning' ? pdfStyles.colors.warning : pdfStyles.colors.danger,
                color: 'white',
                padding: '8px 16px',
                borderRadius: '20px',
                fontSize: pdfStyles.typography.fontSize.sm,
                fontWeight: pdfStyles.typography.fontWeight.semibold,
                marginLeft: '20px'
              }}>
                {Math.round(insight.score)}%
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div style={{ 
        borderTop: `2px solid ${pdfStyles.colors.neutral[300]}`,
        paddingTop: pdfStyles.spacing.lg
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={createTextStyle('muted')}>
            <div style={{ marginBottom: '4px' }}><strong style={{ color: pdfStyles.colors.text.primary }}>Próxima revisión:</strong> {reportMetadata.nextReviewDate}</div>
            <div><strong style={{ color: pdfStyles.colors.text.primary }}>Versión:</strong> {reportMetadata.reportVersion}</div>
          </div>
          <div style={createBaseStyle({ 
            fontSize: pdfStyles.typography.fontSize.xs, 
            color: pdfStyles.colors.text.muted, 
            textAlign: 'right' 
          })}>
            <div style={{ marginBottom: '4px' }}>Documento confidencial</div>
            <div>FINANZ Professional Analytics</div>
          </div>
        </div>
      </div>
    </div>
  );
};
