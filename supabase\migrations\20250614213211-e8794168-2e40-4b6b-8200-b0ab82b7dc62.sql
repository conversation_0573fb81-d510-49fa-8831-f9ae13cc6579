
CREATE OR REPLACE FUNCTION public.apply_credit_card_payment(
    p_card_id uuid,
    p_user_id uuid,
    p_amount numeric,
    p_paid_date date,
    p_due_date date,
    p_notes text,
    p_currency text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    existing_record_id uuid;
    existing_notes text;
BEGIN
    -- 1. Actualizar el balance de la tarjeta de crédito
    UPDATE public.credit_cards
    SET current_balance = current_balance - p_amount,
        updated_at = now()
    WHERE id = p_card_id AND user_id = p_user_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Credit card not found or user not authorized';
    END IF;

    -- 2. Verificar si ya existe un registro de pago para esa fecha de vencimiento
    SELECT id, notes INTO existing_record_id, existing_notes
    FROM public.payment_records
    WHERE user_id = p_user_id
      AND reference_id = p_card_id
      AND payment_type = 'credit_card'
      AND due_date = p_due_date;

    -- 3. Si existe, actualizar el registro. Si no, insertar uno nuevo.
    IF existing_record_id IS NOT NULL THEN
        -- Actualizar el pago existente, consolidando las notas de forma inteligente.
        UPDATE public.payment_records
        SET amount = amount + p_amount,
            paid_date = p_paid_date, -- Usar la fecha del pago más reciente
            notes = (
                CASE
                    -- Si es el primer pago adicional, agregar un encabezado para consolidar.
                    WHEN existing_notes IS NULL OR existing_notes NOT LIKE '%--- Pagos Consolidados ---%' THEN
                        COALESCE(existing_notes, '') || E'\n\n--- Pagos Consolidados ---'
                    ELSE
                        existing_notes
                END
            ) || (
                CASE
                    -- Anexar la nueva nota si se proporcionó, como un item de lista.
                    WHEN p_notes IS NOT NULL AND p_notes <> '' THEN
                        E'\n- ' || p_notes
                    ELSE
                        ''
                END
            ),
            updated_at = now()
        WHERE id = existing_record_id;
    ELSE
        -- Insertar un nuevo registro de pago
        INSERT INTO public.payment_records(user_id, reference_id, payment_type, amount, currency, status, paid_date, due_date, notes)
        VALUES (p_user_id, p_card_id, 'credit_card', p_amount, p_currency, 'paid', p_paid_date, p_due_date, p_notes);
    END IF;
END;
$$;
