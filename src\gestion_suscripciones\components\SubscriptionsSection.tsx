
import React, { useState } from 'react';
import { Plus, FileText, Edit, Trash } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useFinanceData } from '@/hooks/useFinanceData';
import { SubscriptionForm } from './SubscriptionForm';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Subscription } from '@/types';

interface SubscriptionsSectionProps {
  showInactive?: boolean;
}

export function SubscriptionsSection({ showInactive }: SubscriptionsSectionProps) {
  const { subscriptions, addSubscription, updateSubscription, deleteSubscription } = useFinanceData();
  const [showForm, setShowForm] = useState(false);
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null);
  const [subscriptionToDelete, setSubscriptionToDelete] = useState<Subscription | null>(null);

  const filteredSubscriptions = showInactive
    ? subscriptions.filter(sub => !sub.isActive)
    : subscriptions.filter(sub => sub.isActive);

  const handleFormSubmit = (subscriptionData: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingSubscription) {
      updateSubscription(editingSubscription.id, subscriptionData);
    } else {
      addSubscription(subscriptionData);
    }
    setShowForm(false);
    setEditingSubscription(null);
  };

  const handleUpdateSubscription = (id: string, updates: Partial<Subscription>) => {
    updateSubscription(id, updates);
  };

  const handleDeleteSubscription = (id: string) => {
    deleteSubscription(id);
    setSubscriptionToDelete(null);
  };

  const handleStartEditing = (subscription: Subscription) => {
    setEditingSubscription(subscription);
    setShowForm(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          {showInactive ? 'Suscripciones Inactivas' : 'Suscripciones Activas'}
        </h2>
        <Button
          onClick={() => {
            setEditingSubscription(null);
            setShowForm(true);
          }}
          className="gap-2"
        >
          <Plus className="w-4 h-4" />
          Nueva Suscripción
        </Button>
      </div>

      {showForm && (
        <SubscriptionForm
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingSubscription(null);
          }}
          editingSubscription={editingSubscription}
        />
      )}

      {filteredSubscriptions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <FileText className="w-12 h-12 text-finanz-text-secondary mx-auto mb-3" />
            <p className="text-finanz-text-secondary">
              No hay suscripciones {showInactive ? 'inactivas' : 'activas'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {filteredSubscriptions.map((subscription) => (
            <Card key={subscription.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-finanz-neutral/10 rounded-lg">
                      <FileText className="w-4 h-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-finanz-text-primary">{subscription.name}</h4>
                      <p className="text-sm text-finanz-text-secondary">
                        {subscription.categoryName} • Día {subscription.billingDate}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center justify-end gap-2 mb-1">
                      <span className="font-semibold text-finanz-primary">
                        {formatCurrency(subscription.amount, subscription.currency)}
                      </span>
                      <Badge variant="secondary">{subscription.currency}</Badge>
                    </div>
                    <div className="flex items-center justify-end space-x-2">
                      <Button variant="ghost" size="icon" onClick={() => handleStartEditing(subscription)} aria-label="Editar suscripción">
                        <Edit className="w-4 h-4" />
                      </Button>
                       <Button variant="ghost" size="icon" onClick={() => setSubscriptionToDelete(subscription)} aria-label="Eliminar suscripción">
                        <Trash className="w-4 h-4 text-finanz-danger" />
                      </Button>
                      <Switch
                        id={`active-${subscription.id}`}
                        checked={subscription.isActive}
                        onCheckedChange={(checked) => handleUpdateSubscription(subscription.id, { isActive: checked })}
                      />
                      <Label htmlFor={`active-${subscription.id}`} className="text-sm text-finanz-text-secondary">
                        {subscription.isActive ? 'Activa' : 'Inactiva'}
                      </Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <AlertDialog open={!!subscriptionToDelete} onOpenChange={(open) => !open && setSubscriptionToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Estás absolutamente seguro?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta acción no se puede deshacer. Esto eliminará permanentemente la suscripción de '{subscriptionToDelete?.name}'.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => {
                if (subscriptionToDelete) {
                  handleDeleteSubscription(subscriptionToDelete.id);
                }
              }}
            >
              Sí, eliminar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
