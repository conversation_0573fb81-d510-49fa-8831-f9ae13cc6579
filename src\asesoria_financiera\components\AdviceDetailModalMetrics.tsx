
import React from 'react';
import { TrendingUp, Target } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';

interface AdviceDetailModalMetricsProps {
  potentialSavings: number;
  targetAmount: number;
}

export const AdviceDetailModalMetrics: React.FC<AdviceDetailModalMetricsProps> = ({
  potentialSavings,
  targetAmount
}) => {
  if (potentialSavings <= 0 && targetAmount <= 0) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {potentialSavings > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-800">Ahorro Potencial</span>
          </div>
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(potentialSavings, 'DOP')}
          </div>
          <p className="text-sm text-green-700 mt-1">Mensual estimado</p>
        </div>
      )}

      {targetAmount > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Target className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-blue-800">Meta Objetivo</span>
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {formatCurrency(targetAmount, 'DOP')}
          </div>
          <p className="text-sm text-blue-700 mt-1">Monto a alcanzar</p>
        </div>
      )}
    </div>
  );
};
