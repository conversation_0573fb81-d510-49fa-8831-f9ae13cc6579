import { logger } from "@/utils/logger";
import { toast } from 'sonner';
import { SecuritySettings, SecurityLevel } from './security/types';
import { SecurityConfig } from './security/securityConfig';
import { SessionMonitor } from './security/sessionMonitor';
import { AuthMethods } from './security/authMethods';

export class SecurityService {
  private static instance: SecurityService;
  private settings: SecuritySettings | null = null;
  private sessionMonitor: SessionMonitor;
  private authMethods: AuthMethods;

  private constructor() {
    this.sessionMonitor = new SessionMonitor(this.settings);
    this.authMethods = new AuthMethods();
  }

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  async loadSettings(): Promise<SecuritySettings> {
    try {
      const savedSettings = localStorage.getItem('finanz_security_settings');
      if (savedSettings) {
        this.settings = JSON.parse(savedSettings);
        logger.debug('SecurityService: Loaded settings from localStorage:', this.settings);
      } else {
        this.settings = SecurityConfig.getDefaultSettings();
        logger.debug('SecurityService: Using default settings:', this.settings);
      }

      // Reinicializar monitor de sesión con nuevas configuraciones
      this.sessionMonitor = new SessionMonitor(this.settings);

      // Inicializar timers basados en configuración
      if (this.settings.autoLogout) {
        this.sessionMonitor.startSessionMonitoring();
      }

      if (this.settings.lockOnMinimize) {
        this.sessionMonitor.setupWindowVisibilityHandling();
      }

      return this.settings;
    } catch (error) {
      console.error('SecurityService: Error loading settings:', error);
      throw error;
    }
  }

  async saveSettings(settings: SecuritySettings): Promise<void> {
    try {
      localStorage.setItem('finanz_security_settings', JSON.stringify(settings));
      this.settings = settings;
      logger.debug('SecurityService: Settings saved:', settings);

      // Reiniciar monitoreo con nueva configuración
      this.sessionMonitor.stopSessionTimer();
      this.sessionMonitor = new SessionMonitor(this.settings);
      
      if (settings.autoLogout) {
        this.sessionMonitor.startSessionMonitoring();
      }

      if (settings.biometricAuth) {
        await this.authMethods.setupBiometricAuth();
      }

      if (settings.lockOnMinimize) {
        this.sessionMonitor.setupWindowVisibilityHandling();
      }

      // Aplicar configuraciones basadas en el nivel de seguridad
      const updatedSettings = SecurityConfig.applySecurityLevel(settings, settings.securityLevel);
      if (JSON.stringify(updatedSettings) !== JSON.stringify(settings)) {
        this.settings = updatedSettings;
        localStorage.setItem('finanz_security_settings', JSON.stringify(updatedSettings));
      }

      toast.success('Configuración de seguridad actualizada');
    } catch (error) {
      console.error('SecurityService: Error saving settings:', error);
      throw error;
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    return this.authMethods.changePassword(currentPassword, newPassword);
  }

  async setupTwoFactorAuth(): Promise<{ secret: string; qrCodeUrl: string }> {
    return this.authMethods.setupTwoFactorAuth();
  }

  async setupBiometricAuth(): Promise<void> {
    return this.authMethods.setupBiometricAuth();
  }

  async verifyTwoFactorAuth(token: string): Promise<boolean> {
    return this.authMethods.verifyTwoFactorAuth(token);
  }

  resetActivity(): void {
    this.sessionMonitor.resetActivity();
  }

  getTimeUntilTimeout(): number {
    return this.sessionMonitor.getTimeUntilTimeout();
  }

  getSecurityLevel(): SecurityLevel {
    return this.settings?.securityLevel || 'basic';
  }

  shouldRequireReauth(): boolean {
    return this.settings?.requireReauth || false;
  }

  isSessionExpired(): boolean {
    return this.sessionMonitor.isSessionExpired();
  }

  shouldLockScreen(): boolean {
    return this.sessionMonitor.shouldLockScreen();
  }

  async requireReauthentication(): Promise<boolean> {
    if (!this.shouldRequireReauth()) {
      return true;
    }

    return new Promise((resolve) => {
      const handleReauth = (event: CustomEvent) => {
        window.removeEventListener('security:reauthComplete', handleReauth as EventListener);
        resolve(event.detail.success);
      };
      
      window.addEventListener('security:reauthComplete', handleReauth as EventListener);
      window.dispatchEvent(new CustomEvent('security:requireReauth'));
    });
  }
}

export const securityService = SecurityService.getInstance();

// Re-export types for backward compatibility
export type { SecuritySettings, SecurityLevel };
