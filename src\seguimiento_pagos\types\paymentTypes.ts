
export interface PaymentItem {
  id: string;
  type: 'credit-card' | 'loan' | 'subscription' | 'personal-debt' | 'expense';
  name: string;
  amount: number;
  currency: 'DOP' | 'USD';
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue';
  paidDate?: string;
  notes?: string;
  recordId?: string;
  referenceId: string;
  createdAt?: string; // Fecha de creación de la entidad origen (Expense, PaymentRecord, etc.) para validaciones temporales
  // Indica que este pago proviene de un arrastre (rollover) de un periodo anterior
  isCarriedOver?: boolean;
}

export interface CategorizedPayments {
  pending: PaymentItem[];
  paid: PaymentItem[];
  overdue: PaymentItem[];
}

export interface PaymentTotals {
  pending: number;
  overdue: number;
  paidThisMonth: number;
}
