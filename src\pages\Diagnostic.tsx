
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useDashboardData } from '@/dashboard_principal/hooks/useDashboardData';
import { DiagnosticCharts } from '@/dashboard_principal/components/DiagnosticCharts';
import { AdvancedRecommendations } from '@/dashboard_principal/components/AdvancedRecommendations';
import { FinancialProjections } from '@/dashboard_principal/components/FinancialProjections';
import { SmartAlerts } from '@/dashboard_principal/components/SmartAlerts';
import { ActionableInsights } from '@/dashboard_principal/components/ActionableInsights';
import { DiagnosticHeader } from './diagnostic/components/DiagnosticHeader';
import { OverallScoreCard } from './diagnostic/components/OverallScoreCard';
import { KeyMetricsCards } from './diagnostic/components/KeyMetricsCards';
import { AnalysisCards } from './diagnostic/components/AnalysisCards';
import { DiagnosticActions } from './diagnostic/components/DiagnosticActions';

export default function DiagnosticPage() {
  // Cargar datos financieros básicos
  const {
    getTotalDebt,
    // getSavingsRate, // Unused, value taken from useDashboardData
    // getDebtToIncomeRatio, // Unused, value taken from useDashboardData
    // getEmergencyFundMonths, // Unused, value taken from useDashboardData
    isLoading
  } = useFinanceData(
    ['incomes', 'expenses', 'debts', 'reimbursements'],
    { currentMonth: true }
  );

  // Cargar datos mejorados con pagos programados del mes
  const {
    netIncome,
    totalMonthlyPayments, // TOTAL REAL de pagos programados del mes
    netBalance,
    savingsRate,
    debtToIncomeRatio,
    emergencyFundMonths,
    paymentToIncomeRatio,
    paymentBreakdown
  } = useDashboardData();

  const totalDebt = getTotalDebt();

  // Calcular puntuación general basada en pagos reales del mes
  const overallScore = Math.min(100, Math.max(0,
    ((savingsRate / 30 * 100) +
    ((100 - debtToIncomeRatio) / 100 * 100) +
    (emergencyFundMonths / 6 * 100) +
    ((100 - paymentToIncomeRatio) / 100 * 100)) / 4
  ));

  if (isLoading) {
    return (
      <div className="p-6 space-y-6 animate-pulse">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-48"></div>
          </div>
          <div className="w-8 h-8 bg-gray-200 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {[1, 2, 3, 4, 5].map(i => (
            <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <DiagnosticHeader />

      <OverallScoreCard overallScore={overallScore} />

      <SmartAlerts />

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="analysis">Análisis</TabsTrigger>
          <TabsTrigger value="recommendations">Recomendaciones</TabsTrigger>
          <TabsTrigger value="projections">Proyecciones</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <KeyMetricsCards
            netBalance={netBalance}
            savingsRate={savingsRate}
            debtToIncomeRatio={debtToIncomeRatio}
            emergencyFundMonths={emergencyFundMonths}
            paymentCoverage={paymentToIncomeRatio}
          />

          <AnalysisCards
            netIncome={netIncome}
            totalMonthlyPayments={totalMonthlyPayments} // Total real de pagos del mes
            netBalance={netBalance}
            totalDebt={totalDebt}
            savingsRate={savingsRate}
            paymentBreakdown={paymentBreakdown}
          />
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <DiagnosticCharts
            netIncome={netIncome}
            totalMonthlyPayments={totalMonthlyPayments} // Total real de pagos del mes
            totalDebt={totalDebt}
            savingsRate={savingsRate}
            debtToIncomeRatio={debtToIncomeRatio}
            emergencyFundMonths={emergencyFundMonths}
            paymentBreakdown={paymentBreakdown}
          />
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <AdvancedRecommendations />
        </TabsContent>

        <TabsContent value="projections" className="space-y-6">
          <FinancialProjections
            netIncome={netIncome}
            totalExpenses={totalMonthlyPayments} // Usar total de pagos del mes
            savingsRate={savingsRate}
          />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <ActionableInsights />
        </TabsContent>
      </Tabs>

      <DiagnosticActions
        overallScore={overallScore}
        netIncome={netIncome}
        totalExpenses={totalMonthlyPayments} // Usar total de pagos del mes
        netBalance={netBalance}
        totalDebt={totalDebt}
        savingsRate={savingsRate}
        debtToIncomeRatio={debtToIncomeRatio}
        emergencyFundMonths={emergencyFundMonths}
      />
    </div>
  );
}
