import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { formatCurrency } from '@/components/ui/numeric-input';
import { PaymentItem } from '../hooks/usePaymentsLogic';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info, User, CheckCircle } from 'lucide-react';
import { PersonalDebtPaymentOptions } from './PersonalDebtPaymentOptions';

interface PaymentData {
  notes?: string;
  amount?: number;
  interestAmount?: number;
  principalAmount?: number;
  paymentType?: 'minimum' | 'interest_only' | 'custom';
}

interface MarkPaymentAsPaidModalProps {
  payment: PaymentItem | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (payment: PaymentItem, paymentData?: PaymentData) => Promise<void>;
}

export function MarkPaymentAsPaidModal({
  payment,
  isOpen,
  onClose,
  onConfirm,
}: MarkPaymentAsPaidModalProps) {
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [personalDebtPaymentData, setPersonalDebtPaymentData] = useState<PaymentData | null>(null);

  if (!payment) return null;

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      let paymentData: PaymentData | undefined;
      
      if (payment.type === 'personal-debt' && personalDebtPaymentData) {
        paymentData = {
          ...personalDebtPaymentData,
          notes: personalDebtPaymentData.notes || notes
        };
      } else {
        // Para otros tipos de pago, solo pasar las notas si existen
        paymentData = notes.trim() ? { notes: notes.trim() } : undefined;
      }
      
      await onConfirm(payment, paymentData);
      
      // Limpiar estado y cerrar modal
      handleClose();
    } catch (error) {
      console.error('Error confirming payment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setNotes('');
    setPersonalDebtPaymentData(null);
    setIsLoading(false);
    onClose();
  };

  const getPaymentTypeLabel = (type: string) => {
    switch (type) {
      case 'credit-card':
        return 'Tarjeta de Crédito';
      case 'loan':
        return 'Préstamo';
      case 'subscription':
        return 'Suscripción';
      case 'personal-debt':
        return 'Abono a Deuda Personal';
      case 'expense':
        return 'Gasto';
      default:
        return 'Pago';
    }
  };

  const isPersonalDebt = payment.type === 'personal-debt';
  const canConfirm = isPersonalDebt ? personalDebtPaymentData : true;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isPersonalDebt ? <User className="w-5 h-5" /> : <CheckCircle className="w-5 h-5" />}
            {isPersonalDebt ? 'Configurar Abono a Deuda Personal' : 'Confirmar Pago'}
          </DialogTitle>
          <DialogDescription>
            {isPersonalDebt 
              ? 'Elige el tipo de abono que deseas realizar y configura los montos.'
              : 'Confirma que has realizado este pago para actualizar tu seguimiento.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Tipo:</span>
                <span className="font-medium">{getPaymentTypeLabel(payment.type)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Concepto:</span>
                <span className="font-medium">{payment.name}</span>
              </div>
              {!isPersonalDebt && (
                <>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Monto:</span>
                    <span className="font-semibold text-lg">
                      {formatCurrency(payment.amount, payment.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Fecha de vencimiento:</span>
                    <span className="font-medium">
                      {new Date(payment.dueDate).toLocaleDateString()}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>

          {isPersonalDebt ? (
            <PersonalDebtPaymentOptions
              payment={payment}
              onPaymentChange={setPersonalDebtPaymentData}
            />
          ) : (
            <>
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Este pago se marcará como completado en tu seguimiento de pagos.
                  Las notas son opcionales.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="notes">Notas (opcional)</Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Agrega cualquier nota sobre este pago..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                autoComplete="off"
                />
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={isLoading || !canConfirm}
          >
            {isLoading ? 'Procesando...' : (isPersonalDebt ? 'Confirmar Abono' : 'Confirmar Pago')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
