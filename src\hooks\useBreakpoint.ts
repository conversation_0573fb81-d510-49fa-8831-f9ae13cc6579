
import { useState, useEffect } from 'react';

export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export function useBreakpoint() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('md');
  const [deviceType, setDeviceType] = useState<DeviceType>('desktop');
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let newBreakpoint: Breakpoint = 'xs';
      for (const [bp, minWidth] of Object.entries(breakpoints).reverse()) {
        if (width >= minWidth) {
          newBreakpoint = bp as Breakpoint;
          break;
        }
      }
      
      // Determinar tipo de dispositivo con lógica mejorada
      let newDeviceType: DeviceType;
      if (width < 768) {
        newDeviceType = 'mobile';
      } else if (width < 1024 || (width < 1366 && height < 1024)) {
        newDeviceType = 'tablet';
      } else {
        newDeviceType = 'desktop';
      }
      
      // Detectar capacidades táctiles
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      setCurrentBreakpoint(newBreakpoint);
      setDeviceType(newDeviceType);
      setIsTouchDevice(hasTouch);
    };

    updateBreakpoint();
    
    const mediaQueries = Object.entries(breakpoints).map(([bp, minWidth]) => {
      const mq = window.matchMedia(`(min-width: ${minWidth}px)`);
      mq.addEventListener('change', updateBreakpoint);
      return mq;
    });

    window.addEventListener('orientationchange', updateBreakpoint);

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', updateBreakpoint);
      });
      window.removeEventListener('orientationchange', updateBreakpoint);
    };
  }, []);

  const isBreakpoint = (bp: Breakpoint) => {
    return breakpoints[currentBreakpoint] >= breakpoints[bp];
  };

  return {
    currentBreakpoint,
    deviceType,
    isTouchDevice,
    isBreakpoint,
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isDesktop: deviceType === 'desktop',
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  };
}
