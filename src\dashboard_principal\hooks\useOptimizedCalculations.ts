
import { useMemo } from 'react';
import { useDashboardData } from './useDashboardData';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useExchangeRate } from '@/hooks/useExchangeRate';

export interface PeriodComparison {
  current: number;
  previous: number;
  change: number;
  changePercentage: number;
  trend: 'up' | 'down' | 'stable';
}

export interface OptimizedMetrics {
  netIncomeComparison: PeriodComparison;
  expensesComparison: PeriodComparison;
  savingsComparison: PeriodComparison;
  efficiency: number;
  liquidityRatio: number;
  debtServiceRatio: number;
  emergencyFundAdequacy: 'excellent' | 'good' | 'fair' | 'poor';
  financialHealthScore: number;
}

export const useOptimizedCalculations = () => {
  const dashboardData = useDashboardData();
  const { incomes, expenses } = useFinanceData(['incomes', 'expenses']);
  const { rate: exchangeRate } = useExchangeRate();

  const optimizedMetrics = useMemo((): OptimizedMetrics => {
    const currentDate = new Date();
    const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    const previousMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth()).padStart(2, '0')}`;

    // Función helper para crear comparaciones
    const createComparison = (current: number, previous: number): PeriodComparison => {
      const change = current - previous;
      const changePercentage = previous !== 0 ? (change / previous) * 100 : 0;
      const trend = Math.abs(changePercentage) < 1 ? 'stable' : changePercentage > 0 ? 'up' : 'down';
      
      return {
        current,
        previous,
        change,
        changePercentage,
        trend
      };
    };

    // Cálculos de ingresos por período
    const currentMonthIncomes = incomes
      .filter(income => income.month === currentMonth)
      .reduce((sum, income) => {
        const amount = income.currency === 'USD' ? (income.netIncome || 0) * exchangeRate : (income.netIncome || 0);
        return sum + amount;
      }, 0);

    const previousMonthIncomes = incomes
      .filter(income => income.month === previousMonth)
      .reduce((sum, income) => {
        const amount = income.currency === 'USD' ? (income.netIncome || 0) * exchangeRate : (income.netIncome || 0);
        return sum + amount;
      }, 0);

    // Cálculos de gastos por período
    const currentMonthExpenses = expenses
      .filter(expense => expense.month === currentMonth)
      .reduce((sum, expense) => {
        const amount = expense.currency === 'USD' ? (expense.amount || 0) * exchangeRate : (expense.amount || 0);
        return sum + amount;
      }, 0);

    const previousMonthExpenses = expenses
      .filter(expense => expense.month === previousMonth)
      .reduce((sum, expense) => {
        const amount = expense.currency === 'USD' ? (expense.amount || 0) * exchangeRate : (expense.amount || 0);
        return sum + amount;
      }, 0);

    // Cálculos de ahorro
    const currentSavings = currentMonthIncomes - currentMonthExpenses - dashboardData.totalMonthlyPayments;
    const previousSavings = previousMonthIncomes - previousMonthExpenses - dashboardData.totalMonthlyPayments;

    // Métricas avanzadas
    const efficiency = currentMonthIncomes > 0 ? ((currentSavings / currentMonthIncomes) * 100) : 0;
    const liquidityRatio = dashboardData.totalMonthlyPayments > 0 ? (currentMonthIncomes / dashboardData.totalMonthlyPayments) : 0;
    const debtServiceRatio = currentMonthIncomes > 0 ? (dashboardData.totalMonthlyPayments / currentMonthIncomes) * 100 : 0;
    
    // Evaluación del fondo de emergencia
    const emergencyFundAdequacy: 'excellent' | 'good' | 'fair' | 'poor' = 
      dashboardData.emergencyFundMonths >= 6 ? 'excellent' :
      dashboardData.emergencyFundMonths >= 3 ? 'good' :
      dashboardData.emergencyFundMonths >= 1 ? 'fair' : 'poor';

    // Score de salud financiera (0-100)
    const financialHealthScore = Math.min(100, Math.max(0, 
      (efficiency * 0.3) + 
      (Math.min(100, liquidityRatio * 20) * 0.25) + 
      (Math.max(0, 100 - debtServiceRatio) * 0.25) + 
      (dashboardData.emergencyFundMonths * 10 * 0.2)
    ));

    return {
      netIncomeComparison: createComparison(currentMonthIncomes, previousMonthIncomes),
      expensesComparison: createComparison(currentMonthExpenses, previousMonthExpenses),
      savingsComparison: createComparison(currentSavings, previousSavings),
      efficiency,
      liquidityRatio,
      debtServiceRatio,
      emergencyFundAdequacy,
      financialHealthScore
    };
  }, [incomes, expenses, dashboardData, exchangeRate]);

  return optimizedMetrics;
};
