
import { Button } from '@/components/ui/button';
import { RefreshCw, Activity } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export function DiagnosticHeader() {
  const { toast } = useToast();

  const handleRefreshData = () => {
    // Simular actualización
    setTimeout(() => {
      toast({
        title: 'Datos Actualizados',
        description: 'El diagnóstico ha sido recalculado con los datos más recientes',
      });
    }, 1000);
  };

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">Diagnóstico Financiero Avanzado</h1>
        <p className="text-muted-foreground">Análisis completo e insights personalizados de tu salud financiera</p>
      </div>
      <div className="flex items-center space-x-3">
        <Button variant="outline" size="sm" onClick={handleRefreshData}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Actualizar
        </Button>
        <Activity className="w-8 h-8 text-blue-500" />
      </div>
    </div>
  );
}
