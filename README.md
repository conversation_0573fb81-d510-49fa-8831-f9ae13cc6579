# Finanz App - Comprehensive Personal Finance Management

Finanz App is a modern, user-friendly web application built to empower users in managing all aspects of their personal finances. From tracking daily expenses and income to managing debts, planning financial goals, and receiving personalized advice, Finanz App aims to be a comprehensive solution for financial well-being.

The application appears to be primarily in Spanish, judging by UI text in some components (e.g., "Gestión de Deudas", "Consejos Financieros").

## Table of Contents

- [✨ Key Features](#-key-features)
- [🛠️ Technology Stack](#️-technology-stack)
- [🚀 Getting Started](#-getting-started)
  - [Prerequisites](#prerequisites)
  - [Setup & Installation](#setup--installation)
  - [Running the Development Server](#running-the-development-server)
  - [Building for Production](#building-for-production)
  - [Running Tests](#running-tests)
- [📂 Project Structure](#-project-structure)
- [🤝 Contributing](#-contributing)
- [📜 License](#-license)

## ✨ Key Features

*   **Comprehensive Dashboard:** Get a quick overview of your financial health.
*   **Expense Tracking:** Monitor your spending habits.
*   **Income Management:** Keep track of your earnings.
*   **Debt Management:** Manage loans, credit cards, and personal debts.
*   **Financial Goal Setting:** Plan and track progress towards your financial objectives.
*   **Subscription Management:** Keep an eye on recurring subscriptions.
*   **Reimbursement Tracking:** Manage reimbursements owed or due.
*   **Payment Tracking:** Stay on top of upcoming and past payments.
*   **Pending by Default:** Newly added expenses start as `pending` until marked as paid.
*   **Personalized Financial Advice:** Receive tips to improve your financial situation.
*   **Financial Diagnostics:** Analyze your financial health in detail.
*   **Secure Authentication:** Protects your financial data.
*   **User Settings:** Customize your application experience.
*   **Landing Page:** Provides an overview of the app's benefits and features.

## 🛠️ Technology Stack

*   **Frontend:**
    *   React
    *   Vite (Build tool)
    *   TypeScript
    *   Tailwind CSS (Styling)
    *   Shadcn/UI (Component library, built on Radix UI)
    *   React Router (Routing)
    *   Recharts (Charting)
    *   Lucide Icons (Icons)
    *   `date-fns` (Date utilities)
    *   `react-hook-form` & `zod` (Forms and validation)
*   **Backend:**
    *   Supabase (Database, Auth, Functions)
*   **Testing:**
    *   Vitest
*   **Linting:**
    *   ESLint

## 🚀 Getting Started

### Prerequisites

*   Node.js (v18 or later recommended)
*   npm or yarn

### Setup & Installation

1.  **Clone the repository:**
    (Replace `your-repository-url-here` with the actual URL of the repository)
    ```bash
    git clone your-repository-url-here
    cd finanz-app
    ```
    (Assuming the directory name after cloning is `finanz-app`. Adjust if necessary.)

2.  **Install dependencies:**
    Using npm:
    ```bash
    npm install
    ```
    Or using yarn:
    ```bash
    yarn install
    ```

3.  **Set up Supabase:**
    This project uses Supabase for its backend. You will need to:
    *   Create a Supabase project (visit [supabase.com](https://supabase.com)).
    *   Set up your database schema. You can use the SQL files provided in the `supabase/migrations` directory.
    *   Obtain your Supabase Project URL and `anon` key from your Supabase project settings.
    *   Create a `.env` file in the root directory of the project. If an `.env.example` file exists, copy it to `.env`. Otherwise, create a new `.env` file. Add your Supabase credentials like so:
        ```env
        VITE_SUPABASE_URL="your_supabase_project_url"
        VITE_SUPABASE_ANON_KEY="your_supabase_anon_key"
        VITE_SUPABASE_PROJECT_ID="your_supabase_project_id"
        GEMINI_API_KEY="your_gemini_api_key" # used by the get-exchange-rate Edge Function
        ```
        **Note:** Ensure variables prefixed with `VITE_` are present so Vite exposes them to the frontend. The `GEMINI_API_KEY` is only required if you deploy the `get-exchange-rate` Supabase Edge Function.
    *   If the project uses Supabase Edge Functions (check the `supabase/functions` directory), deploy them using the Supabase CLI or through the Supabase dashboard.

### Running the Development Server

To start the development server:

Using npm:
```bash
npm run dev
```
Or using yarn:
```bash
yarn dev
```
The application will typically be available at `http://localhost:5173`.

### Building for Production

To create a production build:

Using npm:
```bash
npm run build
```
Or using yarn:
```bash
yarn build
```
The build artifacts will be stored in the `dist/` directory.

After building, a bundle analysis report is generated at `dist/bundle-report.html`.
Open this file in your browser to inspect the final bundle size and contents.

### Running Tests

To run the test suite:

Using npm:
```bash
npm run test
```
Or using yarn:
```bash
yarn test
```

## 📂 Project Structure

Here's a brief overview of the key directories in this project:

```
.
├── public/                   # Static assets
├── supabase/                 # Supabase backend configuration
│   ├── functions/            # Supabase edge functions
│   └── migrations/           # Database schema migrations
├── src/
│   ├── asesoria_financiera/  # Feature: Financial Advice
│   ├── components/           # Shared UI components
│   │   ├── auth/             # Authentication-specific components
│   │   ├── landing/          # Components for the landing page
│   │   ├── security/         # Security-related components
│   │   └── ui/               # Generic UI elements (likely from Shadcn/UI)
│   ├── configuracion_aplicacion/ # Feature: Application Settings
│   ├── contexts/             # React Context providers (e.g., AuthContext)
│   ├── dashboard_principal/  # Feature: Main Dashboard
│   │   └── components/
│   │       └── tabs/
│   │           ├── SummaryTab.tsx      # Formerly under dashboard_summary/
│   │           ├── AnalysisTab.tsx     # Formerly under dashboard_analysis/
│   │           ├── CashFlowTab.tsx     # Formerly under dashboard_cashflow/
│   │           └── InsightsTab.tsx     # Formerly under dashboard_insights/
│   ├── debt-management/       # Feature: Debt Management
│   ├── gestion_ingresos/     # Feature: Income Management
│   ├── gestion_reembolsos/   # Feature: Reimbursement Management
│   ├── gestion_suscripciones/ # Feature: Subscription Management
│   ├── hooks/                # Custom React hooks
│   ├── infraestructura_principal/ # Core infrastructure components (e.g. main sidebar)
│   ├── integrations/         # Third-party service integrations (e.g., Supabase client)
│   ├── lib/                  # Utility functions
│   ├── main.tsx              # Main application entry point
│   ├── App.tsx               # Root React component with routing
│   ├── pages/                # Top-level page components
│   ├── planificacion_metas/  # Feature: Financial Goals Planning
│   ├── seguimiento_pagos/    # Feature: Payment Tracking
│   ├── styles/               # Global styles and CSS
│   ├── types/                # TypeScript type definitions
│   └── utils/                # General utility functions and configurations
├── tests/                    # Vitest tests
├── .env.example              # Example environment variables (you should create a .env file)
├── package.json              # Project dependencies and scripts
├── vite.config.ts            # Vite configuration
├── tsconfig.json             # TypeScript configuration
└── README.md                 # This file
```

*   **`public/`**: Contains static assets like `favicon.ico` and `robots.txt`.
*   **`supabase/`**: Holds all Supabase-related files, including database migrations and serverless functions. This is crucial for the backend setup.
*   **`src/`**: The main application source code.
    *   **Feature-specific directories** (e.g., `asesoria_financiera/`, `debt-management/`, `gestion_ingresos/`): These directories encapsulate modules related to specific features of the application, often containing their own `components/`, `hooks/`, `utils/`, etc. This indicates a modular, feature-sliced design.
    *   **`components/`**: Contains reusable React components shared across different parts of the application.
        *   `auth/`: Components specifically for authentication (login, signup forms, etc.).
        *   `landing/`: Components used to build the public landing page.
        *   `security/`: Components related to application security features.
        *   `ui/`: Core UI elements, likely stemming from a library like Shadcn/UI, such as buttons, cards, dialogs.
    *   **`contexts/`**: Holds React Context API providers for managing global state (e.g., authentication state).
    *   **`hooks/`**: Custom React hooks used to encapsulate and reuse stateful logic.
    *   **`integrations/`**: Code for connecting to and interacting with external services, like the Supabase client.
    *   **`lib/`**: General utility functions that can be used throughout the application (e.g., `utils.ts`).
    *   **`pages/`**: Components that represent different pages/views of the application, typically mapped to routes.
    *   **`styles/`**: Global stylesheets, Tailwind CSS base styles, and theme-related CSS.
    *   **`types/`**: Shared TypeScript type definitions and interfaces.
    *   **`utils/`**: Broader utility functions, potentially including configuration, logging, etc.
    *   **`main.tsx`**: The entry point for the React application.
    *   **`App.tsx`**: The root component of the application, usually setting up routing and global providers.
*   **`tests/`**: Contains test files, likely using Vitest, for unit and integration testing.

*   **Configuration files** (e.g., `vite.config.ts`, `tailwind.config.ts`, `tsconfig.json`, `package.json`): Define how the project is built, styled, typed, and managed.

## Changelog

### [Unreleased]

* Added `is_recurring` column to the `expenses` table with a default of `false` and a `NOT NULL` constraint. Existing records are updated to `false` and an index on `(user_id, is_recurring)` improves query performance.

## 🤝 Contributing

Contributions are welcome! If you'd like to contribute to Finanz App, please follow these general guidelines:

1.  **Fork the repository.**
2.  **Create a new branch** for your feature or bug fix: `git checkout -b feature/your-feature-name` or `git checkout -b fix/your-bug-fix`.
3.  **Make your changes.** Ensure your code follows the project's coding style (e.g., run `npm run lint` or `yarn lint` to check).
4.  **Write tests** for your changes, if applicable.
5.  **Commit your changes** with a clear and descriptive commit message.
6.  **Push your branch** to your fork.
7.  **Open a pull request** to the main repository.

Please provide a detailed description of your changes in the pull request.

*(Further details on coding standards, specific contribution areas, or contact information can be added here as the project evolves.)*

## 📜 License

This project is currently unlicensed. (Or specify your license, e.g., MIT License, Apache 2.0, etc.)
