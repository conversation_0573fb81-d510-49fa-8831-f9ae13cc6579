import React, { useState, useEffect, useCallback } from 'react'; // Added useCallback
import { Income as IncomeType } from '@/types';
import { IncomeSection } from '../components/IncomeSection';
import { IncomeFormData } from '../components/IncomeFormData';
import { IncomeFormHeader } from '../components/IncomeFormHeader';
import { TrendingUp, AlertTriangle, RefreshCw } from 'lucide-react';
import { ROUTES } from '@/constants/routes';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useIncomeCalculations } from '../hooks/useIncomeCalculations';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useBreakpoint } from '@/hooks/useBreakpoint';

type ViewMode = 'list' | 'form';

export default function IncomePage() {
  const { user, session, loading: authLoading } = useAuth();
  const { isMobile, isTablet } = useBreakpoint();
  const [authCheckTimeout, setAuthCheckTimeout] = useState(false);
  const {
    // incomes, // Removed as per eslint rule (no-unused-vars)
    addIncome,
    updateIncome,
    defaultCurrency,
    isLoadingIncomes,
    errorIncomes,
    isAddingIncome,
    isUpdatingIncome
  } = useFinanceData();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [editingIncome, setEditingIncome] = useState<IncomeType | null>(null);
  const [isSuccessfullySubmitted, setIsSuccessfullySubmitted] = useState(false);
  
  // Add a timeout to prevent immediate "session expired" messages
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!authLoading && (!user || !session)) {
        setAuthCheckTimeout(true);
      }
    }, 3000); // Wait 3 seconds before considering session expired

    return () => clearTimeout(timer);
  }, [authLoading, user, session]);

  // Obtener el mes actual en formato YYYY-MM
  const getCurrentMonth = useCallback(() => { // Memoize if it were complex, but it's simple. For resetForm dep.
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }, []);

  const [formData, setFormData] = useState<Partial<IncomeType>>({
    month: getCurrentMonth(),
    currency: defaultCurrency as "DOP" | "USD",
    fixedSalary: 0,
    variablePercentage: 0,
    quarterlyIncentive: 0,
    performancePercentage: 0,
    vehicleDepreciation: 0,
    legalDeductions: 0,
    payrollLoan: 0,
    otherIncomeItems: []
  });

  const calculations = useIncomeCalculations(formData);

  const resetForm = useCallback(() => {
    setFormData({
      month: getCurrentMonth(),
      currency: defaultCurrency as "DOP" | "USD",
      fixedSalary: 0,
      variablePercentage: 0,
      quarterlyIncentive: 0,
      performancePercentage: 0,
      vehicleDepreciation: 0,
      legalDeductions: 0,
      payrollLoan: 0,
      otherIncomeItems: []
    });
    setEditingIncome(null);
  }, [defaultCurrency, getCurrentMonth]);

  const handleEdit = (income: IncomeType) => {
    // Asegurar que el mes se mantenga en el formato correcto
    setFormData({
      ...income,
      month: income.month // Mantener el mes exacto del ingreso
    });
    setEditingIncome(income);
    setViewMode('form');
  };

  const handleAddNew = () => {
    resetForm();
    setViewMode('form');
  };

  // Helper to get a user-friendly message from an error object
  const getErrorMessage = (error: unknown): string => {
    if (error && typeof error === 'object' && 'message' in error) {
        const errWithMessage = error as { message: string; details?: string };
        if (typeof errWithMessage.message === 'string') {
            if (errWithMessage.details && typeof errWithMessage.details === 'string') {
                return `${errWithMessage.message} - ${errWithMessage.details}`;
            }
            return errWithMessage.message;
        }
    }
    return 'An unexpected error occurred.';
 };

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['incomes'] });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Verificar autenticación antes de proceder
    if (!user || !session) {
      toast({ 
        title: "Error de Autenticación", 
        description: "Tu sesión ha expirado. Por favor, inicia sesión nuevamente.", 
        variant: "destructive" 
      });
      window.location.href = ROUTES.AUTH;
      return;
    }
    
    if (!formData.month || !formData.fixedSalary) {
      toast({ title: "Error", description: "Por favor complete los campos requeridos", variant: "destructive" });
      return;
    }

    if (!calculations) return;

    // Validar que el mes esté en formato YYYY-MM
    const monthRegex = /^\d{4}-\d{2}$/;
    if (!monthRegex.test(formData.month)) {
      toast({ title: "Error", description: "El formato del mes debe ser YYYY-MM", variant: "destructive" });
      return;
    }

    const incomeData: Omit<IncomeType, 'id'> = {
      month: formData.month!,
      currency: (formData.currency || defaultCurrency) as "DOP" | "USD",
      fixedSalary: formData.fixedSalary,
      variablePercentage: formData.variablePercentage || 0,
      variableAmount: calculations.variableAmount,
      quarterlyIncentive: calculations.quarterlyIncentive,
      performancePercentage: formData.performancePercentage || 0,
      vehicleDepreciation: formData.vehicleDepreciation || 0,
      legalDeductions: formData.legalDeductions || 0,
      payrollLoan: formData.payrollLoan || 0,
      grossIncome: calculations.grossIncome,
      netIncome: calculations.netIncome,
      otherIncomeItems: formData.otherIncomeItems || [],
      variableScenarios: calculations.variableScenarios
    };

    try {
      if (editingIncome) {
        await updateIncome(editingIncome.id, incomeData);
        toast({
          title: "Éxito",
          description: `Ingreso de ${formData.month} actualizado correctamente`,
        });
      } else {
        await addIncome(incomeData);
        toast({
          title: "Éxito",
          description: `Ingreso de ${formData.month} registrado correctamente`,
        });
      }
      setIsSuccessfullySubmitted(true);
    } catch (err: unknown) { // Typed err as unknown
      console.error('Error saving income:', err);
      
      const errorObj = err as { message?: string }; // For type-safe access to message

      // Manejar errores de autenticación específicamente
      if (errorObj.message && (errorObj.message.includes('not authenticated') || errorObj.message.includes('Session expired'))) {
        toast({
          title: "Sesión Expirada",
          description: "Tu sesión ha expirado. Redirigiendo al login...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = ROUTES.AUTH;
        }, 2000);
        return;
      }
      
      toast({
        title: "Error al Guardar",
        description: getErrorMessage(err),
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (isSuccessfullySubmitted && !isLoadingIncomes) {
      setViewMode('list');
      resetForm();
      setIsSuccessfullySubmitted(false); // Reset for next submission
    }
  }, [isSuccessfullySubmitted, isLoadingIncomes, setViewMode, resetForm]); // resetForm is now memoized

  // Show loading spinner while auth is initializing
  if (authLoading) {
    return (
      <div className={`
        flex justify-center items-center h-64
        ${isMobile ? 'p-3' : 'p-6'}
      `}>
        <div className="text-center space-y-4">
          <div className={`
            animate-spin rounded-full border-b-2 border-finanz-primary mx-auto
            ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}
          `}></div>
          <p className={`
            ${isMobile ? 'text-sm' : 'text-lg'}
          `}>
            Inicializando...
          </p>
        </div>
      </div>
    );
  }

  // Only show session expired after timeout and when actually not authenticated
  if (authCheckTimeout && (!user || !session)) {
    return (
      <div className={`
        text-center
        ${isMobile ? 'p-3' : 'p-6'}
      `}>
        <AlertTriangle className={`
          mx-auto text-red-500
          ${isMobile ? 'h-8 w-8' : 'h-12 w-12'}
        `} />
        <h2 className={`
          mt-2 font-semibold text-red-700
          ${isMobile ? 'text-lg' : 'text-xl'}
        `}>
          Sesión Expirada
        </h2>
        <p className={`
          mt-2 text-gray-600
          ${isMobile ? 'text-sm' : 'text-base'}
        `}>
          Tu sesión ha expirado. Redirigiendo al login...
        </p>
        <Button
          onClick={() => window.location.href = ROUTES.AUTH}
          className={`
            mt-4
            ${isMobile ? 'text-sm px-4 py-2' : ''}
          `}
        >
          Ir al Login
        </Button>
      </div>
    );
  }

  if (isLoadingIncomes) {
    return (
      <div className={`
        flex justify-center items-center h-64
        ${isMobile ? 'p-3' : 'p-6'}
      `}>
        <p className={`
          ${isMobile ? 'text-sm' : 'text-lg'}
        `}>
          Cargando ingresos...
        </p>
      </div>
    );
  }

  if (errorIncomes) {
    return (
      <div className={`
        text-center
        ${isMobile ? 'p-3' : 'p-6'}
      `}>
        <AlertTriangle className={`
          mx-auto text-red-500
          ${isMobile ? 'h-8 w-8' : 'h-12 w-12'}
        `} />
        <h2 className={`
          mt-2 font-semibold text-red-700
          ${isMobile ? 'text-lg' : 'text-xl'}
        `}>
          Error al Cargar Ingresos
        </h2>
        <p className={`
          mt-2 text-gray-600
          ${isMobile ? 'text-sm' : 'text-base'}
        `}>
          {getErrorMessage(errorIncomes)}
        </p>
        <Button 
          onClick={handleRefresh} 
          className={`
            mt-4
            ${isMobile ? 'text-sm px-4 py-2' : ''}
          `}
        >
          <RefreshCw className={`
            mr-2
            ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}
          `} /> 
          Reintentar
        </Button>
      </div>
    );
  }

  if (viewMode === 'list') {
    return (
      <div className={`
        ${isMobile ? 'p-3 space-y-4' : isTablet ? 'p-4 space-y-5' : 'p-6 space-y-6'}
      `}>
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`
              font-bold text-gray-900
              ${isMobile ? 'text-xl' : isTablet ? 'text-2xl' : 'text-2xl'}
            `}>
              Gestión de Ingresos
            </h1>
            <p className={`
              text-finanz-text-secondary
              ${isMobile ? 'text-xs' : 'text-sm'}
            `}>
              Administra y visualiza tus ingresos mensuales
            </p>
          </div>
          <TrendingUp className={`
            text-finanz-success
            ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}
          `} />
        </div>
        
        <IncomeSection 
          onAddNew={handleAddNew}
          onEdit={handleEdit}
        />
      </div>
    );
  }

  return (
    <div className={`
      ${isMobile ? 'p-3 space-y-4' : isTablet ? 'p-4 space-y-5' : 'p-6 space-y-6'}
    `}>
      <IncomeFormHeader
        formData={formData}
        editingIncome={editingIncome}
        onBack={() => setViewMode('list')}
      />

      <IncomeFormData
        formData={formData}
        setFormData={setFormData}
        defaultCurrency={defaultCurrency as "DOP" | "USD"}
        onSubmit={handleSubmit}
        onCancel={() => setViewMode('list')}
        editingIncome={editingIncome}
        calculations={calculations}
        isSubmitting={isAddingIncome || isUpdatingIncome}
      />
    </div>
  );
}
