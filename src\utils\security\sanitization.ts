export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') {
    return '';
  }

  return input
    .trim()
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '')
    .substring(0, 1000);
};

export const sanitizeFinancialAmount = (amount: number): number => {
  if (typeof amount !== 'number' || !Number.isFinite(amount)) {
    return 0;
  }

  return Math.max(0, Math.round(amount * 100) / 100);
};

export const sanitizeCurrency = (currency: string): string => {
  const validCurrencies = ['DOP', 'USD', 'EUR'];
  const sanitized = sanitizeInput(currency).toUpperCase();
  return validCurrencies.includes(sanitized) ? sanitized : 'DOP';
};

export const sanitizeDate = (date: string): string => {
  const sanitized = sanitizeInput(date);

  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(sanitized)) {
    return new Date().toISOString().split('T')[0];
  }

  const parsedDate = new Date(sanitized);
  if (isNaN(parsedDate.getTime())) {
    return new Date().toISOString().split('T')[0];
  }

  return sanitized;
};

export const sanitizeEmail = (email: string): string => {
  return sanitizeInput(email).toLowerCase();
};

export const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};
