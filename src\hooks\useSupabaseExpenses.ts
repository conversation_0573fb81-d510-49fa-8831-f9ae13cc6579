import { logger } from "@/utils/logger";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Expense } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { QUERY_KEYS } from '@/constants/queryKeys';

const QUERY_KEY = QUERY_KEYS.EXPENSES;

// Transform database object to TypeScript interface
const transformExpense = (dbExpense: any): Expense => ({
  id: dbExpense.id,
  date: dbExpense.date,
  month: dbExpense.month,
  type: dbExpense.type as "Fijo" | "Variable",
  categoryId: dbExpense.category_id,
  categoryName: dbExpense.category_name,
  amount: dbExpense.amount,
  description: dbExpense.description,
  paymentMethod: dbExpense.payment_method as "cash" | "debit-card" | "credit-card" | "transfer",
  status: dbExpense.status as "pending" | "paid",
  currency: dbExpense.currency as "DOP" | "USD",
  paymentDate: dbExpense.payment_date ?? dbExpense.date,
  isRecurring: dbExpense.is_recurring ?? false,
  createdAt: dbExpense.created_at,
  updatedAt: dbExpense.updated_at
});

// Transform TypeScript interface to database object
const transformExpenseToDb = (expense: Omit<Expense, 'id' | 'month' | 'createdAt' | 'updatedAt'>, userId: string) => ({
  date: expense.date,
  month: expense.date.slice(0, 7),
  type: expense.type,
  category_id: expense.categoryId,
  category_name: expense.categoryName,
  amount: expense.amount,
  description: expense.description,
  payment_method: expense.paymentMethod,
  status: expense.status,
  currency: expense.currency,
  payment_date: expense.paymentDate,
  is_recurring: expense.isRecurring,
  user_id: userId
});

export const useSupabaseExpenses = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Función mejorada para invalidar queries relacionadas con sincronización inmediata
  const invalidateRelatedQueries = async () => {
    if (!user?.id) return;
    
    const relatedQueries = [
      [QUERY_KEY, user.id],
      [QUERY_KEYS.PAYMENT_RECORDS, user.id],
      [QUERY_KEYS.DEBTS, user.id],
      [QUERY_KEYS.OTHER_DATA, user.id],
      [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, user.id]
    ];

    // Invalidar todas las queries relacionadas de forma inmediata
    await Promise.all(
      relatedQueries.map(queryKey =>
        queryClient.invalidateQueries({ queryKey, exact: true })
      )
    );

    // Forzar refetch inmediato de datos críticos para la UI
    await queryClient.refetchQueries({ 
      queryKey: [QUERY_KEY, user.id], 
      exact: true 
    });
    
    // También refetch de pagos para sincronización inmediata
    setTimeout(() => {
      queryClient.refetchQueries({
        queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user.id],
        exact: true
      });
    }, 100);
  };

  // Fetch expenses
  const { data: expenses = [], isLoading, error } = useQuery({
    queryKey: [QUERY_KEY, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      logger.debug('Fetching expenses from Supabase for user:', user.id);
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id)
        .not('payment_date', 'is', null)
        .order('date', { ascending: false });
      
      if (error) {
        console.error('Error fetching expenses:', error);
        throw error;
      }
      
      if (!data) {
        logger.debug('Fetched no expenses');
        return [] as Expense[];
      }

      const validExpenses = data.filter(exp => exp.payment_date !== null);
      const invalidIds = data
        .filter(exp => exp.payment_date === null)
        .map(exp => exp.id);

      if (invalidIds.length > 0) {
        await supabase.from('expenses').delete().in('id', invalidIds);
      }

      const transformed = validExpenses.map(transformExpense);

      logger.debug('Fetched expenses count:', transformed.length);
      logger.debug('Sample expenses:', transformed.slice(0, 3).map(e => ({
        id: e.id,
        date: e.date,
        paymentDate: e.paymentDate,
        month: e.month,
        status: e.status
      })));
      return transformed;
    },
    enabled: !!user?.id && enabled,
    staleTime: 1000 * 60 * 2, // Reducido para mayor sincronización
    gcTime: 1000 * 60 * 5,
  });

  // Add expense mutation
  const addExpenseMutation = useMutation({
    mutationFn: async (expense: Omit<Expense, 'id' | 'month' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const dbExpense = transformExpenseToDb(expense, user.id);
      
      logger.debug('Adding expense to Supabase:', dbExpense);
      const { data, error } = await supabase
        .from('expenses')
        .insert([dbExpense])
        .select()
        .single();
      
      if (error) {
        console.error('Error adding expense:', error);
        throw error;
      }
      
      logger.debug('Added expense:', data);
      return transformExpense(data);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  // Update expense mutation with optimistic updates
  const updateExpenseMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Expense> }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      logger.debug('Updating expense in Supabase:', id, updates);
      const dbUpdates: any = {};
      
      if (updates.date !== undefined) {
        dbUpdates.date = updates.date;
        dbUpdates.month = updates.date.slice(0, 7);
      }
      if (updates.type !== undefined) dbUpdates.type = updates.type;
      if (updates.categoryId !== undefined) dbUpdates.category_id = updates.categoryId;
      if (updates.categoryName !== undefined) dbUpdates.category_name = updates.categoryName;
      if (updates.amount !== undefined) dbUpdates.amount = updates.amount;
      if (updates.description !== undefined) dbUpdates.description = updates.description;
      if (updates.paymentMethod !== undefined) dbUpdates.payment_method = updates.paymentMethod;
      if (updates.status !== undefined) dbUpdates.status = updates.status;
      if (updates.currency !== undefined) dbUpdates.currency = updates.currency;
      if (updates.paymentDate !== undefined) dbUpdates.payment_date = updates.paymentDate;
      if (updates.isRecurring !== undefined) dbUpdates.is_recurring = updates.isRecurring;
      
      const { data, error } = await supabase
        .from('expenses')
        .update(dbUpdates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
      
      if (error) {
        console.error('Error updating expense:', error);
        throw error;
      }
      
      logger.debug('Updated expense:', data);
      return transformExpense(data);
    },
    onMutate: async ({ id, updates }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [QUERY_KEY, user?.id] });

      // Snapshot the previous value
      const previousExpenses = queryClient.getQueryData([QUERY_KEY, user?.id]);

      // Optimistically update to the new value
      queryClient.setQueryData([QUERY_KEY, user?.id], (old: Expense[] | undefined) => {
        if (!old) return old;
        return old.map(expense => 
          expense.id === id 
            ? { ...expense, ...updates, updatedAt: new Date().toISOString() }
            : expense
        );
      });

      // Return a context object with the snapshotted value
      return { previousExpenses };
    },
    onError: (err, { id, updates }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData([QUERY_KEY, user?.id], context?.previousExpenses);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  // Delete expense mutation
  const deleteExpenseMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      logger.debug('Deleting expense from Supabase:', id);
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
      
      if (error) {
        console.error('Error deleting expense:', error);
        throw error;
      }
      
      logger.debug('Deleted expense with id:', id);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  return {
    expenses,
    isLoading,
    error,
    addExpense: addExpenseMutation.mutate,
    updateExpense: (id: string, updates: Partial<Expense>) => 
      updateExpenseMutation.mutate({ id, updates }),
    deleteExpense: deleteExpenseMutation.mutate,
    isAddingExpense: addExpenseMutation.isPending,
    isUpdatingExpense: updateExpenseMutation.isPending,
    isDeletingExpense: deleteExpenseMutation.isPending,
  };
};
