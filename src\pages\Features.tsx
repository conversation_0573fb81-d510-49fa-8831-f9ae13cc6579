
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  TrendingUp, 
  Calculator, 
  CreditCard, 
  PiggyBank, 
  Bell, 
  BarChart3,
  Shield,
  Smartphone,
  Cloud,
  Download,
  ArrowLeft
} from 'lucide-react';
import { Link } from 'react-router-dom';

export default function FeaturesPage() {
  const detailedFeatures = [
    {
      icon: TrendingUp,
      title: "Gestión Completa de Ingresos",
      description: "Registra salarios fijos, variables, incentivos, depreciaciones y deducciones",
      features: [
        "Cálculo automático de variables",
        "Escenarios de ingresos (base, medio, óptimo)",
        "Historial mensual detallado",
        "Integración con nómina"
      ]
    },
    {
      icon: Calculator,
      title: "Control Inteligente de Gastos",
      description: "Categorización automática y análisis de patrones de gasto",
      features: [
        "Categorías personalizables",
        "Gastos recurrentes",
        "Presupuesto restante",
        "Filtros avanzados"
      ]
    },
    {
      icon: CreditCard,
      title: "Manejo Profesional de Deudas",
      description: "Gestiona tarjetas, préstamos y deudas personales eficientemente",
      features: [
        "Calculadora de pagos",
        "Alertas de vencimiento",
        "Estrategias de pago",
        "Historial crediticio"
      ]
    },
    {
      icon: PiggyBank,
      title: "Metas Financieras Inteligentes",
      description: "Define, sigue y alcanza tus objetivos financieros",
      features: [
        "Seguimiento de progreso",
        "Contribuciones automáticas",
        "Proyecciones temporales",
        "Hitos y recompensas"
      ]
    },
    {
      icon: Bell,
      title: "Notificaciones Proactivas",
      description: "Mantente al día con recordatorios y alertas inteligentes",
      features: [
        "Pagos pendientes",
        "Vencimientos próximos",
        "Metas alcanzadas",
        "Cambios importantes"
      ]
    },
    {
      icon: BarChart3,
      title: "Análisis y Reportes",
      description: "Insights profundos sobre tu salud financiera",
      features: [
        "Gráficos interactivos",
        "Tendencias temporales",
        "Comparativas mensuales",
        "Exportación de datos"
      ]
    }
  ];

  const technicalFeatures = [
    {
      icon: Shield,
      title: "Seguridad de Datos",
      description: "Encriptación de extremo a extremo y backup automático"
    },
    {
      icon: Smartphone,
      title: "Multiplataforma",
      description: "Acceso desde cualquier dispositivo con sincronización en tiempo real"
    },
    {
      icon: Cloud,
      title: "Sincronización Cloud",
      description: "Tus datos siempre actualizados y disponibles"
    },
    {
      icon: Download,
      title: "Exportación Flexible",
      description: "Exporta a PDF, Excel y otros formatos populares"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <Link to="/" className="inline-flex items-center text-finanz-primary hover:text-finanz-primary/80">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver al Inicio
          </Link>
        </div>
      </header>

      {/* Hero */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Características Completas
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Descubre todas las herramientas que FinanzApp pone a tu disposición 
            para una gestión financiera profesional
          </p>
        </div>
      </section>

      {/* Detailed Features */}
      <section className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {detailedFeatures.map((feature, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <div className="w-12 h-12 bg-finanz-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="w-6 h-6 text-finanz-primary" />
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
                <CardDescription>{feature.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {feature.features.map((item, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-finanz-success rounded-full mr-3" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Technical Features */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Características Técnicas</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {technicalFeatures.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-finanz-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="w-6 h-6 text-finanz-primary" />
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="bg-finanz-primary py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            ¿Listo para tomar control de tus finanzas?
          </h2>
          <p className="text-finanz-primary/20 mb-8 max-w-2xl mx-auto">
            Únete a miles de usuarios que ya confían en FinanzApp para gestionar sus finanzas
          </p>
          <Link to="/dashboard">
            <Button size="lg" variant="secondary" className="px-8 py-3">
              Comenzar Gratis
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}
