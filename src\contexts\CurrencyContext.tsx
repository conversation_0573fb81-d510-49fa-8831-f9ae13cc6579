/* eslint-disable react-refresh/only-export-components */

import React, { createContext, useContext, useEffect, useState } from 'react';

export type Currency = 'DOP' | 'USD';

interface CurrencyContextValue {
  defaultCurrency: Currency;
  setDefaultCurrency: (currency: Currency) => void;
}

const CurrencyContext = createContext<CurrencyContextValue | undefined>(undefined);

export const CurrencyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [defaultCurrency, setDefaultCurrencyState] = useState<Currency>('DOP');

  useEffect(() => {
    const stored = localStorage.getItem('finanz_default_currency');
    if (stored === 'DOP' || stored === 'USD') {
      setDefaultCurrencyState(stored);
    }
  }, []);

  const setDefaultCurrency = (currency: Currency) => {
    setDefaultCurrencyState(currency);
    localStorage.setItem('finanz_default_currency', currency);
  };

  return (
    <CurrencyContext.Provider value={{ defaultCurrency, setDefaultCurrency }}>
      {children}
    </CurrencyContext.Provider>
  );
};

export const useCurrency = () => {
  const ctx = useContext(CurrencyContext);
  if (!ctx) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return ctx;
};
