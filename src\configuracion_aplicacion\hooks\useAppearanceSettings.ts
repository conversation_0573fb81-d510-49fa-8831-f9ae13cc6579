import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import { appearanceSchema, type AppearanceFormData } from '../schemas/settingsSchemas';

export const useAppearanceSettings = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const appearanceForm = useForm<AppearanceFormData>({
    resolver: zodResolver(appearanceSchema),
    defaultValues: {
      theme: 'light',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'es-DO',
      showCurrency: true,
    },
  });

  // Cargar configuración inicial una sola vez cuando el hook se monte
  const loadAppearanceSettings = useCallback(() => {
    try {
      const savedAppearance = localStorage.getItem('finanz_appearance_settings');
      
      if (savedAppearance) {
        const appearanceData = JSON.parse(savedAppearance);
        appearanceForm.reset(appearanceData);
        applyTheme(appearanceData.theme);
      } else {
        // Para nuevos usuarios, usar tema claro como predeterminado
        const defaultTheme: 'light' | 'dark' = 'light';
        applyTheme(defaultTheme);
        appearanceForm.setValue('theme', defaultTheme);
      }
    } catch (error) {
      console.error('Error loading appearance settings:', error);
    }
  }, [appearanceForm]);

  useEffect(() => {
    loadAppearanceSettings();
  }, [loadAppearanceSettings]);

  const applyTheme = (theme: 'light' | 'dark') => {
    const root = document.documentElement;
    root.classList.toggle('dark', theme === 'dark');
  };

  // Nueva función para aplicar tema inmediatamente sin guardar
  const applyThemeImmediately = (theme: 'light' | 'dark') => {
    applyTheme(theme);
    appearanceForm.setValue('theme', theme);
  };

  const saveAppearance = async (data: AppearanceFormData) => {
    try {
      setIsLoading(true);
      localStorage.setItem('finanz_appearance_settings', JSON.stringify(data));
      
      applyTheme(data.theme);
      
      toast({
        title: 'Apariencia actualizada',
        description: `Tema ${data.theme === 'dark' ? 'oscuro' : 'claro'} aplicado correctamente`,
      });
    } catch (error) {
      console.error('Error saving appearance:', error);
      toast({
        title: 'Error',
        description: 'No se pudo guardar la configuración de apariencia',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    appearanceForm,
    saveAppearance,
    applyThemeImmediately,
    isLoading,
  };
};
