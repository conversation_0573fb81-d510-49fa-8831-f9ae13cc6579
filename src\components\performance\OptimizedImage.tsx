import React, { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  priority?: boolean;
  placeholder?: string;
  blurDataURL?: string;
  sizes?: string;
  quality?: number;
  loading?: 'lazy' | 'eager';
  className?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  priority = false,
  placeholder,
  blurDataURL,
  sizes,
  quality = 75,
  loading = 'lazy',
  className = '',
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer para lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Cargar 50px antes de que sea visible
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  // Generar srcSet para diferentes tamaños
  const generateSrcSet = (baseSrc: string) => {
    const sizes = [640, 768, 1024, 1280, 1536];
    return sizes
      .map(size => `${baseSrc}?w=${size}&q=${quality} ${size}w`)
      .join(', ');
  };

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setError(true);
  };

  // Placeholder mientras carga
  const PlaceholderComponent = () => (
    <div 
      className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
      style={{ aspectRatio: '16/9' }}
    >
      {placeholder ? (
        <span className="text-gray-400 text-sm">{placeholder}</span>
      ) : (
        <svg 
          className="w-8 h-8 text-gray-400" 
          fill="currentColor" 
          viewBox="0 0 20 20"
        >
          <path 
            fillRule="evenodd" 
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
            clipRule="evenodd" 
          />
        </svg>
      )}
    </div>
  );

  // Error fallback
  if (error) {
    return (
      <div 
        className={`bg-red-100 border border-red-300 rounded flex items-center justify-center ${className}`}
        style={{ aspectRatio: '16/9' }}
      >
        <span className="text-red-500 text-sm">Error al cargar imagen</span>
      </div>
    );
  }

  // No cargar hasta que esté en vista (a menos que sea priority)
  if (!isInView) {
    return <PlaceholderComponent />;
  }

  return (
    <div className="relative overflow-hidden">
      {/* Blur placeholder si está disponible */}
      {blurDataURL && !isLoaded && (
        <img
          src={blurDataURL}
          alt=""
          className={`absolute inset-0 w-full h-full object-cover filter blur-sm scale-110 ${className}`}
          aria-hidden="true"
        />
      )}
      
      {/* Imagen principal */}
      <img
        ref={imgRef}
        src={src}
        alt={alt}
        srcSet={generateSrcSet(src)}
        sizes={sizes || '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'}
        loading={priority ? 'eager' : loading}
        decoding={priority ? 'sync' : 'async'}
        fetchPriority={priority ? 'high' : 'auto'}
        onLoad={handleLoad}
        onError={handleError}
        className={`
          transition-opacity duration-300
          ${isLoaded ? 'opacity-100' : 'opacity-0'}
          ${className}
        `}
        {...props}
      />
      
      {/* Loading overlay */}
      {!isLoaded && !blurDataURL && <PlaceholderComponent />}
    </div>
  );
};

// Hook para preload de imágenes críticas
export const useImagePreload = (src: string, priority = false) => {
  useEffect(() => {
    if (!priority) return;

    const img = new Image();
    img.src = src;
    
    // Preload también versiones responsive
    const sizes = [640, 768, 1024];
    sizes.forEach(size => {
      const responsiveImg = new Image();
      responsiveImg.src = `${src}?w=${size}&q=75`;
    });
  }, [src, priority]);
};

// Componente para hero images con optimización especial
export const HeroImage: React.FC<OptimizedImageProps> = (props) => {
  return (
    <OptimizedImage
      {...props}
      priority={true}
      loading="eager"
      sizes="100vw"
      className={`w-full h-full object-cover ${props.className || ''}`}
    />
  );
};

// Componente para avatares con optimización especial
export const AvatarImage: React.FC<OptimizedImageProps> = (props) => {
  return (
    <OptimizedImage
      {...props}
      sizes="(max-width: 768px) 64px, 96px"
      className={`rounded-full ${props.className || ''}`}
    />
  );
};
