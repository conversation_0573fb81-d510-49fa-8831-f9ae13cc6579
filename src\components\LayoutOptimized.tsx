
import React, { useRef } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { CSSTransition, SwitchTransition } from 'react-transition-group';
import { AppSidebar } from '@/infraestructura_principal/components/AppSidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { TopBar } from '@/components/TopBar';
import { useDataPrefetch } from '@/hooks/useDataPrefetch';
import { usePagePreload } from '@/hooks/usePagePreload';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { useOrientation } from '@/hooks/useOrientation';

export const LayoutOptimized: React.FC = () => {
  // Prefetch datos para navegación rápida
  useDataPrefetch();
  usePagePreload();
  
  const location = useLocation();
  const nodeRef = useRef<HTMLDivElement>(null);
  const { isMobile, isTablet, deviceType, isTouchDevice } = useBreakpoint();
  const { orientation } = useOrientation();

  // Configurar viewport meta dinámicamente
  React.useEffect(() => {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      if (isMobile) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover');
      } else {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
      }
    }
  }, [isMobile]);

  return (
    <SidebarProvider>
      <div className={`
        min-h-screen w-full flex
        ${isMobile ? 'flex-col' : 'flex-row'}
      `}>
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-w-0">
          <TopBar />
          <main className={`
            flex-1 overflow-hidden transition-all duration-200
            ${isMobile ? 
              'p-2 pb-safe-area-inset-bottom' : 
              isTablet ? 
                'p-3' : 
                'p-4'
            }
            ${isMobile && orientation === 'landscape' ? 'p-1' : ''}
          `}>
            <SwitchTransition mode="in-out">
              <CSSTransition
                key={location.pathname}
                nodeRef={nodeRef}
                classNames={{
                  enter: 'page-enter',
                  enterActive: 'page-enter-active',
                  exit: 'page-exit',
                  exitActive: 'page-exit-active',
                }}
                timeout={isMobile ? 200 : 300}
                unmountOnExit
              >
                <div 
                  ref={nodeRef} 
                  className={`
                    h-full w-full
                    ${isMobile ? 'touch-pan-y overflow-y-auto' : ''}
                    ${isTouchDevice ? 'touch-manipulation' : ''}
                  `}
                  data-device-type={deviceType}
                  data-orientation={orientation}
                >
                  <Outlet />
                </div>
              </CSSTransition>
            </SwitchTransition>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};
