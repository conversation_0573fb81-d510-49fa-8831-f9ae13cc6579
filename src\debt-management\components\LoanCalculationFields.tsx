
import React from 'react';
import { Label } from '@/components/ui/label';
import { NumericInput, PercentageInput, IntegerInput } from '@/components/ui/numeric-input';

interface LoanCalculationFieldsProps {
  totalAmount: number;
  loanTerm: number;
  interestRate: number;
  monthlyPayment: number;
  currency: 'DOP' | 'USD';
  onTotalAmountChange: (value: number | null) => void;
  onLoanTermChange: (value: number | null) => void;
  onInterestRateChange: (value: number | null) => void;
}

export function LoanCalculationFields({
  totalAmount,
  loanTerm,
  interestRate,
  monthlyPayment,
  currency,
  onTotalAmountChange,
  onLoanTermChange,
  onInterestRateChange
}: LoanCalculationFieldsProps) {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="totalAmount">Monto Total *</Label>
        <NumericInput
          value={totalAmount}
          onChange={onTotalAmountChange}
          currency={currency}
          showCurrency
          autoComplete="off"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="loanTerm">Plazo del Préstamo (meses) *</Label>
        <IntegerInput
          value={loanTerm}
          onChange={onLoanTermChange}
          placeholder="Ej: 24"
          autoComplete="off"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="interestRate">Tasa de Interés (%) *</Label>
        <PercentageInput
          value={interestRate}
          onChange={onInterestRateChange}
          placeholder="Ej: 15.5"
          autoComplete="off"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="monthlyPayment">Pago Mensual (Calculado Automáticamente)</Label>
        <NumericInput
          value={monthlyPayment}
          onChange={() => {}} // Campo de solo lectura
          currency={currency}
          showCurrency
          disabled
          className="bg-gray-50"
          autoComplete="off"
        />
        <p className="text-xs text-gray-600">
          Este valor se calcula automáticamente basado en el monto, plazo e interés
        </p>
      </div>
    </>
  );
}
