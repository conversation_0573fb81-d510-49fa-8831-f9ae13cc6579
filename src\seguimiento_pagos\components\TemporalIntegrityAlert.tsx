import React from 'react';
import { AlertTriangle, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { PaymentItem } from '../types/paymentTypes';
import { validateExpenseTemporalIntegrity } from '../utils/temporalUtils';
import { logger } from '@/utils/logger';

interface TemporalIntegrityAlertProps {
  payments: PaymentItem[];
  currentPeriod: {
    year: number;
    month: number;
    displayName: string;
  };
  onRefresh?: () => void;
}

export function TemporalIntegrityAlert({ 
  payments, 
  currentPeriod,
  onRefresh 
}: TemporalIntegrityAlertProps) {
  // Check for temporal inconsistencies in the payments
  const inconsistentPayments = React.useMemo(() => {
    const issues: Array<{
      payment: PaymentItem;
      issue: string;
    }> = [];

    payments.forEach(payment => {
      // Only check expense-type payments for now
      if (payment.type === 'expense') {
        // Log for debugging
        logger.debug(`Checking temporal integrity for payment: ${payment.name} (${payment.id})`);
        
        // This is a simplified check - in a real scenario, we'd need access to the original expense data
        // For now, we can at least check if the payment appears to be in an impossible state
        const paymentDate = new Date(payment.dueDate);
        const currentPeriodEnd = new Date(currentPeriod.year, currentPeriod.month - 1, 0, 23, 59, 59);
        
        // Check if this is marked as overdue in a future period
        if (payment.status === 'overdue' && paymentDate > currentPeriodEnd) {
          issues.push({
            payment,
            issue: `Pago marcado como vencido en período futuro`
          });
        }
        
        // Check if payment ID suggests it's from a different period than displayed
        const idMatch = payment.id.match(/(\d{4})-(\d{1,2})$/);
        if (idMatch) {
          const idYear = parseInt(idMatch[1]);
          const idMonth = parseInt(idMatch[2]);
          
          if (idYear !== currentPeriod.year || idMonth !== currentPeriod.month) {
            // This might be a carried-over payment, which is okay if marked as such
            if (!(payment as any).isCarriedOver) {
              issues.push({
                payment,
                issue: `ID del pago sugiere período diferente: ${idYear}-${idMonth}`
              });
            }
          }
        }
      }
    });

    return issues;
  }, [payments, currentPeriod]);

  if (inconsistentPayments.length === 0) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Inconsistencias Temporales Detectadas</AlertTitle>
      <AlertDescription className="mt-2">
        <div className="space-y-2">
          <p>
            Se han detectado {inconsistentPayments.length} pago(s) con inconsistencias temporales 
            en el período {currentPeriod.displayName}.
          </p>
          
          <div className="mt-3 space-y-1 text-sm">
            {inconsistentPayments.slice(0, 3).map((item, index) => (
              <div key={index} className="flex items-start gap-2">
                <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>
                  <strong>{item.payment.name}:</strong> {item.issue}
                </span>
              </div>
            ))}
            {inconsistentPayments.length > 3 && (
              <div className="text-muted-foreground">
                ...y {inconsistentPayments.length - 3} más
              </div>
            )}
          </div>

          <div className="mt-4 flex gap-2">
            {onRefresh && (
              <Button size="sm" variant="outline" onClick={onRefresh}>
                Recargar Datos
              </Button>
            )}
            <Button 
              size="sm" 
              variant="ghost"
              onClick={() => {
                logger.info('Temporal integrity issues:', inconsistentPayments);
                console.log('Detalles de inconsistencias:', inconsistentPayments);
              }}
            >
              Ver Detalles en Consola
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
} 