
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Calendar, DollarSign } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface FinancialProjectionsProps {
  netIncome: number;
  totalExpenses: number;
  savingsRate: number;
}

export function FinancialProjections({
  netIncome,
  totalExpenses,
  savingsRate
}: FinancialProjectionsProps) {
  const currentSavings = netIncome - totalExpenses;
  const { isMobile } = useBreakpoint();
  
  
  const projections = [
    {
      period: '6 meses',
      timeframe: 6,
      scenarios: {
        conservative: {
          growthRate: 0.02,
          description: 'Crecimiento conservador (2%)',
          color: 'bg-blue-100 text-blue-800'
        },
        moderate: {
          growthRate: 0.05,
          description: 'Crecimiento moderado (5%)',
          color: 'bg-green-100 text-green-800'
        },
        optimistic: {
          growthRate: 0.08,
          description: 'Crecimiento optimista (8%)',
          color: 'bg-purple-100 text-purple-800'
        }
      }
    },
    {
      period: '1 año',
      timeframe: 12,
      scenarios: {
        conservative: {
          growthRate: 0.02,
          description: 'Crecimiento conservador (2%)',
          color: 'bg-blue-100 text-blue-800'
        },
        moderate: {
          growthRate: 0.05,
          description: 'Crecimiento moderado (5%)',
          color: 'bg-green-100 text-green-800'
        },
        optimistic: {
          growthRate: 0.08,
          description: 'Crecimiento optimista (8%)',
          color: 'bg-purple-100 text-purple-800'
        }
      }
    },
    {
      period: '5 años',
      timeframe: 60,
      scenarios: {
        conservative: {
          growthRate: 0.04,
          description: 'Crecimiento conservador (4%)',
          color: 'bg-blue-100 text-blue-800'
        },
        moderate: {
          growthRate: 0.07,
          description: 'Crecimiento moderado (7%)',
          color: 'bg-green-100 text-green-800'
        },
        optimistic: {
          growthRate: 0.10,
          description: 'Crecimiento optimista (10%)',
          color: 'bg-purple-100 text-purple-800'
        }
      }
    }
  ];

  const calculateProjection = (months: number, monthlyGrowthRate: number) => {
    let totalSavings = 0;
    let currentMonthlySavings = Math.max(0, currentSavings); // Solo proyectar si hay ahorros positivos
    
    for (let i = 0; i < months; i++) {
      totalSavings += currentMonthlySavings;
      currentMonthlySavings *= (1 + monthlyGrowthRate);
    }
    
    return totalSavings;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-finanz-primary" />
            <span>Proyecciones Financieras</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentSavings <= 0 ? (
            <div className="text-center p-8 bg-red-50 rounded-lg">
              <h4 className="text-lg font-semibold text-red-800 mb-2">
                No es posible realizar proyecciones
              </h4>
              <p className="text-red-600">
                Actualmente tienes un déficit de {formatCurrency(Math.abs(currentSavings), 'DOP')} mensual.
                Necesitas reducir tus gastos o aumentar tus ingresos para poder ahorrar.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">Capacidad de Ahorro Actual</h4>
                <p className={`font-bold text-green-600 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {formatCurrency(currentSavings, 'DOP')} / mes
                </p>
                <p className="text-sm text-green-600 mt-1">
                  Tasa de ahorro: {savingsRate.toFixed(1)}%
                </p>
              </div>

              {projections.map((projection) => (
                <div key={projection.period} className="border rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-4">
                    <Calendar className="w-4 h-4 text-finanz-primary" />
                    <h4 className="font-semibold">{projection.period}</h4>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(projection.scenarios).map(([key, scenario]) => {
                      const monthlyGrowthRate = scenario.growthRate / 12;
                      const projectedSavings = calculateProjection(projection.timeframe, monthlyGrowthRate);
                      
                      return (
                        <div key={key} className="bg-finanz-neutral/5 rounded-lg p-3">
                          <Badge className={`mb-2 ${scenario.color}`}>
                            {scenario.description}
                          </Badge>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-1">
                              <DollarSign className="w-4 h-4 text-finanz-success" />
                              <span className="text-sm text-gray-600">Ahorros acumulados:</span>
                            </div>
                            <div className="text-lg font-bold text-finanz-success">
                              {formatCurrency(projectedSavings, 'DOP')}
                            </div>
                            
                            <div className="text-xs text-gray-500 mt-2">
                              Crecimiento mensual: {(scenario.growthRate / 12 * 100).toFixed(2)}%
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Metas de Ahorro Sugeridas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-finanz-success/10 rounded-lg p-4">
              <h4 className="font-semibold text-finanz-success mb-2">Meta Corto Plazo (6 meses)</h4>
              <p className="text-sm text-gray-600 mb-2">Fondo de emergencia básico</p>
              <div className={`font-bold text-finanz-success ${isMobile ? 'text-lg' : 'text-xl'}`}>
                {formatCurrency(totalExpenses * 3, 'DOP')}
              </div>
              {currentSavings > 0 && (
                <div className="text-xs text-gray-500 mt-1">
                  ~{formatCurrency((totalExpenses * 3) / 6, 'DOP')} mensual
                </div>
              )}
            </div>
            
            <div className="bg-finanz-primary/10 rounded-lg p-4">
              <h4 className="font-semibold text-finanz-primary mb-2">Meta Largo Plazo (5 años)</h4>
              <p className="text-sm text-gray-600 mb-2">Independencia financiera</p>
              <div className={`font-bold text-finanz-primary ${isMobile ? 'text-lg' : 'text-xl'}`}>
                {formatCurrency(netIncome * 12 * 5, 'DOP')}
              </div>
              {currentSavings > 0 && (
                <div className="text-xs text-gray-500 mt-1">
                  ~{formatCurrency(netIncome, 'DOP')} mensual requerido
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
