
-- Enable Row Level Security on payment_projections table
ALTER TABLE public.payment_projections ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for payment_projections
CREATE POLICY "Users can view their own payment projections" 
  ON public.payment_projections 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own payment projections" 
  ON public.payment_projections 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own payment projections" 
  ON public.payment_projections 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own payment projections" 
  ON public.payment_projections 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Enable Row Level Security on payment_audit_log table
ALTER TABLE public.payment_audit_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for payment_audit_log
CREATE POLICY "Users can view their own audit logs" 
  ON public.payment_audit_log 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Note: Audit logs should only be created by triggers, not directly by users
-- So we don't add INSERT/UPDATE/DELETE policies for regular users
