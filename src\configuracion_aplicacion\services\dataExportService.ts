import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export class DataExportService {
  private static instance: DataExportService;

  static getInstance(): DataExportService {
    if (!DataExportService.instance) {
      DataExportService.instance = new DataExportService();
    }
    return DataExportService.instance;
  }

  async deleteAllUserData(): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) {
        throw new Error('Usuario no autenticado');
      }

      const confirm = window.confirm(
        '¿Estás absolutamente seguro de que quieres eliminar TODOS tus datos? Esta acción no se puede deshacer.'
      );

      if (!confirm) return;

      const doubleConfirm = window.confirm(
        'Esta es tu última oportunidad. ¿Realmente quieres eliminar todos tus datos financieros?'
      );

      if (!doubleConfirm) return;

      const userId = user.user.id;

      // Eliminar datos de todas las tablas en orden específico
      await this.deleteFromTable('payment_records', userId);
      await this.deleteFromTable('personal_debt_payments', userId);
      await this.deleteFromTable('reimbursements', userId);
      await this.deleteFromTable('subscriptions', userId);
      await this.deleteFromTable('financial_goals', userId);
      await this.deleteFromTable('personal_debts', userId);
      await this.deleteFromTable('credit_cards', userId);
      await this.deleteFromTable('loans', userId);
      await this.deleteFromTable('expenses', userId);
      await this.deleteFromTable('incomes', userId);

      // Limpiar localStorage
      const keysToRemove = [
        'finanz_profile_settings',
        'finanz_notification_settings',
        'finanz_security_settings',
        'finanz_appearance_settings',
      ];
      
      keysToRemove.forEach(key => localStorage.removeItem(key));

      toast.success('Todos los datos han sido eliminados');
    } catch (error: unknown) {
      const errMsg = error instanceof Error ? error.message : 'Error desconocido';
      console.error('Error deleting all data:', errMsg);
      toast.error('Error al eliminar los datos: ' + errMsg);
    }
  }

  private async deleteFromTable(tableName: string, userId: string): Promise<void> {
    try {
      switch (tableName) {
        case 'payment_records':
          await supabase.from('payment_records').delete().eq('user_id', userId);
          break;
        case 'personal_debt_payments':
          await supabase.from('personal_debt_payments').delete().eq('user_id', userId);
          break;
        case 'reimbursements':
          await supabase.from('reimbursements').delete().eq('user_id', userId);
          break;
        case 'subscriptions':
          await supabase.from('subscriptions').delete().eq('user_id', userId);
          break;
        case 'financial_goals':
          await supabase.from('financial_goals').delete().eq('user_id', userId);
          break;
        case 'personal_debts':
          await supabase.from('personal_debts').delete().eq('user_id', userId);
          break;
        case 'credit_cards':
          await supabase.from('credit_cards').delete().eq('user_id', userId);
          break;
        case 'loans':
          await supabase.from('loans').delete().eq('user_id', userId);
          break;
        case 'expenses':
          await supabase.from('expenses').delete().eq('user_id', userId);
          break;
        case 'incomes':
          await supabase.from('incomes').delete().eq('user_id', userId);
          break;
        default:
          console.warn(`Unknown table: ${tableName}`);
      }
    } catch (error) {
      console.error(`Error deleting from ${tableName}:`, error);
    }
  }
}

export const dataExportService = DataExportService.getInstance();
