
import { logger } from "@/utils/logger";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Income } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { QUERY_KEYS } from '@/constants/queryKeys';

const QUERY_KEY = QUERY_KEYS.INCOMES;

// Define DBIncome interface based on database schema
interface DBIncome {
  id: string;
  month: string;
  currency: "DOP" | "USD";
  fixed_salary: number;
  variable_percentage?: number | null;
  variable_amount?: number | null;
  quarterly_incentive?: number | null;
  performance_percentage?: number | null;
  vehicle_depreciation?: number | null;
  legal_deductions?: number | null;
  payroll_loan?: number | null;
  gross_income: number;
  net_income: number;
  other_income_items?: Array<Record<string, any>> | null;
  variable_scenarios?: Record<string, any> | null;
  user_id: string;
  // created_at?: string; // If needed
  // updated_at?: string; // If needed
}

// Transform database object to TypeScript interface
const transformIncome = (dbIncome: DBIncome): Income => ({
  id: dbIncome.id,
  month: dbIncome.month,
  currency: dbIncome.currency as "DOP" | "USD",
  fixedSalary: dbIncome.fixed_salary,
  variablePercentage: dbIncome.variable_percentage,
  variableAmount: dbIncome.variable_amount,
  quarterlyIncentive: dbIncome.quarterly_incentive,
  performancePercentage: dbIncome.performance_percentage,
  vehicleDepreciation: dbIncome.vehicle_depreciation,
  legalDeductions: dbIncome.legal_deductions,
  payrollLoan: dbIncome.payroll_loan,
  grossIncome: dbIncome.gross_income,
  netIncome: dbIncome.net_income,
  otherIncomeItems: dbIncome.other_income_items || [],
  variableScenarios: dbIncome.variable_scenarios || {}
});

// Transform TypeScript interface to database object
const transformIncomeToDb = (income: Omit<Income, 'id'>, userId: string) => ({
  month: income.month,
  currency: income.currency,
  fixed_salary: income.fixedSalary,
  variable_percentage: income.variablePercentage,
  variable_amount: income.variableAmount,
  quarterly_incentive: income.quarterlyIncentive,
  performance_percentage: income.performancePercentage,
  vehicle_depreciation: income.vehicleDepreciation,
  legal_deductions: income.legalDeductions,
  payroll_loan: income.payrollLoan,
  gross_income: income.grossIncome,
  net_income: income.netIncome,
  other_income_items: income.otherIncomeItems,
  variable_scenarios: income.variableScenarios,
  user_id: userId
});

// Helper function to transform keys of an update object to snake_case
const transformUpdatesToDb = (updates: Partial<Income>): Partial<DBIncome> => {
  const dbUpdates: Partial<DBIncome> = {};
  let key: keyof Income;
  for (key in updates) {
    if (Object.prototype.hasOwnProperty.call(updates, key)) {
      const value = updates[key];
      switch (key) {
        case 'fixedSalary': dbUpdates.fixed_salary = value; break;
        case 'variablePercentage': dbUpdates.variable_percentage = value; break;
        case 'variableAmount': dbUpdates.variable_amount = value; break;
        case 'quarterlyIncentive': dbUpdates.quarterly_incentive = value; break;
        case 'performancePercentage': dbUpdates.performance_percentage = value; break;
        case 'vehicleDepreciation': dbUpdates.vehicle_depreciation = value; break;
        case 'legalDeductions': dbUpdates.legal_deductions = value; break;
        case 'payrollLoan': dbUpdates.payroll_loan = value; break;
        case 'grossIncome': dbUpdates.gross_income = value; break;
        case 'netIncome': dbUpdates.net_income = value; break;
        case 'otherIncomeItems': dbUpdates.other_income_items = value; break;
        case 'variableScenarios': dbUpdates.variable_scenarios = value; break;
        // Properties that are the same in Income and DBIncome (if updatable)
        case 'month': dbUpdates.month = value; break;
        case 'currency': dbUpdates.currency = value; break;
        // 'id' is not part of 'updates' for DB, it's used in .eq('id', id)
      }
    }
  }
  return dbUpdates;
};

export const useSupabaseIncomes = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient();
  const { user, session } = useAuth();

  // Improved authentication check
  const isAuthenticated = !!(user && session && user.id);

  // Fetch incomes query
  const { data: incomes = [], isLoading, error } = useQuery({
    queryKey: [QUERY_KEY, user?.id],
    queryFn: async () => {
      if (!isAuthenticated) {
        logger.debug('User not authenticated, returning empty array');
        return [];
      }
      
      logger.debug('Fetching incomes from Supabase for user:', user.id);
      
      // Verify session is still valid
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (!currentSession) {
        throw new Error('Session expired');
      }

      const { data, error } = await supabase
        .from('incomes')
        .select('*')
        .eq('user_id', user.id)
        .order('month', { ascending: false });
      
      if (error) {
        console.error('Error fetching incomes:', error);
        throw error;
      }
      
      logger.debug('Fetched incomes:', data);
      return data.map(transformIncome);
    },
    enabled: isAuthenticated && enabled,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });

  // Add income mutation
  const addIncomeMutation = useMutation({
    mutationFn: async (income: Omit<Income, 'id'>) => {
      logger.debug('Adding income (original):', income);
      
      if (!isAuthenticated) {
        throw new Error('User not authenticated');
      }

      // Double-check session validity before mutation
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (!currentSession || !currentSession.user) {
        throw new Error('Session expired or invalid');
      }

      const incomeForDb = transformIncomeToDb(income, user.id);
      logger.debug('Adding income (transformed for DB):', incomeForDb);

      const { data, error } = await supabase
        .from('incomes')
        .insert(incomeForDb)
        .select()
        .single();

      if (error) {
        console.error('Error adding income:', error);
        throw error;
      }

      return transformIncome(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY, user?.id] });
    },
    onError: (error, _variables, _context) => { // Mark variables and context as unused
      console.error('addIncomeMutation failed:', error);
    },
  });

  // Update income mutation
  const updateIncomeMutation = useMutation({
    mutationFn: async ({ id, ...updates }: { id: string } & Partial<Income>) => {
      logger.debug('Updating income (original updates):', updates);

      if (!isAuthenticated) {
        throw new Error('User not authenticated');
      }

      // Double-check session validity before mutation
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (!currentSession || !currentSession.user) {
        throw new Error('Session expired or invalid');
      }

      const updatesForDb = transformUpdatesToDb(updates);
      logger.debug('Updating income (transformed updates for DB):', updatesForDb);

      const { data, error } = await supabase
        .from('incomes')
        .update(updatesForDb)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating income:', error);
        throw error;
      }

      return transformIncome(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY, user?.id] });
    },
    onError: (error, _variables, _context) => { // Mark variables and context as unused
      console.error('updateIncomeMutation failed:', error);
    },
  });

  // Delete income mutation
  const deleteIncomeMutation = useMutation({
    mutationFn: async (id: string) => {
      logger.debug('Deleting income:', id);
      
      if (!isAuthenticated) {
        throw new Error('User not authenticated');
      }

      // Double-check session validity before mutation
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (!currentSession || !currentSession.user) {
        throw new Error('Session expired or invalid');
      }

      const { error } = await supabase
        .from('incomes')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting income:', error);
        throw error;
      }

      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY, user?.id] });
    },
    onError: (error, _variables, _context) => { // Mark variables and context as unused
      console.error('deleteIncomeMutation failed:', error);
    },
  });

  return {
    incomes,
    isLoading,
    error,
    addIncome: (newIncome: Omit<Income, 'id'>) => addIncomeMutation.mutateAsync(newIncome),
    updateIncome: (id: string, updates: Partial<Income>) => 
      updateIncomeMutation.mutateAsync({ id, ...updates }),
    deleteIncome: deleteIncomeMutation.mutate,
    isAddingIncome: addIncomeMutation.isPending,
    isUpdatingIncome: updateIncomeMutation.isPending,
    isDeletingIncome: deleteIncomeMutation.isPending,
  };
};
