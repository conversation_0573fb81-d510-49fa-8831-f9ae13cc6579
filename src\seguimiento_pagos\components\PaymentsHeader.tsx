import React from 'react';
import { TrendingUp } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';
import { TemporalNavigator } from './TemporalNavigator';
import { PeriodMetricsCard } from './PeriodMetricsCard';
import type { PeriodMetrics } from '../hooks/useTemporalPayments';
import type { ProjectionAnalysis, ProjectedPayment } from '../hooks/usePaymentProjections';
import type { CurrentPeriod } from '../hooks/useTemporalNavigation';
import type { DateRange } from 'react-day-picker';

// Simplified return type for getAvailableMonths from the hook
interface AvailableMonthInfo {
  year: number;
  month: number;
  displayName: string;
  isCurrentMonth: boolean;
  isPastMonth: boolean;
  isFutureMonth: boolean;
}
interface PaymentsHeaderProps {
  currentPeriod: CurrentPeriod;
  periodMetrics: PeriodMetrics;
  isProjectionPeriod: boolean; // This is likely currentPeriod.isFutureMonth && currentPeriod.displayMode === 'month'
  hasProjections: boolean;
  projectionAnalysis: ProjectionAnalysis;
  projectedPaymentsForPeriod: ProjectedPayment[];

  // Props for TemporalNavigator
  selectedDateRange?: DateRange;
  onDateRangeSelect: (dateRange?: DateRange) => void;
  navigateToPreviousMonth: () => void;
  navigateToNextMonth: () => void;
  navigateToCurrentMonth: () => void;
  navigateToMonth: (year: number, month: number) => void;
  getAvailableMonths: () => AvailableMonthInfo[];
}

export function PaymentsHeader({
  currentPeriod,
  periodMetrics,
  isProjectionPeriod,
  hasProjections,
  projectionAnalysis,
  projectedPaymentsForPeriod,
  selectedDateRange,
  onDateRangeSelect,
  navigateToPreviousMonth,
  navigateToNextMonth,
  navigateToCurrentMonth,
  navigateToMonth,
  getAvailableMonths,
}: PaymentsHeaderProps) {
  // Determine if projections should be shown:
  // Only for future months when in 'month' display mode.
  const showProjections = currentPeriod.displayMode === 'month' && currentPeriod.isFutureMonth && hasProjections;

  return (
    <>
      {/* Navegador temporal */}
      <TemporalNavigator
        currentPeriod={currentPeriod}
        selectedDateRange={selectedDateRange}
        onDateRangeSelect={onDateRangeSelect}
        navigateToPreviousMonth={navigateToPreviousMonth}
        navigateToNextMonth={navigateToNextMonth}
        navigateToCurrentMonth={navigateToCurrentMonth}
        navigateToMonth={navigateToMonth}
        getAvailableMonths={getAvailableMonths}
      />

      {/* Métricas del período */}
      <PeriodMetricsCard
        metrics={periodMetrics}
        periodName={currentPeriod.displayName}
        // isCurrentMonth is now part of currentPeriod, and relevant for month display mode
        isCurrentMonth={currentPeriod.displayMode === 'month' && currentPeriod.isCurrentMonth}
      />

      {/* Proyecciones: show only if in month mode and it's a future month */}
      {showProjections && (
        <Card className="border-finanz-indigo/20 bg-finanz-indigo/5">
          <CardHeader>
            <CardTitle className="text-finanz-indigo flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Proyecciones Inteligentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-finanz-indigo">
                  {projectedPaymentsForPeriod.length}
                </div>
                <div className="text-sm text-finanz-text-secondary">Pagos Proyectados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-finanz-indigo">
                  {projectionAnalysis.confidenceScore}%
                </div>
                <div className="text-sm text-finanz-text-secondary">Confianza</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-finanz-indigo">
                  {formatCurrency(projectedPaymentsForPeriod.reduce((sum, p) => sum + p.amount, 0))}
                </div>
                <div className="text-sm text-finanz-text-secondary">Total Proyectado</div>
              </div>
            </div>

            {projectionAnalysis.recommendations.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Recomendaciones:</h4>
                {projectionAnalysis.recommendations.slice(0, 2).map((rec, index) => (
                  <div key={index} className="text-xs text-finanz-text-secondary bg-white/50 rounded p-2">
                    {rec}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
