import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Filter } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFinanceData } from '@/hooks/useFinanceData';
import { IncomeList } from './IncomeList';
import { IncomeDashboard } from './IncomeDashboard';
import { SmartReplicateButton } from './SmartReplicateButton';
import { DeleteIncomeDialog } from './DeleteIncomeDialog';
import { Income } from '@/types';
import { toast } from 'sonner';
import { formatMonthYear } from '../utils/csvUtils';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface IncomeSectionProps {
  onAddNew: () => void;
  onEdit: (income: Income) => void;
}

// Monedas válidas soportadas por la aplicación
const VALID_CURRENCIES = ["DOP", "USD"] as const;
type ValidCurrency = typeof VALID_CURRENCIES[number];

export function IncomeSection({ onAddNew, onEdit }: IncomeSectionProps) {
  const { incomes, deleteIncome, defaultCurrency } = useFinanceData();
  const { isMobile, isTablet } = useBreakpoint();
  const containerRef = useRef<HTMLDivElement>(null);
  const [filterMonth, setFilterMonth] = useState<string>('all');
  const [filterCurrency, setFilterCurrency] = useState<string>('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [incomeToDelete, setIncomeToDelete] = useState<Income | null>(null);

  // Memoizar cálculo de meses únicos con ordenamiento correcto
  const uniqueMonths = useMemo(() => {
    if (!incomes || incomes.length === 0) return [];
    
    return [...new Set(incomes.map(income => income.month))]
      .sort()
      .reverse(); // Más recientes primero
  }, [incomes]);

  // Memoizar cálculo de monedas únicas con validación
  const uniqueCurrencies = useMemo(() => {
    if (!incomes || incomes.length === 0) return [];

    const currenciesSet = new Set(
      incomes
        .map(income => income.currency)
        .filter((currency): currency is ValidCurrency => 
          VALID_CURRENCIES.includes(currency as ValidCurrency)
        )
    );

    return Array.from(currenciesSet);
  }, [incomes]);

  // Crear string estable para dependencias de useEffect
  const uniqueMonthsString = uniqueMonths.join(',');
  const uniqueCurrenciesString = uniqueCurrencies.join(',');

  // Resetear filtro de mes si el mes seleccionado ya no existe
  useEffect(() => {
    if (uniqueMonths.length > 0 && filterMonth !== 'all' && !uniqueMonths.includes(filterMonth)) {
      setFilterMonth('all');
    }
  }, [uniqueMonths, uniqueMonthsString, filterMonth]);

  // Resetear filtro de moneda si la moneda seleccionada ya no existe
  useEffect(() => {
    if (uniqueCurrencies.length > 0 && filterCurrency !== 'all' && !uniqueCurrencies.includes(filterCurrency as ValidCurrency)) {
      setFilterCurrency('all');
    }
  }, [uniqueCurrencies, uniqueCurrenciesString, filterCurrency]);

  const filteredIncomes = incomes.filter(income => {
    const monthMatch = filterMonth === 'all' || income.month === filterMonth;
    const currencyMatch = filterCurrency === 'all' || income.currency === filterCurrency;
    return monthMatch && currencyMatch;
  });

  const handleDeleteIncome = (incomeId: string) => {
    const income = incomes.find(inc => inc.id === incomeId);
    if (income) {
      setIncomeToDelete(income);
      setDeleteDialogOpen(true);
    }
  };

  const confirmDelete = () => {
    if (incomeToDelete) {
      deleteIncome(incomeToDelete.id);
      setDeleteDialogOpen(false);
      setIncomeToDelete(null);
      toast.success('Ingreso eliminado correctamente');
    }
  };

  const handleReplicationSuccess = (recommendations: string[]) => {
    if (recommendations.length > 0) {
      toast.success('Replicación completada con recomendaciones', {
        description: `${recommendations.length} recomendaciones disponibles`,
        duration: 5000
      });
    }
  };

  return (
    <div className={`
      ${isMobile ? 'space-y-3' : isTablet ? 'space-y-4' : 'space-y-6'}
    `} ref={containerRef}>
      {/* Dashboard */}
      <IncomeDashboard incomes={incomes} defaultCurrency={defaultCurrency as "DOP" | "USD"} />

      {/* Controls */}
      <Card>
        <CardHeader className={isMobile ? 'pb-2' : ''}>
          <div className={`
            flex items-start justify-between
            ${isMobile ? 'flex-col gap-3' : isTablet ? 'flex-col sm:flex-row gap-3' : 'flex-row gap-4'}
          `}>
            <CardTitle className={`
              ${isMobile ? 'text-lg' : 'text-xl'}
            `}>
              Historial de Ingresos
            </CardTitle>
            <div className={`
              flex gap-2
              ${isMobile ? 'flex-col w-full' : 'flex-row flex-wrap'}
            `}>
              <SmartReplicateButton onSuccess={handleReplicationSuccess} />
              <Button 
                onClick={onAddNew} 
                className={`
                  bg-finanz-success hover:bg-finanz-success/90
                  ${isMobile ? 'w-full text-sm py-2' : ''}
                `}
                size={isMobile ? "sm" : "default"}
              >
                <Plus className={`
                  mr-2
                  ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}
                `} />
                {isMobile ? 'Agregar' : 'Agregar Ingreso'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className={isMobile ? 'pt-0' : ''}>
          <div className={`
            flex gap-2 mb-4
            ${isMobile ? 'flex-col' : 'flex-row flex-wrap'}
          `}>
            <div className={`
              flex items-center gap-2
              ${isMobile ? 'mb-2' : ''}
            `}>
              <Filter className={`
                text-finanz-text-secondary
                ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}
              `} />
              <span className={`
                text-finanz-text-secondary
                ${isMobile ? 'text-xs' : 'text-sm'}
              `}>
                Filtros:
              </span>
            </div>
            <div className={`
              flex gap-2
              ${isMobile ? 'flex-col' : 'flex-row'}
            `}>
              <Select value={filterMonth} onValueChange={setFilterMonth}>
                <SelectTrigger className={`
                  ${isMobile ? 'w-full h-8 text-xs' : 'w-40'}
                `}>
                  <SelectValue placeholder="Mes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los meses</SelectItem>
                  {uniqueMonths.map(month => (
                    <SelectItem key={month} value={month}>
                      {formatMonthYear(month)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filterCurrency} onValueChange={setFilterCurrency}>
                <SelectTrigger className={`
                  ${isMobile ? 'w-full h-8 text-xs' : 'w-32'}
                `}>
                  <SelectValue placeholder="Moneda" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  <SelectItem value="DOP">RD$</SelectItem>
                  <SelectItem value="USD">USD$</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <IncomeList 
            incomes={filteredIncomes}
            onEdit={onEdit}
            onDelete={handleDeleteIncome}
          />
        </CardContent>
      </Card>

      {/* Dialog de confirmación de eliminación */}
      <DeleteIncomeDialog
        isOpen={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        incomeMonth={incomeToDelete ? formatMonthYear(incomeToDelete.month) : undefined}
      />
    </div>
  );
}
