
import { DollarSign } from 'lucide-react';
import { FinancialAdviceSection } from '@/asesoria_financiera/components/FinancialAdviceSection';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export default function FinancialAdvicePage() {
  const { isMobile /*, isTablet*/ } = useBreakpoint(); // isTablet removed
  
  return (
    <div className={`space-y-4 md:space-y-6 ${isMobile ? 'p-3' : 'p-6'}`}>
      <div className={`flex items-center justify-between ${isMobile ? 'flex-col space-y-2' : ''}`}>
        <div className={`${isMobile ? 'text-center' : ''}`}>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-3xl'}`}>
            {isMobile ?  'Consejos' : 'Consejos Financieros'}
          </h1>
          <p className={`text-finanz-text-secondary ${isMobile ? 'text-xs' : ''}`}>
            {isMobile ? 'Tips personalizados' : 'Recibe consejos personalizados para mejorar tu salud financiera'}
          </p>
        </div>
        <DollarSign className={`text-finanz-success ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`} />
      </div>

      <FinancialAdviceSection />
    </div>
  );
}
