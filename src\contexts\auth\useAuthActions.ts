import { logger } from "@/utils/logger";
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { rateLimit, auditLog } from '@/utils/securityUtils';
import { sanitizeErrorMessage } from '@/utils/security/contentSecurity';
import { ROUTES } from '@/constants/routes';

// Enhanced error handling with security sanitization
const getSecureErrorMessage = (error: unknown, defaultMessage: string = 'An unexpected error occurred.'): string => {
  const sanitizedMessage = sanitizeErrorMessage(error as Error);
  
  if (sanitizedMessage.toLowerCase().includes('invalid login credentials')) {
    return 'Invalid login credentials. Please check your email and password.';
  }
  if (sanitizedMessage.toLowerCase().includes('user not found') || sanitizedMessage.toLowerCase().includes('no user found')) {
    return 'No user found with that email address.';
  }
  if (sanitizedMessage.toLowerCase().includes('password should be at least 6 characters')) {
    return 'Password should be at least 6 characters.';
  }
  
  return sanitizedMessage || defaultMessage;
};

export const useAuthActions = (
  setLoading: (loading: boolean) => void,
  clearAuthState: () => void,
  updateActivity: () => void
) => {
  const { toast } = useToast();

  const signInWithGoogle = async (forceAccountSelection = false) => {
    try {
      // Local rate limiting using a per-client identifier
      const clientId = 'google_signin';
      if (!rateLimit(clientId, 10, 5 * 60 * 1000)) {
        toast({
          title: 'Too many attempts',
          description: 'Please wait a few minutes before trying again.',
          variant: 'destructive',
        });
        return;
      }

      setLoading(true);
      logger.debug('useAuthActions: Starting Google OAuth...', forceAccountSelection ? 'with account selection' : 'normal flow');
      
      auditLog('google_signin_attempt', { forceAccountSelection });
      
      const currentDomain = window.location.origin;
      
      const oauthOptions: {
        provider: string;
        options: {
          redirectTo: string;
          queryParams: Record<string, string>;
        };
      } = {
        provider: 'google',
        options: {
          redirectTo: `${currentDomain}/app/dashboard`,
          queryParams: {
            prompt: forceAccountSelection ? 'select_account consent' : 'consent',
            scope: 'openid email profile'
          }
        }
      };

      const { error } = await supabase.auth.signInWithOAuth(oauthOptions);

      if (error) {
        console.error('useAuthActions: Google OAuth error:', error);
        auditLog('google_signin_error', { error: getSecureErrorMessage(error) });
        toast({
          title: 'Error al iniciar sesión con Google',
          description: getSecureErrorMessage(error, 'No se pudo iniciar sesión con Google. Inténtalo de nuevo.'),
          variant: 'destructive',
        });
        throw error;
      }
      
      logger.debug('useAuthActions: Google OAuth initiated successfully');
      auditLog('google_signin_success', {});
    } catch (error) {
      console.error('useAuthActions: Error signing in with Google:', error);
      if (!toast || typeof toast !== 'function') { /* Guard for tests or if toast isn't ready */ }
      else if (!(error && error.message && error.message.includes('OAuth error'))) {
        toast({
            title: 'Error al iniciar sesión con Google',
            description: getSecureErrorMessage(error, 'Ocurrió un error inesperado durante el inicio de sesión con Google.'),
            variant: 'destructive',
        });
      }
      setLoading(false);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      logger.debug('useAuthActions: Signing out...');
      
      auditLog('signout_attempt', {});
      
      // Don't clear auth state immediately, let Supabase handle it
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('useAuthActions: Supabase sign out error:', error);
        auditLog('signout_error', { error: getSecureErrorMessage(error) });
        toast({
          title: 'Error al cerrar sesión',
          description: getSecureErrorMessage(error, 'Ocurrió un error al cerrar sesión del servidor. Inténtalo de nuevo.'),
          variant: 'destructive',
        });
      } else {
        auditLog('signout_success', {});
      }

      logger.debug('useAuthActions: Sign out completed');
      
      // Enhanced cleanup - but let auth state handle the actual clearing
      sessionStorage.clear();
      localStorage.removeItem('last_activity');
      
      // Wait a bit before redirecting to allow auth state to update
      setTimeout(() => {
        window.location.href = ROUTES.AUTH;
      }, 500);
      
    } catch (error) {
      console.error('useAuthActions: Error during sign out process:', error);
      auditLog('signout_error', { error: getSecureErrorMessage(error) });
      toast({
        title: 'Error al cerrar sesión',
        description: getSecureErrorMessage(error, 'Ocurrió un error inesperado durante el cierre de sesión.'),
        variant: 'destructive',
      });
      clearAuthState();
      window.location.href = ROUTES.AUTH;
    } finally {
      setLoading(false);
    }
  };

  const unlockScreen = async (password: string, userEmail?: string): Promise<boolean> => {
    try {
      if (!userEmail) {
        console.error('useAuthActions: No email provided for unlock');
        auditLog('unlock_attempt_failed', { reason: 'no_email' });
        return false;
      }

      // Rate limiting for unlock attempts
      if (!rateLimit(`unlock_${userEmail}`, 5, 15 * 60 * 1000)) { // 5 attempts per 15 minutes
        auditLog('unlock_rate_limited', { email: userEmail });
        return false;
      }

      logger.debug('useAuthActions: Attempting to unlock screen for:', userEmail);
      auditLog('unlock_attempt', { email: userEmail });

      const { data, error } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: password,
      });

      if (error) {
        console.error('useAuthActions: Unlock failed:', error);
        auditLog('unlock_failed', { email: userEmail, error: getSecureErrorMessage(error) });
        return false;
      }

      if (data.user) {
        updateActivity();
        auditLog('unlock_success', { email: userEmail });
        logger.debug('useAuthActions: Screen unlocked successfully');
        return true;
      }

      return false;
    } catch (error) {
      console.error('useAuthActions: Error during unlock:', error);
      auditLog('unlock_error', { email: userEmail, error: getSecureErrorMessage(error) });
      return false;
    }
  };

  return {
    signInWithGoogle,
    signOut,
    refreshSession: async () => {
      try {
        logger.debug('useAuthActions: Refreshing session...');
        auditLog('session_refresh_attempt', {});
        
        const { data, error } = await supabase.auth.refreshSession();
        
        if (!error && data.session) {
          updateActivity();
          auditLog('session_refresh_success', {});
          logger.debug('useAuthActions: Session refreshed successfully');
        } else {
          console.error('useAuthActions: Failed to refresh session:', error);
          auditLog('session_refresh_error', { error: getSecureErrorMessage(error) });
          // Don't immediately clear auth state on refresh failure
        }
      } catch (error) {
        console.error('useAuthActions: Error refreshing session:', error);
        auditLog('session_refresh_error', { error: getSecureErrorMessage(error) });
        // Don't immediately clear auth state on refresh failure
      }
    },
    unlockScreen,
  };
};
