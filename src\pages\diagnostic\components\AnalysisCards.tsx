
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BarChart3, Calendar, AlertCircle, TrendingDown } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';

interface AnalysisCardsProps {
  netIncome: number;
  totalMonthlyPayments: number; // Total real de pagos programados del mes
  netBalance: number;
  totalDebt: number;
  savingsRate: number;
  paymentBreakdown?: {
    pending: number;
    overdue: number;
    paid: number;
  };
}

export function AnalysisCards({ 
  netIncome, 
  totalMonthlyPayments, 
  netBalance, 
  // totalDebt, // Unused prop
  // savingsRate, // Unused prop
  paymentBreakdown = { pending: 0, overdue: 0, paid: 0 }
}: AnalysisCardsProps) {
  const totalPayments = paymentBreakdown.pending + paymentBreakdown.overdue + paymentBreakdown.paid;
  const paymentCoverage = netIncome > 0 ? (totalMonthlyPayments / netIncome) * 100 : 0;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card className="bg-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>Análisis: Ingresos vs Pagos Totales del Mes</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Ingresos Netos del Mes</span>
              <span className="font-semibold text-green-500">
                {formatCurrency(netIncome, 'DOP')}
              </span>
            </div>
            <Progress
              value={100}
              className="h-3 bg-gray-100"
              indicatorClassName="bg-green-500"
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-muted-foreground">Total Pagos del Mes</span>
              <span className="font-semibold text-red-500">
                {formatCurrency(totalMonthlyPayments, 'DOP')}
              </span>
            </div>
            <Progress
              value={Math.min(100, paymentCoverage)}
              className="h-3"
              indicatorClassName="bg-finanz-primary"
            />
            <div className="text-xs text-muted-foreground mt-1">
              {paymentCoverage.toFixed(1)}% de los ingresos
            </div>
          </div>
          
          <div className="pt-3 border-t">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Balance Real Disponible</span>
              <span className={`font-bold text-lg ${netBalance >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {formatCurrency(netBalance, 'DOP')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Después de todos los pagos del mes
            </div>
          </div>

          {paymentCoverage > 80 && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <AlertCircle className="w-4 h-4 text-yellow-600" />
              <span className="text-xs text-yellow-700">
                ⚠️ Los pagos del mes representan más del 80% de tus ingresos
              </span>
            </div>
          )}

          {netBalance < 0 && (
            <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
              <TrendingDown className="w-4 h-4 text-red-600" />
              <span className="text-xs text-red-700">
                🚨 Déficit: Tus pagos superan tus ingresos este mes
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>Desglose de Pagos del Mes</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Pagos Pendientes</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-yellow-500/10 text-yellow-600 border-yellow-300">
                  {formatCurrency(paymentBreakdown.pending, 'DOP')}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {totalPayments > 0 ? ((paymentBreakdown.pending / totalPayments) * 100).toFixed(1) : 0}%
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Pagos Vencidos</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-red-500/10 text-red-600 border-red-300">
                  {formatCurrency(paymentBreakdown.overdue, 'DOP')}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {totalPayments > 0 ? ((paymentBreakdown.overdue / totalPayments) * 100).toFixed(1) : 0}%
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Pagos Realizados</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-300">
                  {formatCurrency(paymentBreakdown.paid, 'DOP')}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {totalPayments > 0 ? ((paymentBreakdown.paid / totalPayments) * 100).toFixed(1) : 0}%
                </span>
              </div>
            </div>

            <div className="pt-3 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Programado del Mes</span>
                <span className="font-bold text-blue-600">
                  {formatCurrency(totalPayments, 'DOP')}
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Incluye: Tarjetas, préstamos, deudas personales, suscripciones y gastos recurrentes
              </div>
            </div>
          </div>

          {paymentBreakdown.overdue > 0 && (
            <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <span className="text-xs text-red-700">
                Tienes pagos vencidos que requieren atención inmediata
              </span>
            </div>
          )}

          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-xs text-blue-700">
              <strong>Eficiencia de Pagos:</strong> {((netBalance / netIncome) * 100).toFixed(1)}%
              <br />
              <span className="text-blue-600">
                Porcentaje de ingresos disponibles después de pagos
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
