/* eslint-disable react-refresh/only-export-components */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Filter, Search, X, DollarSign } from 'lucide-react'; // Replaced Calendar with DollarSign or similar
// import { DateRangePicker } from '@/components/ui/date-range-picker'; // Removed DateRangePicker
// import { parseISO, startOfDay, endOfDay, isBefore, isAfter, format } from 'date-fns'; // Removed date-fns imports
import { PaymentItem } from '../types/paymentTypes';
import { Label } from '@/components/ui/label'; // Added Label import

interface PaymentFiltersProps {
  onFiltersChange: (filters: PaymentFilterValues) => void;
  totalPayments: number;
  filteredCount: number;
}

export interface PaymentFilterValues {
  searchTerm: string;
  paymentType: string;
  status: string;
  currency: string;
  amountRange: { min: number; max: number };
  // dateRange: { start: Date | null; end: Date | null }; // Removed dateRange
}

export const PaymentFilters = ({ 
  onFiltersChange, 
  totalPayments, 
  filteredCount 
}: PaymentFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<PaymentFilterValues>({
    searchTerm: '',
    paymentType: 'all',
    status: 'all',
    currency: 'all',
    amountRange: { min: 0, max: 0 },
    // dateRange: { start: null, end: null } // Removed dateRange
  });

  const updateFilters = (newFilters: Partial<PaymentFilterValues>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters: PaymentFilterValues = {
      searchTerm: '',
      paymentType: 'all',
      status: 'all',
      currency: 'all',
      amountRange: { min: 0, max: 0 },
      // dateRange: { start: null, end: null } // Removed dateRange
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = filters.searchTerm ||
    filters.paymentType !== 'all' ||
    filters.status !== 'all' ||
    filters.currency !== 'all' ||
    // filters.dateRange.start !== null || // Removed dateRange check
    // filters.dateRange.end !== null || // Removed dateRange check
    filters.amountRange.min > 0 ||
    filters.amountRange.max > 0;

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filtros Adicionales
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2">
                {filteredCount} de {totalPayments}
              </Badge>
            )}
          </CardTitle>
          <div className="flex gap-2">
            {hasActiveFilters && (
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                <X className="w-4 h-4" />
                Limpiar
              </Button>
            )}
            {/* Button to toggle expanded filters can remain if amountRange is considered an "expanded" filter */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Menos Opciones' : 'Más Opciones'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Búsqueda básica */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-finanz-text-secondary" />
          <Input
            id="search-term"
            placeholder="Buscar por nombre de pago..."
            value={filters.searchTerm}
            onChange={(e) => updateFilters({ searchTerm: e.target.value })}
            className="pl-10"
            autoComplete="off"
          />
        </div>

        {/* Filtros rápidos */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2"> {/* Adjusted to 3 cols as Date button is removed */}
          <div className="space-y-2">
            <Label htmlFor="payment-type-select">Tipo de Pago</Label>
            <Select 
              value={filters.paymentType} 
              onValueChange={(value) => updateFilters({ paymentType: value })}
            >
              <SelectTrigger id="payment-type-select" autoComplete="off">
                <SelectValue placeholder="Tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los tipos</SelectItem>
                <SelectItem value="credit-card">Tarjeta de Crédito</SelectItem>
                <SelectItem value="loan">Préstamo</SelectItem>
                <SelectItem value="subscription">Suscripción</SelectItem>
                <SelectItem value="personal-debt">Deuda Personal</SelectItem>
                <SelectItem value="expense">Gasto</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status-select">Estado</Label>
            <Select 
              value={filters.status} 
              onValueChange={(value) => updateFilters({ status: value })}
            >
              <SelectTrigger id="status-select" autoComplete="off">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="pending">Pendiente</SelectItem>
                <SelectItem value="paid">Pagado</SelectItem>
                <SelectItem value="overdue">Vencido</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="currency-select-filter">Moneda</Label>
            <Select 
              value={filters.currency} 
              onValueChange={(value) => updateFilters({ currency: value })}
            >
              <SelectTrigger id="currency-select-filter" autoComplete="off">
                <SelectValue placeholder="Moneda" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                <SelectItem value="DOP">DOP</SelectItem>
                <SelectItem value="USD">USD</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Removed Date Button - was here:
          <Button variant="outline" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
            <Calendar className="w-4 h-4 mr-1" />
            Fechas
          </Button>
          */}
        </div>

        {/* Filtros expandidos - Now only contains Amount Range */}
        {isExpanded && (
          <div className="space-y-4 pt-4 border-t">
            <div className="grid grid-cols-1 gap-4"> {/* Simplified grid */}
              {/* Rango de montos */}
              <div className="space-y-2">
                <Label htmlFor="amount-min">Rango de Montos</Label> {/* No htmlFor ya que los inputs están anidados */}
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    id="amount-min"
                    type="number"
                    placeholder="Mínimo"
                    value={filters.amountRange.min || ''}
                    onChange={(e) => updateFilters({ 
                      amountRange: { ...filters.amountRange, min: Number(e.target.value) || 0 }
                    })}
                    name="amount_min"
                    autoComplete="off"
                  />
                  <Input
                    id="amount-max"
                    type="number"
                    placeholder="Máximo"
                    value={filters.amountRange.max || ''}
                    onChange={(e) => updateFilters({ 
                      amountRange: { ...filters.amountRange, max: Number(e.target.value) || 0 }
                    })}
                    name="amount_max"
                    autoComplete="off"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Resumen de filtros activos */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-1 pt-2 border-t">
            {filters.searchTerm && (
              <Badge variant="outline">
                Búsqueda: "{filters.searchTerm}"
              </Badge>
            )}
            {filters.paymentType !== 'all' && (
              <Badge variant="outline">
                Tipo: {filters.paymentType}
              </Badge>
            )}
            {filters.status !== 'all' && (
              <Badge variant="outline">
                Estado: {filters.status}
              </Badge>
            )}
              {filters.currency !== 'all' && (
                <Badge variant="outline">
                  Moneda: {filters.currency}
                </Badge>
              )}
              {(filters.amountRange.min > 0 || filters.amountRange.max > 0) && (
                <Badge variant="outline">
                  Monto:
                  {filters.amountRange.min > 0 ? ` ${filters.amountRange.min}` : ''}
                  {filters.amountRange.max > 0 ? ` - ${filters.amountRange.max}` : ''}
                </Badge>
              )}
              {/* Removed DateRange active filter display
              {(filters.dateRange.start || filters.dateRange.end) && (
                <Badge variant="outline">
                  Fechas:
                  {filters.dateRange.start ?
                    ` ${format(filters.dateRange.start, 'dd/MM/yyyy')}` : ''}
                  {filters.dateRange.end ?
                    ` - ${format(filters.dateRange.end, 'dd/MM/yyyy')}` : ''}
                </Badge>
              )}
              */}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Hook personalizado para aplicar filtros
export const usePaymentFilters = (payments: PaymentItem[]) => {
  const [filters, setFilters] = useState<PaymentFilterValues>({
    searchTerm: '',
    paymentType: 'all',
    status: 'all',  
    currency: 'all',
    amountRange: { min: 0, max: 0 },
    // dateRange: { start: null, end: null } // Removed dateRange
  });

  const filteredPayments = React.useMemo(() => {
    // Removed date filtering logic from here as it's handled by useTemporalPayments
    // const startDate =
    //   filters.dateRange.start ? startOfDay(filters.dateRange.start) : null;
    // const endDate =
    //   filters.dateRange.end ? endOfDay(filters.dateRange.end) : null;

    return payments.filter(payment => {
      // Filtro de búsqueda
      if (filters.searchTerm && !payment.name.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }

      // Filtro de tipo
      if (filters.paymentType !== 'all' && payment.type !== filters.paymentType) {
        return false;
      }

      // Filtro de estado
      if (filters.status !== 'all' && payment.status !== filters.status) {
        return false;
      }

      // Filtro de moneda
      if (filters.currency !== 'all' && payment.currency !== filters.currency) {
        return false;
      }

      // Filtro de monto
      if (filters.amountRange.min > 0 && payment.amount < filters.amountRange.min) {
        return false;
      }
      if (filters.amountRange.max > 0 && payment.amount > filters.amountRange.max) {
        return false;
      }

      // Removed date filtering logic from here
      // const dueDate = parseISO(payment.dueDate);
      // if (startDate && isBefore(dueDate, startDate)) {
      //   return false;
      // }
      // if (endDate && isAfter(dueDate, endDate)) {
      //   return false;
      // }

      return true;
    });
  }, [payments, filters]);

  return {
    filteredPayments,
    filters,
    setFilters,
    hasActiveFilters:
      filters.searchTerm ||
      filters.paymentType !== 'all' ||
      filters.status !== 'all' ||
      filters.currency !== 'all' ||
      // filters.dateRange.start !== null || // Removed dateRange check
      // filters.dateRange.end !== null || // Removed dateRange check
      filters.amountRange.min > 0 ||
      filters.amountRange.max > 0
  };
};
