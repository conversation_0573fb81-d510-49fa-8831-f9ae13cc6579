# Dependencies
node_modules/

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Test files
coverage/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx
tests/

# Documentation
README.md
PERFORMANCE_OPTIMIZATION.md

# Scripts (keep optimize-build.cjs for deployment)
# scripts/

# Git
.git/
.gitignore

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
