
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TouchButton } from '@/components/ui/touch-button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Settings, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface DashboardCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DashboardCustomizer: React.FC<DashboardCustomizerProps> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState({
    showMetrics: true,
    showCharts: true,
    showAlerts: true,
    showPayments: true,
    compactMode: false,
    darkMode: false
  });
  const { isMobile } = useBreakpoint();

  const handleSettingChange = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSaveSettings = () => {
    // Guardar configuración en localStorage
    localStorage.setItem('dashboard-settings', JSON.stringify(settings));
    toast.success('Configuración guardada exitosamente');
    onClose();
  };

  const handleResetSettings = () => {
    setSettings({
      showMetrics: true,
      showCharts: true,
      showAlerts: true,
      showPayments: true,
      compactMode: false,
      darkMode: false
    });
    toast.info('Configuración restablecida');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-foreground">
            <Settings className="w-5 h-5 text-foreground" />
            Personalizar Dashboard
          </DialogTitle>
          <DialogDescription>
            Configura la visualización de tu dashboard según tus preferencias
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Secciones Visibles</CardTitle>
              <CardDescription>
                Selecciona qué componentes quieres mostrar en tu dashboard
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-metrics" className="flex items-center gap-2">
                  {settings.showMetrics ? 
                    <Eye className="w-4 h-4 text-foreground" /> : 
                    <EyeOff className="w-4 h-4 text-foreground" />
                  }
                  Métricas Principales
                </Label>
                <Switch
                  id="show-metrics"
                  checked={settings.showMetrics}
                  onCheckedChange={() => handleSettingChange('showMetrics')}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-charts" className="flex items-center gap-2">
                  {settings.showCharts ? 
                    <Eye className="w-4 h-4 text-foreground" /> : 
                    <EyeOff className="w-4 h-4 text-foreground" />
                  }
                  Gráficos y Análisis
                </Label>
                <Switch
                  id="show-charts"
                  checked={settings.showCharts}
                  onCheckedChange={() => handleSettingChange('showCharts')}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-alerts" className="flex items-center gap-2">
                  {settings.showAlerts ? 
                    <Eye className="w-4 h-4 text-foreground" /> : 
                    <EyeOff className="w-4 h-4 text-foreground" />
                  }
                  Alertas e Insights
                </Label>
                <Switch
                  id="show-alerts"
                  checked={settings.showAlerts}
                  onCheckedChange={() => handleSettingChange('showAlerts')}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-payments" className="flex items-center gap-2">
                  {settings.showPayments ? 
                    <Eye className="w-4 h-4 text-foreground" /> : 
                    <EyeOff className="w-4 h-4 text-foreground" />
                  }
                  Vista de Pagos
                </Label>
                <Switch
                  id="show-payments"
                  checked={settings.showPayments}
                  onCheckedChange={() => handleSettingChange('showPayments')}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Opciones de Visualización</CardTitle>
              <CardDescription>
                Personaliza el aspecto general del dashboard
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="compact-mode">
                  Modo Compacto
                </Label>
                <Switch
                  id="compact-mode"
                  checked={settings.compactMode}
                  onCheckedChange={() => handleSettingChange('compactMode')}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="dark-mode">
                  Modo Oscuro
                </Label>
                <Switch
                  id="dark-mode"
                  checked={settings.darkMode}
                  onCheckedChange={() => handleSettingChange('darkMode')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Acciones */}
          <div className="flex items-center justify-between pt-4">
            <TouchButton
              variant="outline"
              onClick={handleResetSettings}
              size={isMobile ? 'sm' : 'default'}
            >
              Restablecer
            </TouchButton>

            <div className="flex gap-2">
              <TouchButton
                variant="outline"
                onClick={onClose}
                size={isMobile ? 'sm' : 'default'}
              >
                Cancelar
              </TouchButton>
              <TouchButton
                onClick={handleSaveSettings}
                size={isMobile ? 'sm' : 'default'}
              >
                Guardar Cambios
              </TouchButton>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
