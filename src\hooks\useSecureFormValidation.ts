
import { useState, useCallback } from 'react';
import { validateFinancialForm } from '@/utils/security/formValidation';
import { generateCSRFToken } from '@/utils/security/formValidation';
import { auditLog } from '@/utils/securityUtils';
import { useAuth } from '@/contexts/AuthContext';
import { z } from 'zod';

export const useSecureFormValidation = <T>(
  schema: z.ZodSchema<T>,
  requireCSRF: boolean = true
) => {
  const { user } = useAuth();
  const [errors, setErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [csrfToken, setCSRFToken] = useState<string>('');

  // Initialize CSRF token
  const initializeCSRF = useCallback(() => {
    if (requireCSRF) {
      const token = generateCSRFToken();
      setCSRFToken(token);
      sessionStorage.setItem('csrf_token', token);
    }
  }, [requireCSRF]);

  // Validate form data with security checks
  const validateForm = useCallback(async (data: unknown): Promise<{ isValid: boolean; data?: T }> => {
    setIsValidating(true);
    setErrors([]);

    try {
      // Enhanced validation with CSRF protection
      const validation = validateFinancialForm(
        schema, 
        data, 
        requireCSRF ? csrfToken : undefined
      );

      if (!validation.isValid) {
        setErrors(validation.errors || ['Error de validación']);
        
        // Log validation failure for security monitoring
        auditLog('form_validation_failed', {
          userId: user?.id,
          errors: validation.errors,
          hasCSRF: !!csrfToken
        });
        
        return { isValid: false };
      }

      // Log successful validation
      auditLog('form_validation_success', {
        userId: user?.id,
        hasCSRF: !!csrfToken
      });

      return { isValid: true, data: validation.data };
    } catch (error) {
      const errorMsg = 'Error durante la validación del formulario';
      setErrors([errorMsg]);
      
      auditLog('form_validation_error', {
        userId: user?.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return { isValid: false };
    } finally {
      setIsValidating(false);
    }
  }, [schema, csrfToken, requireCSRF, user?.id]);

  return {
    errors,
    isValidating,
    csrfToken,
    validateForm,
    initializeCSRF,
    clearErrors: () => setErrors([])
  };
};
