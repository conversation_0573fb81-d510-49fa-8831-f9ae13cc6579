
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  // TrendingDown, // Unused
  // Calendar, // Unused
  // DollarSign, // Unused
  // Target, // Unused
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { useOptimizedCalculations } from '../hooks/useOptimizedCalculations';
import { useDashboardData } from '../hooks/useDashboardData';
// import { formatCurrency } from '@/components/ui/numeric-input'; // Unused

interface AlertItem {
  id: string;
  type: 'critical' | 'warning' | 'info' | 'success';
  title: string;
  description: string;
  action?: string;
  actionUrl?: string;
  priority: number;
}

export const ProactiveAlerts: React.FC = () => {
  const optimizedMetrics = useOptimizedCalculations();
  const dashboardData = useDashboardData();

  const generateAlerts = (): AlertItem[] => {
    const alerts: AlertItem[] = [];

    // Alerta crítica: Ratio deuda-ingreso alto
    if (optimizedMetrics.debtServiceRatio > 40) {
      alerts.push({
        id: 'high-debt-ratio',
        type: 'critical',
        title: 'Ratio Deuda-Ingreso Elevado',
        description: `Tu ratio deuda-ingreso es del ${optimizedMetrics.debtServiceRatio.toFixed(1)}%. Se recomienda mantenerlo por debajo del 30%.`,
        action: 'Revisar Deudas',
        actionUrl: '/app/debts',
        priority: 1
      });
    }

    // Alerta crítica: Liquidez baja
    if (optimizedMetrics.liquidityRatio < 1.2) {
      alerts.push({
        id: 'low-liquidity',
        type: 'critical',
        title: 'Liquidez Insuficiente',
        description: `Tu ratio de liquidez es ${optimizedMetrics.liquidityRatio.toFixed(2)}. Podrías tener dificultades para cubrir tus pagos.`,
        action: 'Revisar Ingresos',
        actionUrl: '/app/income',
        priority: 1
      });
    }

    // Alerta warning: Fondo de emergencia inadecuado
    if (optimizedMetrics.emergencyFundAdequacy === 'poor' || optimizedMetrics.emergencyFundAdequacy === 'fair') {
      alerts.push({
        id: 'emergency-fund',
        type: 'warning',
        title: 'Fondo de Emergencia Insuficiente',
        description: `Tienes ${dashboardData.emergencyFundMonths.toFixed(1)} meses de gastos cubiertos. Se recomiendan al menos 3-6 meses.`,
        action: 'Planificar Ahorro',
        actionUrl: '/app/goals',
        priority: 2
      });
    }

    // Alerta warning: Tendencia negativa en ahorros
    if (optimizedMetrics.savingsComparison.trend === 'down' && Math.abs(optimizedMetrics.savingsComparison.changePercentage) > 10) {
      alerts.push({
        id: 'declining-savings',
        type: 'warning',
        title: 'Reducción en Ahorros',
        description: `Tus ahorros han disminuido ${Math.abs(optimizedMetrics.savingsComparison.changePercentage).toFixed(1)}% respecto al mes anterior.`,
        action: 'Analizar Gastos',
        actionUrl: '/app/expenses',
        priority: 2
      });
    }

    // Alerta info: Oportunidad de mejora
    if (optimizedMetrics.efficiency < 20 && optimizedMetrics.efficiency > 0) {
      alerts.push({
        id: 'efficiency-opportunity',
        type: 'info',
        title: 'Oportunidad de Optimización',
        description: `Tu eficiencia financiera es del ${optimizedMetrics.efficiency.toFixed(1)}%. Hay margen para mejorar tus ahorros.`,
        action: 'Ver Consejos',
        actionUrl: '/app/advice',
        priority: 3
      });
    }

    // Alerta success: Buen rendimiento
    if (optimizedMetrics.financialHealthScore > 75) {
      alerts.push({
        id: 'good-performance',
        type: 'success',
        title: '¡Excelente Salud Financiera!',
        description: `Tu score de salud financiera es ${optimizedMetrics.financialHealthScore.toFixed(0)}/100. ¡Sigue así!`,
        priority: 3
      });
    }

    // Alerta info: Tendencia positiva en ingresos
    if (optimizedMetrics.netIncomeComparison.trend === 'up' && optimizedMetrics.netIncomeComparison.changePercentage > 5) {
      alerts.push({
        id: 'income-growth',
        type: 'success',
        title: 'Crecimiento en Ingresos',
        description: `Tus ingresos han aumentado ${optimizedMetrics.netIncomeComparison.changePercentage.toFixed(1)}% este mes. ¡Considera aumentar tus ahorros!`,
        action: 'Planificar Metas',
        actionUrl: '/app/goals',
        priority: 3
      });
    }

    return alerts.sort((a, b) => a.priority - b.priority);
  };

  const alerts = generateAlerts();

  const getAlertIcon = (type: AlertItem['type']) => {
    switch (type) {
      case 'critical': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info': return <Clock className="w-4 h-4 text-blue-500" />;
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
  };

  const getAlertVariant = (type: AlertItem['type']) => {
    switch (type) {
      case 'critical': return 'destructive';
      case 'warning': return 'default';
      case 'info': return 'default';
      case 'success': return 'default';
    }
  };

  if (alerts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            Estado Financiero
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-gray-600">
              ¡Todo se ve bien! No hay alertas activas en este momento.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            Alertas y Recomendaciones
          </CardTitle>
          <Badge variant="outline">
            {alerts.length} {alerts.length === 1 ? 'alerta' : 'alertas'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {alerts.map((alert) => (
          <Alert key={alert.id} variant={getAlertVariant(alert.type)}>
            <div className="flex items-start gap-3">
              {getAlertIcon(alert.type)}
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm">{alert.title}</h4>
                  <Badge 
                    variant={alert.type === 'critical' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {alert.type === 'critical' ? 'Crítico' : 
                     alert.type === 'warning' ? 'Atención' :
                     alert.type === 'info' ? 'Info' : 'Éxito'}
                  </Badge>
                </div>
                <AlertDescription className="text-xs">
                  {alert.description}
                </AlertDescription>
                {alert.action && (
                  <div className="pt-1">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="h-7 text-xs"
                      onClick={() => {
                        if (alert.actionUrl) {
                          window.location.href = alert.actionUrl;
                        }
                      }}
                    >
                      {alert.action}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Alert>
        ))}
      </CardContent>
    </Card>
  );
};
