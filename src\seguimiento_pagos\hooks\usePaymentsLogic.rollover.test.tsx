import { renderHook } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { usePaymentsLogic } from './usePaymentsLogic'
import { useFinanceData } from '@/hooks/useFinanceData'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { useQueryClient } from '@tanstack/react-query'
import { logger } from '@/utils/logger'

vi.mock('@/utils/logger', () => ({
  logger: { debug: vi.fn(), info: vi.fn(), warn: vi.fn(), error: vi.fn() }
}))
vi.mock('@/hooks/useFinanceData')
vi.mock('@/contexts/AuthContext')
vi.mock('@/hooks/use-toast')
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual<typeof import('@tanstack/react-query')>('@tanstack/react-query')
  return { ...actual, useQueryClient: vi.fn() }
})

describe('usePaymentsLogic - late created expense rollover', () => {
  const MOCK_TODAY = '2024-07-20T10:00:00.000Z'

  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(new Date(MOCK_TODAY))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('adds current month expenses with past due date to carriedOverPayments', () => {
    const expense = {
      id: 'exp1',
      date: '2024-06-15',
      month: '2024-06',
      type: 'Variable' as const,
      categoryId: 'cat',
      categoryName: 'Category',
      amount: 50,
      description: '',
      paymentMethod: 'cash' as const,
      status: 'pending' as const,
      currency: 'USD' as const,
      paymentDate: '2024-06-15',
      isRecurring: false,
      createdAt: '2024-07-10',
      updatedAt: '2024-07-10'
    }

    ;(useFinanceData as any).mockReturnValue({
      creditCards: [],
      loans: [],
      subscriptions: [],
      personalDebts: [],
      personalDebtPayments: [],
      expenses: [expense],
      paymentRecords: [],
      addPaymentRecord: vi.fn(),
      markPaymentAsPaid: vi.fn(),
      updatePaymentRecord: vi.fn(),
      addPersonalDebtPayment: vi.fn(),
      updatePersonalDebt: vi.fn(),
      deletePersonalDebtPayment: vi.fn()
    })

    ;(useAuth as any).mockReturnValue({ user: { id: '1' } })
    ;(useToast as any).mockReturnValue({ toast: vi.fn() })
    ;(useQueryClient as any).mockReturnValue({ invalidateQueries: vi.fn(), refetchQueries: vi.fn() })

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={new QueryClient()}>{children}</QueryClientProvider>
    )

    const { result } = renderHook(() => usePaymentsLogic({ year: 2024, month: 7 }), { wrapper })

    const found = result.current.payments.overdue.find(p => p.referenceId === 'exp1')
    expect(found).toBeDefined()
    expect((found as any).isCarriedOver).toBe(true)
    expect(found!.dueDate).toBe('2024-06-15')
  })
})
