import React from 'react';
import { Dialog, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar, TrendingUp, FileText } from 'lucide-react';
import { FinancialGoal, GoalContribution } from '@/types';
import { formatCurrency } from '@/components/ui/numeric-input';

interface ContributionHistoryProps {
  goal: FinancialGoal;
  contributions: GoalContribution[];
  isOpen: boolean;
  onClose: () => void;
}

export function ContributionHistory({ goal, contributions, isOpen, onClose }: ContributionHistoryProps) {
  const totalContributions = contributions.reduce((sum, contrib) => sum + contrib.amount, 0);
  const averageContribution = contributions.length > 0 ? totalContributions / contributions.length : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-finanz-primary" />
            Historial de Contribuciones - {goal.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-finanz-text-secondary">Total Contribuciones</div>
                <div className="text-lg font-bold text-finanz-primary">
                  {formatCurrency(totalContributions, goal.currency)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-finanz-text-secondary">Número de Aportes</div>
                <div className="text-lg font-bold text-finanz-primary">
                  {contributions.length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-finanz-text-secondary">Promedio por Aporte</div>
                <div className="text-lg font-bold text-finanz-primary">
                  {formatCurrency(averageContribution, goal.currency)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contributions List */}
          <div className="space-y-3">
            <h4 className="font-semibold text-finanz-text-primary">Últimas Contribuciones</h4>
            
            {contributions.length === 0 ? (
              <div className="text-center py-8">
                <TrendingUp className="w-12 h-12 text-finanz-text-secondary mx-auto mb-3" />
                <p className="text-finanz-text-secondary">No hay contribuciones registradas</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {contributions
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .map((contribution) => (
                    <Card key={contribution.id} className="hover:shadow-sm transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2 flex-1">
                            <div className="flex items-center gap-3">
                              <Badge variant="outline" className="bg-finanz-success/10 text-finanz-success border-finanz-success/20">
                                +{formatCurrency(contribution.amount, goal.currency)}
                              </Badge>
                              <div className="flex items-center gap-1 text-sm text-finanz-text-secondary">
                                <Calendar className="w-4 h-4" />
                                {new Date(contribution.date).toLocaleDateString('es-ES', {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric'
                                })}
                              </div>
                            </div>
                            {contribution.note && (
                              <div className="flex items-start gap-2 text-sm text-finanz-text-secondary">
                                <FileText className="w-4 h-4 mt-0.5 flex-shrink-0" />
                                <span>{contribution.note}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
