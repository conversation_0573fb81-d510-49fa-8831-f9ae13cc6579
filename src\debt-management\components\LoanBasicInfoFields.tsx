
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { loanTypes } from '../constants/loanTypes';

interface LoanBasicInfoFieldsProps {
  type: string;
  name: string;
  currency: 'DOP' | 'USD';
  onTypeChange: (value: string) => void;
  onNameChange: (value: string) => void;
  onCurrencyChange: (value: 'DOP' | 'USD') => void;
}

export function LoanBasicInfoFields({
  type,
  name,
  currency,
  onTypeChange,
  onNameChange,
  onCurrencyChange
}: LoanBasicInfoFieldsProps) {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="loan-type-select">Tipo de Préstamo *</Label>
        <Select value={type} onValueChange={onTypeChange}>
          <SelectTrigger id="loan-type-select" autoComplete="off">
            <SelectValue placeholder="Selecciona el tipo de préstamo" />
          </SelectTrigger>
          <SelectContent>
            {loanTypes.map((loanType) => (
              <SelectItem key={loanType.value} value={loanType.value}>
                {loanType.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">Nombre del Préstamo *</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          placeholder="Ej: Préstamo Personal"
          required
          autoComplete="off"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="loan-currency-select">Moneda</Label>
        <Select value={currency} onValueChange={onCurrencyChange}>
          <SelectTrigger id="loan-currency-select" autoComplete="off">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="DOP">Pesos (DOP)</SelectItem>
            <SelectItem value="USD">Dólares (USD)</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </>
  );
}
