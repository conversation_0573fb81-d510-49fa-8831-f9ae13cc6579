
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Target } from 'lucide-react';

interface OverallScoreCardProps {
  overallScore: number;
}

export function OverallScoreCard({ overallScore }: OverallScoreCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 70) return 'text-green-500';
    if (score >= 50) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getScoreDescription = (score: number) => {
    if (score >= 80) return 'Excelente';
    if (score >= 70) return 'Muy Buena';
    if (score >= 60) return 'Buena';
    if (score >= 50) return 'Regular';
    if (score >= 40) return 'Mejorable';
    return 'Crítica';
  };

  const getScoreBarColor = (score: number) => {
    if (score >= 70) return 'bg-green-500';
    if (score >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <Card className="border-2 border-blue-500/20">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="w-5 h-5" />
            <span>Puntuación General de Salud Financiera</span>
          </div>
          <Badge className={`${getScoreColor(overallScore)} bg-opacity-10`}>
            {getScoreDescription(overallScore)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-8">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-3">
              <span className="text-lg font-medium text-muted-foreground">Tu puntuación</span>
              <span className={`text-4xl font-bold ${getScoreColor(overallScore)}`}>
                {Math.round(overallScore)}%
              </span>
            </div>
            <Progress
              value={overallScore}
              className="h-4 mb-3"
              indicatorClassName={getScoreBarColor(overallScore)}
            />
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-xs text-muted-foreground">Crítica</div>
                <div className="text-red-500 font-medium">0-39%</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-muted-foreground">Regular</div>
                <div className="text-yellow-500 font-medium">40-69%</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-muted-foreground">Excelente</div>
                <div className="text-green-500 font-medium">70-100%</div>
              </div>
            </div>
          </div>
          <div className="text-center">
            <div className={`text-6xl font-bold ${getScoreColor(overallScore)}`}>
              {Math.round(overallScore)}
            </div>
            <div className="text-sm text-muted-foreground">de 100 puntos</div>
            <div className="text-xs text-muted-foreground mt-1">
              Actualizado: {new Date().toLocaleDateString()}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
