
import { useMemo } from 'react';
import { usePaymentsLogic } from './usePaymentsLogic';
import { TemporalPeriod } from './useTemporalNavigation';
import { PaymentItem, CategorizedPayments, PaymentTotals } from '../types/paymentTypes';
import { useExchangeRate } from '@/hooks/useExchangeRate';
import { logger } from '@/utils/logger';
import { stripTime, endOfMonth } from '../utils/temporalUtils';

interface PeriodMetrics {
  totalPayments: number;
  completedPayments: number;
  pendingPayments: number;
  overduePayments: number;
  completionRate: number;
  totalAmount: number;
  isProjection: boolean;
}

export const useTemporalPayments = (currentPeriod: TemporalPeriod) => {
  // Pass currentPeriod to usePaymentsLogic
  const { payments: allPaymentsForPeriod, totals: baseTotals } = usePaymentsLogic(currentPeriod);
  const { rate: exchangeRate } = useExchangeRate();

  logger.debug("useTemporalPayments: Received currentPeriod:", currentPeriod);
  logger.debug("useTemporalPayments: Payments from usePaymentsLogic for period:", allPaymentsForPeriod);

  // Payments from usePaymentsLogic are now already generated for the currentPeriod.
  // The filtering logic here becomes simpler or might primarily serve to ensure
  // strict adherence if generateAllPayments was broader than the exact period.
  // However, with PaymentGenerationService now strictly using currentPeriod,
  // allPaymentsForPeriod should ideally *only* contain payments for that period.
  // The categorization (pending, paid, overdue) is still useful.

  // For clarity, let's rename allPaymentsForPeriod to reflect it's already period-specific from generation.
  // The categorization service (inside usePaymentsLogic) will handle pending/paid/overdue.
  // So, `allPaymentsForPeriod` is already categorized.

  // The filtering logic below might be redundant if PaymentGenerationService is perfect.
  // However, keeping it provides a safeguard and handles cases where `generateAllPayments`
  // might still return items slightly outside the strict start/end of month if not perfectly aligned.
  // Given PaymentGenerationService was updated to be strict, this filter might not catch much.
  const filteredPayments = useMemo((): CategorizedPayments => {
    const periodStart = new Date(currentPeriod.startDate + "T00:00:00").getTime();
    // Para los rangos personalizados se acota hasta el final del mes de la fecha inicial,
    // conforme a la lógica de negocio validada en los tests.
    const periodEnd = currentPeriod.displayMode === 'range'
      ? endOfMonth(currentPeriod.year, currentPeriod.month - 1).getTime()
      : endOfMonth(currentPeriod.year, currentPeriod.month - 1).getTime();

    // CRITICAL FIX: Determine if the period is really the current month of the system
    // This ensures carried-over payments only appear in the actual current month
    const now = new Date();
    const actualCurrentYear = now.getFullYear();
    const actualCurrentMonth = now.getMonth() + 1;
    
    const isRealCurrentMonth =
      currentPeriod.year === actualCurrentYear &&
      currentPeriod.month === actualCurrentMonth;

    const filterPaymentList = (payments: PaymentItem[], includePastOverdues = false): PaymentItem[] => {
      return payments.filter(payment => {
        const paymentTime = stripTime(payment.dueDate).getTime();
        const inCurrentWindow = paymentTime >= periodStart && paymentTime <= periodEnd;
        
        // ENHANCED: Only include past overdues if viewing actual current month AND payment is marked as carried over
        const shouldIncludeOverdue = includePastOverdues && 
                                   isRealCurrentMonth && 
                                   paymentTime < periodStart && 
                                   (payment.status === 'pending' || payment.status === 'overdue') &&
                                   (payment as any).isCarriedOver === true; // Must be explicitly marked as carried over

        const createdAtStr = (payment as any).createdAt;
        if (createdAtStr) {
          const createdAtTime = stripTime(createdAtStr).getTime();
          if (createdAtTime > periodEnd) {
            logger.debug(`TEMPORAL FILTER: Payment ${payment.name} (ID: ${payment.id}) creado el ${createdAtStr} posterior al final del periodo. Se excluye.`);
            return false;
          }
        }

        const keep = inCurrentWindow || shouldIncludeOverdue;
        
        if (!keep) {
          logger.debug(`TEMPORAL FILTER: Payment ${payment.name} (ID: ${payment.id}, Due: ${payment.dueDate}) filtered out for period ${currentPeriod.startDate} - ${currentPeriod.endDate}. InCurrentWindow: ${inCurrentWindow}, ShouldIncludeOverdue: ${shouldIncludeOverdue}`);
        }
        
        // CRITICAL VALIDATION: Ensure no temporal inconsistencies pass through
        if (keep) {
          // For carried-over payments, ensure they're only shown in actual current month
          if ((payment as any).isCarriedOver && !isRealCurrentMonth) {
            logger.warn(`TEMPORAL INCONSISTENCY: Carried-over payment ${payment.name} detected in non-current month viewing. Filtering out.`);
            return false;
          }
          
          // Additional safeguard: Validate payment dates make sense for the viewing period
          const paymentDate = new Date(payment.dueDate + "T00:00:00");
          const viewingPeriodEnd = new Date(currentPeriod.endDate + "T23:59:59");
          
          // If this is a future period and the payment is marked as overdue, that's impossible
          if (currentPeriod.isFutureMonth && payment.status === 'overdue' && !shouldIncludeOverdue) {
            logger.warn(`TEMPORAL INCONSISTENCY: Overdue payment ${payment.name} found in future period. Filtering out.`);
            return false;
          }
        }
        
        return keep;
      });
    };

    // Enhanced logging for debugging temporal issues
    logger.debug(`TEMPORAL FILTERING: Processing period ${currentPeriod.year}-${currentPeriod.month}. IsRealCurrentMonth: ${isRealCurrentMonth}`);
    logger.debug(`TEMPORAL FILTERING: Input payments - Pending: ${allPaymentsForPeriod.pending.length}, Paid: ${allPaymentsForPeriod.paid.length}, Overdue: ${allPaymentsForPeriod.overdue.length}`);

    // Only include carry-over logic if viewing the actual current month
    const includeCarryOver = isRealCurrentMonth;

    const result = {
      pending: filterPaymentList(allPaymentsForPeriod.pending, includeCarryOver),
      paid: filterPaymentList(allPaymentsForPeriod.paid),
      overdue: filterPaymentList(allPaymentsForPeriod.overdue, includeCarryOver)
    };

    logger.debug(`TEMPORAL FILTERING: Result payments - Pending: ${result.pending.length}, Paid: ${result.paid.length}, Overdue: ${result.overdue.length}`);
    
    return result;
  }, [allPaymentsForPeriod, currentPeriod]);

  logger.debug("useTemporalPayments: Payments after strict filtering:", filteredPayments);


  // Calcular totales específicos del período
  const periodTotals = useMemo((): PaymentTotals => {
    const calculateTotal = (payments: PaymentItem[]) => {
      return payments.reduce((total, payment) => {
        const amount = payment.currency === 'USD' 
          ? payment.amount * exchangeRate 
          : payment.amount;
        return total + amount;
      }, 0);
    };

    return {
      pending: calculateTotal(filteredPayments.pending),
      overdue: calculateTotal(filteredPayments.overdue),
      paidThisMonth: calculateTotal(filteredPayments.paid)
    };
  }, [filteredPayments, exchangeRate]);

  // Calcular métricas del período
  const periodMetrics = useMemo((): PeriodMetrics => {
    const totalPayments = filteredPayments.pending.length + 
                         filteredPayments.paid.length + 
                         filteredPayments.overdue.length;
    
    const completedPayments = filteredPayments.paid.length;
    const completionRate = totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0;
    const totalAmount = periodTotals.pending + periodTotals.overdue + periodTotals.paidThisMonth;

    return {
      totalPayments,
      completedPayments,
      pendingPayments: filteredPayments.pending.length,
      overduePayments: filteredPayments.overdue.length,
      completionRate,
      totalAmount,
      isProjection: currentPeriod.isFutureMonth
    };
  }, [filteredPayments, periodTotals, currentPeriod.isFutureMonth]);

  return {
    payments: filteredPayments,
    totals: periodTotals,
    periodMetrics
  };
};
