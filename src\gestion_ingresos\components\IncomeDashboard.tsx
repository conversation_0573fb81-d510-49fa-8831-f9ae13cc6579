
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { TrendingUp, DollarSign, Calculator, Target } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Income } from '@/types';

interface IncomeDashboardProps {
  incomes: Income[];
  defaultCurrency: 'DOP' | 'USD';
}

export function IncomeDashboard({ incomes, defaultCurrency }: IncomeDashboardProps) {
  const calculateTotals = () => {
    if (incomes.length === 0) {
      return {
        totalIncomes: 0,
        averageIncome: 0,
        lastMonthIncome: 0,
        growthRate: 0
      };
    }

    // Convert all incomes to default currency for calculations
    const convertedIncomes = incomes.map(income => {
      const rate = defaultCurrency === 'USD' ? 59 : 1/59;
      const multiplier = income.currency === defaultCurrency ? 1 : rate;
      
      return {
        ...income,
        convertedNet: income.netIncome * multiplier,
        convertedGross: income.grossIncome * multiplier
      };
    });

    const totalIncomes = convertedIncomes.reduce((sum, income) => sum + income.convertedNet, 0);
    const averageIncome = totalIncomes / convertedIncomes.length;
    
    // Sort by month to get last month's income
    const sortedIncomes = convertedIncomes.sort((a, b) => new Date(b.month).getTime() - new Date(a.month).getTime());
    const lastMonthIncome = sortedIncomes[0]?.convertedNet || 0;
    const previousMonthIncome = sortedIncomes[1]?.convertedNet || 0;
    
    const growthRate = previousMonthIncome > 0 ? 
      ((lastMonthIncome - previousMonthIncome) / previousMonthIncome) * 100 : 0;

    return {
      totalIncomes,
      averageIncome,
      lastMonthIncome,
      growthRate
    };
  };

  const { totalIncomes, averageIncome, lastMonthIncome, growthRate } = calculateTotals();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Acumulado</CardTitle>
          <DollarSign className="h-4 w-4 text-finanz-success" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-finanz-success">
            {formatCurrency(totalIncomes, defaultCurrency)}
          </div>
          <p className="text-xs text-finanz-text-secondary">
            Suma de todos los ingresos netos
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Promedio Mensual</CardTitle>
          <Calculator className="h-4 w-4 text-finanz-primary" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-finanz-primary">
            {formatCurrency(averageIncome, defaultCurrency)}
          </div>
          <p className="text-xs text-finanz-text-secondary">
            Ingreso neto promedio
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Último Mes</CardTitle>
          <Target className="h-4 w-4 text-finanz-warning" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-finanz-warning">
            {formatCurrency(lastMonthIncome, defaultCurrency)}
          </div>
          <p className="text-xs text-finanz-text-secondary">
            Ingreso del mes más reciente
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Crecimiento</CardTitle>
          <TrendingUp className={`h-4 w-4 ${growthRate >= 0 ? 'text-finanz-success' : 'text-finanz-danger'}`} />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${growthRate >= 0 ? 'text-finanz-success' : 'text-finanz-danger'}`}>
            {growthRate > 0 ? '+' : ''}{growthRate.toFixed(1)}%
          </div>
          <p className="text-xs text-finanz-text-secondary">
            vs. mes anterior
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
