
import { useEffect, useRef } from 'react';
import {
  preloadIndexPage,
  preloadAuthPage,
  preloadDashboardPage,
  preloadExpensesPage,
  preloadIncomePage,
  preloadLoansPage,
  preloadGoalsPage,
  preloadConfigurationPage,
  preloadSubscriptionsPage,
  preloadReimbursementsPage,
  preloadPaymentsPage,
  preloadFinancialAdvicePage,
  preloadDiagnosticPage,
} from '@/utils/pagePreloader';

export const usePagePreload = () => {
  const hasPreloaded = useRef(false);

  useEffect(() => {
    if (hasPreloaded.current) return;
    hasPreloaded.current = true;

    preloadIndexPage();
    preloadAuthPage();
    preloadDashboardPage();
    preloadExpensesPage();
    preloadIncomePage();
    preloadLoansPage();
    preloadGoalsPage();
    preloadConfigurationPage();
    preloadSubscriptionsPage();
    preloadReimbursementsPage();
    preloadPaymentsPage();
    preloadFinancialAdvicePage();
    preloadDiagnosticPage();
  }, []);
};
