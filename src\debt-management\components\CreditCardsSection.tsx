
import React, { useState, useEffect } from 'react';
import { Plus, CreditCard as CreditCardIcon, Calendar, AlertTriangle, DollarSign, History } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { useFinanceData } from '@/hooks/useFinanceData';
import { CreditCardForm } from './CreditCardForm';
import { formatCurrency } from '@/components/ui/numeric-input';
import { CreditCard } from '@/types';
import { ApplyPaymentModal, PaymentFormData } from './ApplyPaymentModal';
import { PaymentHistoryModal } from './PaymentHistoryModal';

export function CreditCardsSection() {
  const {
    creditCards,
    addCreditCard,
    updateCreditCard,
    deleteCreditCard,
    isAddingCreditCard,
    isUpdatingCreditCard,
    isLoading: isLoadingDebts, // Aliasing for clarity
    applyCreditCardPayment,
    isApplyingCreditCardPayment,
    paymentRecords,
  } = useFinanceData(['debts', 'payments']);

  const [showForm, setShowForm] = useState(false);
  const [editingCard, setEditingCard] = useState<CreditCard | null>(null);
  const [payingCard, setPayingCard] = useState<CreditCard | null>(null);
  const [historyCard, setHistoryCard] = useState<CreditCard | null>(null);
  const [submissionStatus, setSubmissionStatus] = useState<{ mode: 'add' | 'edit' | 'pay' | 'history' | null, success: boolean }>({ mode: null, success: false });
  const { toast } = useToast();

  const handleAddCard = async (cardData: Omit<CreditCard, 'id' | 'userId'>) => {
    try {
      await addCreditCard(cardData);
      toast({ title: "Éxito", description: "Tarjeta de crédito agregada." });
      setSubmissionStatus({ mode: 'add', success: true });
    } catch (error) {
      console.error("Error adding credit card:", error);
      toast({ title: "Error", description: "No se pudo agregar la tarjeta.", variant: "destructive" });
    }
  };

  const handleUpdateCard = async (id: string, updates: Omit<CreditCard, 'id' | 'userId'>) => {
    try {
      await updateCreditCard(id, updates);
      toast({ title: "Éxito", description: "Tarjeta de crédito actualizada." });
      setSubmissionStatus({ mode: 'edit', success: true });
    } catch (error) {
      console.error("Error updating credit card:", error);
      toast({ title: "Error", description: "No se pudo actualizar la tarjeta.", variant: "destructive" });
    }
  };

  const handleApplyPayment = async (formData: PaymentFormData) => {
    if (!payingCard) return;
    try {
      await applyCreditCardPayment({
        cardId: payingCard.id,
        amount: formData.amount,
        paidDate: formData.paidDate,
        notes: formData.notes,
        currency: payingCard.currency,
        dueDate: payingCard.paymentDueDate,
      });
      toast({ title: "Éxito", description: "Pago aplicado correctamente." });
      setSubmissionStatus({ mode: 'pay', success: true });
    } catch (error) {
      console.error("Error applying payment:", error);
      toast({ title: "Error", description: "No se pudo aplicar el pago.", variant: "destructive" });
    }
  };

  const handleDeleteCard = (id: string) => {
    deleteCreditCard(id);
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge className="bg-green-100 text-green-800">Activa</Badge>
    ) : (
      <Badge variant="secondary">Inactiva</Badge>
    );
  };

  const getDaysUntilDate = (dateString: string) => {
    const today = new Date();
    const targetDate = new Date(dateString);
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDateStatus = (days: number) => {
    if (days < 0) return { text: 'Vencida', color: 'text-red-600' };
    if (days === 0) return { text: 'Hoy', color: 'text-orange-600' };
    if (days <= 3) return { text: `${days} día(s)`, color: 'text-orange-600' };
    return { text: `${days} día(s)`, color: 'text-gray-600' };
  };

  useEffect(() => {
    if (submissionStatus.success && !isLoadingDebts && !isApplyingCreditCardPayment) {
      if (submissionStatus.mode === 'add') {
        setShowForm(false);
      }
      if (submissionStatus.mode === 'pay') {
        setPayingCard(null);
      }
      setEditingCard(null);
      setSubmissionStatus({ mode: null, success: false });
    }
  }, [submissionStatus, isLoadingDebts, isApplyingCreditCardPayment]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Tarjetas de Crédito</h2>
        <Button onClick={() => { setEditingCard(null); setShowForm(true); setPayingCard(null); setHistoryCard(null); }} className="gap-2">
          <Plus className="w-4 h-4" />
          Agregar Tarjeta
        </Button>
      </div>

      {showForm && !editingCard && ( // Ensure editingCard is null for the "add" form to show
        <CreditCardForm
          onSubmit={handleAddCard}
          onCancel={() => {
            setShowForm(false);
            // setEditingCard(null); // Not needed here as editingCard should be null
          }}
          editingCreditCard={null} // Explicitly pass null
          isSubmitting={isAddingCreditCard}
        />
      )}

      {editingCard && ( // This form is for editing
        <CreditCardForm
          onSubmit={async (cardData) => {
            // The type of cardData from CreditCardForm is Omit<CreditCard, 'id' | 'userId'>
            // handleUpdateCard expects Omit<CreditCard, 'id' | 'userId'> for its 'updates'
            await handleUpdateCard(editingCard.id, cardData);
          }}
          onCancel={() => setEditingCard(null)}
          editingCreditCard={editingCard}
          isSubmitting={isUpdatingCreditCard}
        />
      )}

      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        {creditCards.length === 0 ? (
          <div className="text-center py-8 col-span-full">
            <CreditCardIcon className="w-12 h-12 text-finanz-text-secondary mx-auto mb-3" />
            <p className="text-finanz-text-secondary">No hay tarjetas de crédito registradas</p>
          </div>
        ) : (
          creditCards.map((card) => {
            const cutoffDays = getDaysUntilDate(card.cutoffDate);
            const dueDays = getDaysUntilDate(card.paymentDueDate);
            const cutoffStatus = getDateStatus(cutoffDays);
            const dueStatus = getDateStatus(dueDays);
            
            return (
              <Card key={card.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="font-medium text-finanz-text-primary">{card.name}</h4>
                      <p className="text-sm text-finanz-text-secondary">
                        Límite: {formatCurrency(card.creditLimit, card.currency)}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold text-finanz-danger">
                          {formatCurrency(card.currentBalance, card.currency)}
                        </span>
                        {getStatusBadge(card.isActive)}
                      </div>
                      <p className="text-xs text-finanz-text-secondary">
                        Pago mínimo: {formatCurrency(card.minimumPayment, card.currency)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">Fecha de Corte</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{new Date(card.cutoffDate).toLocaleDateString()}</p>
                        <p className={`text-xs ${cutoffStatus.color}`}>
                          {cutoffDays === 0 ? 'Hoy' : cutoffDays > 0 ? `En ${cutoffStatus.text}` : cutoffStatus.text}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-2 bg-orange-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4 text-orange-600" />
                        <span className="text-sm font-medium text-orange-900">Límite de Pago</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{new Date(card.paymentDueDate).toLocaleDateString()}</p>
                        <p className={`text-xs ${dueStatus.color}`}>
                          {dueDays === 0 ? 'Vence hoy' : dueDays > 0 ? `Vence en ${dueStatus.text}` : dueStatus.text}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-900">Pago Programado</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{new Date(card.paymentDate).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingCard(card);
                        setShowForm(false);
                        setPayingCard(null);
                        setHistoryCard(null);
                      }}
                    >
                      Editar
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-green-600 text-green-600 hover:bg-green-50 hover:text-green-700"
                      onClick={() => {
                        setPayingCard(card);
                        setEditingCard(null);
                        setShowForm(false);
                        setHistoryCard(null);
                      }}
                    >
                      <DollarSign className="mr-2 h-4 w-4" />
                      Aplicar Pago
                    </Button>
                     <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setHistoryCard(card);
                        setEditingCard(null);
                        setShowForm(false);
                        setPayingCard(null);
                      }}
                    >
                      <History className="mr-2 h-4 w-4" />
                      Historial
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteCard(card.id)}
                    >
                      Eliminar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
      <ApplyPaymentModal
        isOpen={!!payingCard}
        onClose={() => setPayingCard(null)}
        onSubmit={handleApplyPayment}
        card={payingCard}
        isSubmitting={isApplyingCreditCardPayment}
      />
      <PaymentHistoryModal
        isOpen={!!historyCard}
        onClose={() => setHistoryCard(null)}
        cardName={historyCard?.name || ''}
        payments={paymentRecords.filter(p => p.referenceId === historyCard?.id && (p.paymentType as any) === 'credit_card')}
      />
    </div>
  );
}
