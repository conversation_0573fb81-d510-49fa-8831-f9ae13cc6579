
import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface BiometricCredential {
  id: string;
  type: string;
  name: string;
  createdAt: Date;
}

export const useBiometricAuth = () => {
  const [isSupported, setIsSupported] = useState(
    typeof window !== 'undefined' && 
    'credentials' in navigator && 
    'create' in navigator.credentials
  );
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState<BiometricCredential[]>([]);

  const checkSupport = useCallback(() => {
    const supported = typeof window !== 'undefined' && 
      'credentials' in navigator && 
      'create' in navigator.credentials;
    
    setIsSupported(supported);
    return supported;
  }, []);

  const registerBiometric = useCallback(async (username: string): Promise<boolean> => {
    if (!checkSupport()) {
      toast.error('Autenticación biométrica no disponible en este dispositivo');
      return false;
    }

    setIsLoading(true);
    
    try {
      const challenge = new Uint8Array(32);
      crypto.getRandomValues(challenge);

      const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
        challenge,
        rp: {
          name: 'FinanzApp',
          id: window.location.hostname,
        },
        user: {
          id: new TextEncoder().encode(username),
          name: username,
          displayName: username,
        },
        pubKeyCredParams: [
          { alg: -7, type: 'public-key' }, // ES256
          { alg: -257, type: 'public-key' } // RS256
        ],
        authenticatorSelection: {
          authenticatorAttachment: 'platform',
          userVerification: 'required',
          requireResidentKey: false,
        },
        timeout: 60000,
        attestation: 'direct'
      };

      const credential = await navigator.credentials.create({
        publicKey: publicKeyCredentialCreationOptions
      }) as PublicKeyCredential;

      if (credential) {
        const newCredential: BiometricCredential = {
          id: credential.id,
          type: 'biometric',
          name: `${navigator.platform} - ${new Date().toLocaleDateString()}`,
          createdAt: new Date()
        };

        // Guardar credencial localmente (en producción se guardaría en el servidor)
        const savedCredentials = JSON.parse(
          localStorage.getItem('finanz_biometric_credentials') || '[]'
        );
        savedCredentials.push(newCredential);
        localStorage.setItem(
          'finanz_biometric_credentials', 
          JSON.stringify(savedCredentials)
        );

        setCredentials(savedCredentials);
        toast.success('Autenticación biométrica configurada exitosamente');
        return true;
      }

      return false;
    } catch (error: any) {
      console.error('Error registering biometric:', error);
      
      if (error.name === 'NotSupportedError') {
        toast.error('Autenticación biométrica no soportada');
      } else if (error.name === 'NotAllowedError') {
        toast.error('Acceso a autenticación biométrica denegado');
      } else if (error.name === 'SecurityError') {
        toast.error('Error de seguridad en autenticación biométrica');
      } else {
        toast.error('Error al configurar autenticación biométrica');
      }
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [checkSupport]);

  const authenticateWithBiometric = useCallback(async (): Promise<boolean> => {
    if (!checkSupport()) {
      toast.error('Autenticación biométrica no disponible');
      return false;
    }

    setIsLoading(true);

    try {
      const savedCredentials = JSON.parse(
        localStorage.getItem('finanz_biometric_credentials') || '[]'
      );

      if (savedCredentials.length === 0) {
        toast.error('No hay credenciales biométricas registradas');
        return false;
      }

      const challenge = new Uint8Array(32);
      crypto.getRandomValues(challenge);

      const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions = {
        challenge,
        allowCredentials: savedCredentials.map((cred: BiometricCredential) => ({
          id: new TextEncoder().encode(cred.id),
          type: 'public-key'
        })),
        timeout: 60000,
        userVerification: 'required'
      };

      const assertion = await navigator.credentials.get({
        publicKey: publicKeyCredentialRequestOptions
      });

      if (assertion) {
        toast.success('Autenticación biométrica exitosa');
        return true;
      }

      return false;
    } catch (error: any) {
      console.error('Error authenticating with biometric:', error);
      
      if (error.name === 'NotAllowedError') {
        toast.error('Autenticación biométrica cancelada');
      } else {
        toast.error('Error en autenticación biométrica');
      }
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [checkSupport]);

  const removeBiometricCredential = useCallback((credentialId: string) => {
    const savedCredentials = JSON.parse(
      localStorage.getItem('finanz_biometric_credentials') || '[]'
    );
    
    const filtered = savedCredentials.filter(
      (cred: BiometricCredential) => cred.id !== credentialId
    );
    
    localStorage.setItem(
      'finanz_biometric_credentials', 
      JSON.stringify(filtered)
    );
    
    setCredentials(filtered);
    toast.success('Credencial biométrica eliminada');
  }, []);

  const loadCredentials = useCallback(() => {
    const saved = JSON.parse(
      localStorage.getItem('finanz_biometric_credentials') || '[]'
    );
    setCredentials(saved);
  }, []);

  return {
    isSupported,
    isLoading,
    credentials,
    registerBiometric,
    authenticateWithBiometric,
    removeBiometricCredential,
    loadCredentials,
    checkSupport
  };
};
