
import { useMemo } from 'react';
import { usePaymentsLogic } from './usePaymentsLogic';
import { paymentProjectionService, ProjectionAnalysis } from '../services/paymentProjectionService';
import { TemporalPeriod } from './useTemporalNavigation';

export const usePaymentProjections = (currentPeriod: TemporalPeriod) => {
  const { allPayments } = usePaymentsLogic();

  const projectionAnalysis = useMemo((): ProjectionAnalysis => {
    // Solo generar proyecciones si estamos viendo un período futuro
    if (!currentPeriod.isFutureMonth) {
      return {
        projections: [],
        totalProjected: 0,
        confidenceScore: 0,
        patternsSummary: { monthly: 0, weekly: 0, yearly: 0, irregular: 0 },
        recommendations: []
      };
    }

    // Filtrar pagos históricos (excluir proyecciones previas)
    const historicalPayments = allPayments.filter(payment => 
      payment.status === 'paid' || payment.status === 'overdue'
    );

    if (historicalPayments.length === 0) {
      return {
        projections: [],
        totalProjected: 0,
        confidenceScore: 0,
        patternsSummary: { monthly: 0, weekly: 0, yearly: 0, irregular: 0 },
        recommendations: ['No hay suficiente historial de pagos para generar proyecciones confiables.']
      };
    }

    return paymentProjectionService.generateProjections(historicalPayments, 6);
  }, [allPayments, currentPeriod.isFutureMonth]);

  const projectedPaymentsForPeriod = useMemo(() => {
    return projectionAnalysis.projections.filter(projection => {
      const projectionDate = new Date(projection.projectedDate);
      const periodStart = new Date(currentPeriod.startDate);
      const periodEnd = new Date(currentPeriod.endDate);
      
      return projectionDate >= periodStart && projectionDate <= periodEnd;
    });
  }, [projectionAnalysis.projections, currentPeriod]);

  return {
    projectionAnalysis,
    projectedPaymentsForPeriod,
    hasProjections: projectionAnalysis.projections.length > 0,
    isProjectionPeriod: currentPeriod.isFutureMonth
  };
};
