
import { z } from 'zod';

export const profileSchema = z.object({
  name: z.string().min(1, 'El nombre completo es requerido'), // Changed from fullName to name
  email: z.string().email('Email inválido'),
  phone: z.string().optional(),
  profession: z.string().optional(), // Changed from occupation to profession
});

export const notificationSchema = z.object({
  paymentReminders: z.boolean(),
  overduePayments: z.boolean(),
  budgetAlerts: z.boolean(),
  goalProgress: z.boolean(),
  emailReports: z.boolean(),
  pushNotifications: z.boolean(),
  weeklyReports: z.boolean(),
  monthlyReports: z.boolean(),
});

export const securitySchema = z.object({
  twoFactorAuth: z.boolean(),
  biometricAuth: z.boolean(),
  sessionTimeout: z.string(), // Changed to string to match Select component usage
  autoLogout: z.boolean(),
  currentPassword: z.string().optional(),
  newPassword: z.string().optional(),
});

export const appearanceSchema = z.object({
  theme: z.enum(['light', 'dark']),
  language: z.enum(['es', 'en']),
  dateFormat: z.enum(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD']),
  numberFormat: z.enum(['es-DO', 'en-US', 'es-ES']),
  showCurrency: z.boolean(),
});

export type ProfileFormData = z.infer<typeof profileSchema>;
export type NotificationFormData = z.infer<typeof notificationSchema>;
export type SecurityFormData = z.infer<typeof securitySchema>;
export type AppearanceFormData = z.infer<typeof appearanceSchema>;
