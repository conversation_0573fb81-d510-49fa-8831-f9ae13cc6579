/* eslint-disable react-refresh/only-export-components */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

export type Currency = 'DOP' | 'USD';

interface NumericInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value?: number | null;
  onChange?: (value: number | null) => void;
  currency?: Currency;
  showCurrency?: boolean;
  className?: string;
}

export const NumericInput: React.FC<NumericInputProps> = ({
  value: controlledValue = null,
  onChange,
  currency = 'DOP',
  showCurrency = false,
  className,
  placeholder = "0.00",
  onBlur: externalOnBlur,
  ...props
}) => {
  const [displayValue, setDisplayValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const lastCursorPos = useRef(0);

  const getCurrencySymbol = (curr: Currency): string => {
    return curr === 'DOP' ? 'RD$' : 'USD$';
  };

  const currencySymbol = showCurrency ? getCurrencySymbol(currency) : '';

  const formatNumberOnBlur = useCallback((val: number | null) => {
    // 1. Convertir a string y manejar nulos/undefined
    let valueStr = String(val === null || typeof val === 'undefined' ? '' : val);

    // 2. Si está vacío después de la conversión, retornar vacío.
    if (valueStr.trim() === "") return "";

    // 3. Quitar el símbolo de moneda si está presente al inicio y cualquier espacio.
    if (currencySymbol && valueStr.startsWith(currencySymbol)) {
      valueStr = valueStr.substring(currencySymbol.length).trim();
    }
    
    // 4. Quitar comas de miles para el parseo.
    const numStrToParse = valueStr.replace(/,/g, '');

    // 5. Validar si es un número válido.
    const numericValue = parseFloat(numStrToParse);
    if (isNaN(numericValue)) {
      return ""; 
    }

        // 6. Formatear el número.
    const splitParts = String(numericValue.toFixed(2)).split('.');
    let integerPart = splitParts[0] || "0";
    const decimalPart = splitParts[1] || "";
    
    if (integerPart === "") integerPart = "0";

    try {
      const formattedIntegerWithCommas = BigInt(integerPart).toLocaleString('en-US', { useGrouping: true });
      return `${currencySymbol}${formattedIntegerWithCommas}.${decimalPart}`;
    } catch {
      return `${currencySymbol}${integerPart}.${decimalPart}`;
    }
  }, [currencySymbol]);

  // Efecto para formatear el valor inicial o cuando cambia externamente
  useEffect(() => {
    if (!isFocused) {
      setDisplayValue(formatNumberOnBlur(controlledValue));
    }
  }, [controlledValue, isFocused, formatNumberOnBlur]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const originalRawValue = e.target.value;
    const currentCursorPos = e.target.selectionStart || 0;
    let newCursorPos = currentCursorPos;

    // 1. Limpiar el valor: solo números y un punto decimal.
    let cleanedValue = originalRawValue.replace(/[^\d.]/g, '');
    
    // 2. Asegurar que solo haya un punto decimal
    let parts = cleanedValue.split('.');
    if (parts.length > 2) {
      cleanedValue = parts[0] + '.' + parts.slice(1).join('');
      parts = cleanedValue.split('.'); // Re-split after cleaning
    }
    
    const integerPart = parts[0] || "";
    let decimalPart = parts[1] || "";
    decimalPart = decimalPart.substring(0, 2); // Limitar decimales

    // 3. Formatear la parte entera con comas
    let formattedInteger = integerPart;
    if (integerPart) {
      const originalIntegerLength = integerPart.length;
      try {
        if (/^\d+$/.test(integerPart)) {
          formattedInteger = BigInt(integerPart).toLocaleString('en-US', {useGrouping: true});
        } else if (integerPart === "") {
          formattedInteger = "";
        } else {
          formattedInteger = integerPart; 
        }
      } catch {
        formattedInteger = integerPart;
      }
      
      if (currentCursorPos <= originalIntegerLength && originalIntegerLength > 0) {
        let digitsBeforeCursorOriginal = 0;
        for(let i = 0; i < currentCursorPos; i++) {
          if (integerPart[i] >= '0' && integerPart[i] <= '9') {
            digitsBeforeCursorOriginal++;
          }
        }
        let tempNewCursorPos = 0;
        let digitsCountedInFormatted = 0;
        for(let i = 0; i < formattedInteger.length; i++) {
          tempNewCursorPos++;
          if (formattedInteger[i] >= '0' && formattedInteger[i] <= '9') {
            digitsCountedInFormatted++;
          }
          if (digitsCountedInFormatted === digitsBeforeCursorOriginal) {
            break;
          }
        }
        newCursorPos = (digitsBeforeCursorOriginal === 0) ? 0 : tempNewCursorPos;
      } else if (parts.length > 1) { 
        newCursorPos = formattedInteger.length + 1 + Math.max(0, currentCursorPos - (originalIntegerLength + 1));
      } else { 
        newCursorPos = formattedInteger.length;
      }
    }
    
    let newDisplayValue = formattedInteger;
    if (parts.length > 1) {
      newDisplayValue += '.' + decimalPart;
    }
    
    setDisplayValue(newDisplayValue);

    const numericValue = parseFloat(cleanedValue.replace(/,/g, ''));
    if (!isNaN(numericValue)) {
      onChange?.(numericValue);
    } else if (cleanedValue === "" || cleanedValue === ".") {
      onChange?.(null); 
    }

    lastCursorPos.current = Math.max(0, Math.min(newCursorPos, newDisplayValue.length));
    
    if (originalRawValue[currentCursorPos - 1] === '.' && !integerPart.includes('.') && newDisplayValue.includes('.')) {
      lastCursorPos.current = newDisplayValue.indexOf('.') + 1;
    }
  };

  useEffect(() => {
    if (inputRef.current && isFocused) { 
      inputRef.current.setSelectionRange(lastCursorPos.current, lastCursorPos.current);
    }
  }, [displayValue, isFocused]); 

  const handleFocus = () => {
    setIsFocused(true);
    const val = String(controlledValue === null || typeof controlledValue === 'undefined' ? '' : controlledValue)
                    .replace(currencySymbol, '') // Quitar símbolo al enfocar
                    .replace(/[^\d.-]/g, '');

    const [integerPart = "", decimalPart = ""] = val.split('.');
    
    let formattedIntegerOnFocus = integerPart;
    if(integerPart) {
      try {
        if (/^\d+$/.test(integerPart)) {
          formattedIntegerOnFocus = BigInt(integerPart).toLocaleString('en-US', {useGrouping: true});
        }
      } catch { /* no hacer nada, mantener integerPart */ }
    }

    let focusDisplayValue = formattedIntegerOnFocus;
    if (val.includes('.')) {
      focusDisplayValue += '.' + decimalPart.substring(0,2);
    } else if (decimalPart && integerPart) { 
      focusDisplayValue += '.' + decimalPart.substring(0,2);
    }

    setDisplayValue(focusDisplayValue);
    setTimeout(() => { 
      if (inputRef.current) {
        const len = focusDisplayValue.length;
        if (parseFloat(val) === 0 && focusDisplayValue.includes('.')) {
          inputRef.current.setSelectionRange(0,0);
          lastCursorPos.current = 0;
        } else {
          inputRef.current.setSelectionRange(len, len);
          lastCursorPos.current = len;
        }
      }
    }, 0);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    const rawValueFromDisplay = displayValue.replace(/,/g, '');
    const numericValueFromDisplay = parseFloat(rawValueFromDisplay);

    if (rawValueFromDisplay === "" || rawValueFromDisplay === ".") {
      if (controlledValue !== null) onChange?.(null);
    } else if (!isNaN(numericValueFromDisplay)) {
      if (numericValueFromDisplay !== controlledValue) {
        onChange?.(numericValueFromDisplay);
      }
    } else {
      if (controlledValue !== null) onChange?.(null);
    }
    
    if (externalOnBlur) {
      externalOnBlur(e); 
    }
  };

  return (
    <div className="relative">
      <Input
        {...props}
        ref={inputRef}
        type="text"
        value={isFocused ? displayValue : formatNumberOnBlur(controlledValue)}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={cn("text-left", className)}
        placeholder={placeholder}
        inputMode="decimal"
      />
    </div>
  );
};

// Componente específico para porcentajes
interface PercentageInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value?: number | null;
  onChange?: (value: number | null) => void;
  className?: string;
}

export const PercentageInput: React.FC<PercentageInputProps> = ({
  value,
  onChange,
  className,
  placeholder = "0.00",
  ...props
}) => {
  return (
    <div className="relative">
      <NumericInput
        {...props}
        value={value}
        onChange={onChange}
        className={cn("pr-8 text-left", className)}
        placeholder={placeholder}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <span className="text-gray-500 text-sm">%</span>
      </div>
    </div>
  );
};

// Componente específico para números enteros
interface IntegerInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value?: number | null;
  onChange?: (value: number | null) => void;
  className?: string;
}

export const IntegerInput: React.FC<IntegerInputProps> = ({
  value,
  onChange,
  className,
  placeholder = "0",
  ...props
}) => {
  const handleChange = (newValue: number | null) => {
    if (newValue !== null) {
      onChange?.(Math.round(newValue));
    } else {
      onChange?.(null);
    }
  };

  return (
    <NumericInput
      {...props}
      value={value}
      onChange={handleChange}
      className={cn("text-left", className)}
      placeholder={placeholder}
    />
  );
};

export const formatCurrency = (amount: number, currency: Currency = 'DOP'): string => {
  const symbol = currency === 'DOP' ? 'RD$' : 'USD$';
  return `${symbol}${amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`;
};

export const getAmountInDOP = (amount: number, currency: Currency): number => {
  return currency === 'USD' ? amount * 59 : amount;
};
