import { logger } from "@/utils/logger";

// Security utility functions
export const generateSecureId = (): string => {
  return crypto.randomUUID();
};

export const hashString = async (str: string): Promise<string> => {
  const encoder = new TextEncoder();
  const data = encoder.encode(str);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

export const validateSessionToken = (token: string): boolean => {
  // Basic token validation
  if (!token || token.length < 10) return false;
  
  // Check if token contains only valid characters
  const validTokenRegex = /^[A-Za-z0-9._-]+$/;
  return validTokenRegex.test(token);
};

export const rateLimit = (() => {
  const attempts: Record<string, { count: number; lastAttempt: number }> = {};
  
  return (identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) => {
    const now = Date.now();
    const userAttempts = attempts[identifier];
    
    if (!userAttempts) {
      attempts[identifier] = { count: 1, lastAttempt: now };
      return true;
    }
    
    // Reset if window has passed
    if (now - userAttempts.lastAttempt > windowMs) {
      attempts[identifier] = { count: 1, lastAttempt: now };
      return true;
    }
    
    // Check if under limit
    if (userAttempts.count < maxAttempts) {
      userAttempts.count++;
      userAttempts.lastAttempt = now;
      return true;
    }
    
    return false;
  };
})();

export const auditLog = (
  action: string, 
  details: Record<string, unknown> = {}, 
  userId?: string, 
  severity: 'low' | 'medium' | 'high' = 'low'
) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    action,
    userId: userId || 'anonymous',
    details,
    severity,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown'
  };
  
  logger.debug(`[AUDIT-${severity.toUpperCase()}]`, JSON.stringify(logEntry));
  
  // In production, you would send this to your logging service
  // Example: await logToService(logEntry);
};

export const sanitizeError = (error: unknown): string => {
  // Prevent sensitive information leakage in error messages
  const sensitivePatterns = [
    /password/gi,
    /token/gi,
    /key/gi,
    /secret/gi,
    /email/gi,
    /user_id/gi
  ];
  
  let message = 'An unexpected error occurred';
  if (typeof error === 'object' && error !== null && 'message' in error && typeof (error as Record<string, unknown>).message === 'string') {
    message = (error as Record<string, unknown>).message as string;
  }
  
  sensitivePatterns.forEach(pattern => {
    message = message.replace(pattern, '[REDACTED]');
  });
  
  return message;
};
