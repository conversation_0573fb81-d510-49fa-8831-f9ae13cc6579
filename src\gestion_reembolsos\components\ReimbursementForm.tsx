
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { NumericInput } from '@/components/ui/numeric-input';
import { Reimbursement, reimbursementCategories } from '@/types';

interface ReimbursementFormProps {
  onSubmit: (reimbursement: Omit<Reimbursement, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdate?: (id: string, updates: Partial<Reimbursement>) => void;
  onCancel: () => void;
  editingReimbursement?: Reimbursement | null;
  isLoading?: boolean;
}

export function ReimbursementForm({ onSubmit, onUpdate, onCancel, editingReimbursement, isLoading = false }: ReimbursementFormProps) {
  const [formData, setFormData] = useState({
    description: editingReimbursement?.description || '',
    amount: editingReimbursement?.amount || 0,
    currency: editingReimbursement?.currency || 'DOP' as 'DOP' | 'USD',
    categoryId: editingReimbursement?.categoryId || '',
    categoryName: editingReimbursement?.categoryName || '',
    date: editingReimbursement?.date || new Date().toISOString().split('T')[0],
    status: editingReimbursement?.status || 'Pendiente' as 'Pendiente' | 'Rechazado' | 'Procesando' | 'Completado',
    reimbursementDate: editingReimbursement?.reimbursementDate || null,
    attachments: editingReimbursement?.attachments || [],
    notes: editingReimbursement?.notes || ''
  });

  console.log('ReimbursementForm - editingReimbursement:', editingReimbursement);
  console.log('ReimbursementForm - formData:', formData);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isLoading) return;
    
    console.log('ReimbursementForm - handleSubmit called');
    console.log('ReimbursementForm - editingReimbursement:', editingReimbursement);
    console.log('ReimbursementForm - onUpdate function:', onUpdate);
    
    if (editingReimbursement && onUpdate) {
      console.log('ReimbursementForm - Updating reimbursement with ID:', editingReimbursement.id);
      console.log('ReimbursementForm - Update data:', formData);
      onUpdate(editingReimbursement.id, formData);
    } else {
      console.log('ReimbursementForm - Creating new reimbursement');
      onSubmit(formData);
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    const category = reimbursementCategories.find(cat => cat.id === categoryId);
    setFormData({ 
      ...formData, 
      categoryId, 
      categoryName: category?.name || '' 
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {editingReimbursement ? 'Editar Reembolso' : 'Nuevo Reembolso'}
          {isLoading && (
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="description">Descripción *</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descripción del gasto"
                required
                disabled={isLoading}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Categoría *</Label>
              <Select 
                value={formData.categoryId} 
                onValueChange={handleCategoryChange}
                disabled={isLoading}
              >
                <SelectTrigger id="category" autoComplete="off">
                  <SelectValue placeholder="Seleccionar categoría" />
                </SelectTrigger>
                <SelectContent>
                  {reimbursementCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Monto *</Label>
              <NumericInput
                id="amount"
                value={formData.amount}
                onChange={(value) => setFormData({ ...formData, amount: value || 0 })}
                currency={formData.currency}
                showCurrency
                disabled={isLoading}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Moneda</Label>
              <Select 
                value={formData.currency} 
                onValueChange={(value: 'DOP' | 'USD') => setFormData({ ...formData, currency: value })}
                disabled={isLoading}
              >
                <SelectTrigger id="currency" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOP">Pesos (DOP)</SelectItem>
                  <SelectItem value="USD">Dólares (USD)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Fecha del Gasto *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                required
                disabled={isLoading}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Estado</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value: 'Pendiente' | 'Rechazado' | 'Procesando' | 'Completado') => setFormData({ ...formData, status: value })}
                disabled={isLoading}
              >
                <SelectTrigger id="status" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Pendiente">Pendiente</SelectItem>
                  <SelectItem value="Procesando">Procesando</SelectItem>
                  <SelectItem value="Completado">Completado</SelectItem>
                  <SelectItem value="Rechazado">Rechazado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notas Adicionales</Label>
          <Textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            placeholder="Notas o comentarios adicionales..."
            rows={3}
            disabled={isLoading}
              autoComplete="off"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button 
              type="submit" 
              className="flex-1" 
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  {editingReimbursement ? 'Actualizando...' : 'Agregando...'}
                </div>
              ) : (
                editingReimbursement ? 'Actualizar Reembolso' : 'Agregar Reembolso'
              )}
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
