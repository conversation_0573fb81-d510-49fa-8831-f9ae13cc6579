
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff } from 'lucide-react';

interface SignupFormFieldsProps {
  signupData: {
    fullName: string;
    email: string;
    phone: string;
    profession: string;
    password: string;
    confirmPassword: string;
  };
  validationErrors: {[key: string]: string};
  isLoading: boolean;
  onInputChange: (field: string, value: string) => void;
}

export const SignupFormFields: React.FC<SignupFormFieldsProps> = ({
  signupData,
  validationErrors,
  isLoading,
  onInputChange
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="signup-name">Nombre Completo *</Label>
          <Input
            id="signup-name"
            placeholder="Tu nombre completo"
            value={signupData.fullName}
            onChange={(e) => onInputChange('fullName', e.target.value)}
            required
            disabled={isLoading}
            className={validationErrors.fullName ? 'border-red-500' : ''}
          />
          {validationErrors.fullName && (
            <p className="text-sm text-red-600">{validationErrors.fullName}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="signup-phone">Teléfono</Label>
          <Input
            id="signup-phone"
            placeholder="+****************"
            value={signupData.phone}
            onChange={(e) => onInputChange('phone', e.target.value)}
            disabled={isLoading}
            className={validationErrors.phone ? 'border-red-500' : ''}
          />
          {validationErrors.phone && (
            <p className="text-sm text-red-600">{validationErrors.phone}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="signup-profession">Profesión</Label>
        <Input
          id="signup-profession"
          placeholder="Tu profesión"
          value={signupData.profession}
          onChange={(e) => onInputChange('profession', e.target.value)}
          disabled={isLoading}
          className={validationErrors.profession ? 'border-red-500' : ''}
        />
        {validationErrors.profession && (
          <p className="text-sm text-red-600">{validationErrors.profession}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="signup-email">Correo Electrónico *</Label>
        <Input
          id="signup-email"
          type="email"
          placeholder="<EMAIL>"
          value={signupData.email}
          onChange={(e) => onInputChange('email', e.target.value)}
          required
          disabled={isLoading}
          className={validationErrors.email ? 'border-red-500' : ''}
        />
        {validationErrors.email && (
          <p className="text-sm text-red-600">{validationErrors.email}</p>
        )}
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="signup-password">Contraseña *</Label>
        <div className="relative">
          <Input
            id="signup-password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Mínimo 8 caracteres" // Cambiado de 12 a 8
            value={signupData.password}
            onChange={(e) => onInputChange('password', e.target.value)}
            required
            disabled={isLoading}
            className={validationErrors.password ? 'border-red-500' : ''}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3"
            onClick={() => setShowPassword(!showPassword)}
            disabled={isLoading}
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
        {validationErrors.password && (
          <p className="text-sm text-red-600">{validationErrors.password}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="signup-confirm">Confirmar Contraseña *</Label>
        <div className="relative">
          <Input
            id="signup-confirm"
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Repite tu contraseña"
            value={signupData.confirmPassword}
            onChange={(e) => onInputChange('confirmPassword', e.target.value)}
            required
            disabled={isLoading}
            className={validationErrors.confirmPassword ? 'border-red-500' : ''}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            disabled={isLoading}
          >
            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
        {validationErrors.confirmPassword && (
          <p className="text-sm text-red-600">{validationErrors.confirmPassword}</p>
        )}
      </div>
    </>
  );
};
