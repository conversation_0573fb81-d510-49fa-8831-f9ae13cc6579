
import { logger } from "@/utils/logger";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { PaymentRecord } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { QUERY_KEYS } from '@/constants/queryKeys';

const QUERY_KEY = QUERY_KEYS.PAYMENT_RECORDS;

// Transform database object to TypeScript interface
const transformPaymentRecord = (dbPaymentRecord: any): PaymentRecord => ({
  id: dbPaymentRecord.id,
  paymentType: dbPaymentRecord.payment_type,
  referenceId: dbPaymentRecord.reference_id,
  dueDate: dbPaymentRecord.due_date,
  amount: dbPaymentRecord.amount,
  currency: dbPaymentRecord.currency as "DOP" | "USD",
  status: dbPaymentRecord.status as "pending" | "paid" | "overdue",
  paidDate: dbPaymentRecord.paid_date,
  notes: dbPaymentRecord.notes,
  createdAt: dbPaymentRecord.created_at,
  updatedAt: dbPaymentRecord.updated_at
});

// Transform TypeScript interface to database object
const transformPaymentRecordToDb = (paymentRecord: Omit<PaymentRecord, 'id' | 'createdAt' | 'updatedAt'>, userId: string) => ({
  user_id: userId,
  payment_type: paymentRecord.paymentType,
  reference_id: paymentRecord.referenceId,
  due_date: paymentRecord.dueDate,
  amount: paymentRecord.amount,
  currency: paymentRecord.currency,
  status: paymentRecord.status,
  paid_date: paymentRecord.paidDate,
  notes: paymentRecord.notes
});

export const useSupabasePaymentRecords = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Fetch payment records
  const { data: paymentRecords = [], isLoading, error } = useQuery({
    queryKey: [QUERY_KEY, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      logger.debug('Fetching payment records from Supabase for user:', user.id);
      const { data, error } = await supabase
        .from('payment_records')
        .select('*')
        .eq('user_id', user.id)
        .order('due_date', { ascending: true });
      
      if (error) {
        console.error('Error fetching payment records:', error);
        throw error;
      }
      
      logger.debug('Fetched payment records:', data);
      return data.map(transformPaymentRecord);
    },
    enabled: !!user?.id && enabled,
    staleTime: 1000 * 60 * 2, // Reducido a 2 minutos para mayor sincronización
    gcTime: 1000 * 60 * 5, // Reducido a 5 minutos
  });

  // Función mejorada para invalidar queries relacionadas
  const invalidateRelatedQueries = async () => {
    if (!user?.id) return;
    
    const relatedQueries = [
      [QUERY_KEY, user.id],
      [QUERY_KEYS.DEBTS, user.id],
      [QUERY_KEYS.EXPENSES, user.id],
      [QUERY_KEYS.OTHER_DATA, user.id],
      [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, user.id]
    ];

    await Promise.all(
      relatedQueries.map(queryKey =>
        queryClient.invalidateQueries({ queryKey, exact: true })
      )
    );
  };

  // Add payment record mutation
  const addPaymentRecordMutation = useMutation({
    mutationFn: async (paymentRecord: Omit<PaymentRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const dbPaymentRecord = transformPaymentRecordToDb(paymentRecord, user.id);
      
      logger.debug('Adding payment record to Supabase:', dbPaymentRecord);
      const { data, error } = await supabase
        .from('payment_records')
        .insert([dbPaymentRecord])
        .select()
        .single();
      
      if (error) {
        console.error('Error adding payment record:', error);
        throw error;
      }
      
      logger.debug('Added payment record:', data);
      return transformPaymentRecord(data);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  // Update payment record mutation
  const updatePaymentRecordMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<PaymentRecord> }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      logger.debug('Updating payment record in Supabase:', id, updates);
      const dbUpdates: any = {};
      
      if (updates.paymentType !== undefined) dbUpdates.payment_type = updates.paymentType;
      if (updates.referenceId !== undefined) dbUpdates.reference_id = updates.referenceId;
      if (updates.dueDate !== undefined) dbUpdates.due_date = updates.dueDate;
      if (updates.amount !== undefined) dbUpdates.amount = updates.amount;
      if (updates.currency !== undefined) dbUpdates.currency = updates.currency;
      if (updates.status !== undefined) dbUpdates.status = updates.status;
      if (updates.paidDate !== undefined) dbUpdates.paid_date = updates.paidDate;
      if (updates.notes !== undefined) dbUpdates.notes = updates.notes;
      
      const { data, error } = await supabase
        .from('payment_records')
        .update(dbUpdates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
      
      if (error) {
        console.error('Error updating payment record:', error);
        throw error;
      }
      
      logger.debug('Updated payment record:', data);
      return transformPaymentRecord(data);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  // Delete payment record mutation
  const deletePaymentRecordMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      logger.debug('Deleting payment record from Supabase:', id);
      const { error } = await supabase
        .from('payment_records')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
      
      if (error) {
        console.error('Error deleting payment record:', error);
        throw error;
      }
      
      logger.debug('Deleted payment record with id:', id);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  // Mark payment as paid mutation
  const markPaymentAsPaidMutation = useMutation({
    mutationFn: async ({ id, paidDate, notes }: { id: string; paidDate: string; notes?: string }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      logger.debug('Marking payment as paid:', id, paidDate);
      const updatePayload: any = {
        status: 'paid',
        paid_date: paidDate
      };
      if (notes !== undefined) updatePayload.notes = notes;

      const { data, error } = await supabase
        .from('payment_records')
        .update(updatePayload)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
      
      if (error) {
        console.error('Error marking payment as paid:', error);
        throw error;
      }
      
      logger.debug('Marked payment as paid:', data);
      return transformPaymentRecord(data);
    },
    onSuccess: async () => {
      await invalidateRelatedQueries();
    },
  });

  return {
    paymentRecords,
    isLoading,
    error,
    addPaymentRecord: addPaymentRecordMutation.mutateAsync,
    updatePaymentRecord: (
      id: string,
      updates: Partial<PaymentRecord>
    ) => updatePaymentRecordMutation.mutateAsync({ id, updates }),
    deletePaymentRecord: deletePaymentRecordMutation.mutate,
    markPaymentAsPaid: (id: string, paidDate: string, notes?: string) =>
      markPaymentAsPaidMutation.mutateAsync({ id, paidDate, notes }),
    isAddingPaymentRecord: addPaymentRecordMutation.isPending,
    isUpdatingPaymentRecord: updatePaymentRecordMutation.isPending,
    isDeletingPaymentRecord: deletePaymentRecordMutation.isPending,
  };
};
