interface RateLimitEntry {
  count: number;
  firstAttempt: number;
  lastAttempt: number;
}

export class RateLimiter {
  private attempts: Map<string, RateLimitEntry> = new Map();
  private readonly CLEANUP_INTERVAL = 60 * 1000; // 1 minute

  constructor() {
    setInterval(() => {
      this.cleanup();
    }, this.CLEANUP_INTERVAL);
  }

  checkLimit(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now();
    const entry = this.attempts.get(key);

    if (!entry) {
      this.attempts.set(key, { count: 1, firstAttempt: now, lastAttempt: now });
      return true;
    }

    if (now - entry.firstAttempt > windowMs) {
      this.attempts.set(key, { count: 1, firstAttempt: now, lastAttempt: now });
      return true;
    }

    if (entry.count >= maxAttempts) {
      entry.lastAttempt = now;
      return false;
    }

    entry.count++;
    entry.lastAttempt = now;
    return true;
  }

  clearAttempts(key: string): void {
    this.attempts.delete(key);
  }

  private cleanup(): void {
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1 hour

    for (const [key, entry] of this.attempts.entries()) {
      if (now - entry.lastAttempt > maxAge) {
        this.attempts.delete(key);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();
