
import { Income as IncomeType } from '@/types';

// Escala progresiva del ISR para República Dominicana 2024 (aplicable para 2025)
const ISR_TAX_BRACKETS = [
  { min: 0, max: 416220, rate: 0, base: 0 },
  { min: 416220.01, max: 624329, rate: 0.15, base: 0 },
  { min: 624329.01, max: 867123, rate: 0.20, base: 31216 },
  { min: 867123.01, max: Infinity, rate: 0.25, base: 79776 }
];

// Porcentajes de cotización para ARS y AFP
const ARS_EMPLOYEE_RATE = 0.0304; // 3.04%
const AFP_EMPLOYEE_RATE = 0.0287; // 2.87%

// Tope salarial cotizable (debe actualizarse según normativa vigente)
const COTIZABLE_SALARY_CAP = 250000; // Ejemplo - verificar tope actual

const calculateISR = (annualTaxableIncome: number): number => {
  // Encontrar el tramo correspondiente
  const bracket = ISR_TAX_BRACKETS.find(bracket => 
    annualTaxableIncome >= bracket.min && annualTaxableIncome <= bracket.max
  );
  
  if (!bracket || bracket.rate === 0) {
    return 0;
  }
  
  // Calcular el excedente sobre el mínimo del tramo
  const excess = annualTaxableIncome - bracket.min;
  
  // ISR = base del tramo + (excedente × tasa del tramo)
  return bracket.base + (excess * bracket.rate);
};

const calculateARSAFP = (salary: number) => {
  // Aplicar el tope salarial cotizable
  const cotizableSalary = Math.min(salary, COTIZABLE_SALARY_CAP);
  
  const arsContribution = cotizableSalary * ARS_EMPLOYEE_RATE;
  const afpContribution = cotizableSalary * AFP_EMPLOYEE_RATE;
  
  return {
    arsContribution,
    afpContribution,
    totalARSAFP: arsContribution + afpContribution,
    cotizableSalary
  };
};

export const useIncomeCalculations = (formData: Partial<IncomeType>) => {
  const calculateValues = () => {
    if (!formData.fixedSalary) return null;

    const variableAmount = (formData.fixedSalary * (formData.variablePercentage || 0)) / 100;
    
    // El incentivo trimestral se calcula sobre la suma del sueldo fijo + sueldo variable
    const baseForQuarterlyIncentive = formData.fixedSalary + variableAmount;
    const quarterlyIncentiveAmount = (baseForQuarterlyIncentive * (formData.performancePercentage || 0)) / 100;
    
    const otherIncomeTotal = (formData.otherIncomeItems || []).reduce((total, item) => {
      const amountInCurrency = item.currency === formData.currency ? item.amount : 
        (item.currency === 'USD' && formData.currency === 'DOP') ? item.amount * 59 : 
        (item.currency === 'DOP' && formData.currency === 'USD') ? item.amount / 59 : item.amount;
      return total + amountInCurrency;
    }, 0);
    
    // CAMBIO IMPORTANTE: Separar ingresos gravables de otros ingresos
    // Ingresos gravables (sujetos a deducciones): sueldo fijo + variable + incentivo trimestral + depreciación vehículo
    const taxableGrossIncome = 
      formData.fixedSalary + 
      (formData.vehicleDepreciation || 0) +
      variableAmount + 
      quarterlyIncentiveAmount;
    
    // Ingreso bruto total (incluye otros ingresos que NO son gravables para efectos de ISR)
    const grossIncome = taxableGrossIncome + otherIncomeTotal;
    
    // Calcular ARS y AFP basado ÚNICAMENTE en el salario cotizable (sueldo fijo + variable)
    const salaryForARSAFP = formData.fixedSalary + variableAmount;
    const arsAfpCalculation = calculateARSAFP(salaryForARSAFP);
    
    // CAMBIO CRÍTICO: Calcular ISR ÚNICAMENTE sobre ingresos gravables (sin otros ingresos)
    // Base gravable = ingresos gravables - aportes TSS (NO incluye otros ingresos)
    const taxableIncome = taxableGrossIncome - arsAfpCalculation.totalARSAFP;
    const annualTaxableIncome = Math.max(0, taxableIncome * 12); // Evitar valores negativos
    const annualISR = calculateISR(annualTaxableIncome);
    const monthlyIRS = annualISR / 12;
    
    // Deducciones totales (solo se aplican a ingresos gravables)
    const totalLegalDeductions = monthlyIRS + arsAfpCalculation.totalARSAFP + (formData.legalDeductions || 0);
    
    // Ingreso neto = Ingreso bruto total - deducciones que solo aplican a ingresos gravables - préstamo nómina
    const netIncome = grossIncome - totalLegalDeductions - (formData.payrollLoan || 0);

    const variableScenarios = {
      base: netIncome,
      medium: netIncome * 1.19,
      optimal: netIncome * 1.22
    };

    return {
      variableAmount,
      quarterlyIncentive: quarterlyIncentiveAmount,
      grossIncome,
      netIncome,
      monthlyIRS,
      arsContribution: arsAfpCalculation.arsContribution,
      afpContribution: arsAfpCalculation.afpContribution,
      totalARSAFP: arsAfpCalculation.totalARSAFP,
      cotizableSalary: arsAfpCalculation.cotizableSalary,
      taxableIncome, // Base gravable del ISR (sin otros ingresos)
      variableScenarios
    };
  };

  return calculateValues();
};
