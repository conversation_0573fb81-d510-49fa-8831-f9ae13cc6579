
// src/seguimiento_pagos/hooks/usePaymentsLogic.test.ts

import { renderHook, act } from '@testing-library/react';
import { describe, beforeEach, expect, test, vi } from 'vitest';
import { usePaymentsLogic, PaymentItem } from './usePaymentsLogic';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { PaymentActionService } from '../services/paymentActionService';
import { useQueryClient } from '@tanstack/react-query';
import { logger } from '@/utils/logger';

// Mock dependencies
vi.mock('@/utils/logger');
vi.mock('@/hooks/useFinanceData');
vi.mock('@/contexts/AuthContext');
vi.mock('@/hooks/use-toast');
vi.mock('../services/paymentActionService');
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual<typeof import('@tanstack/react-query')>('@tanstack/react-query');
  return {
    ...actual,
    useQueryClient: vi.fn(),
    useQuery: vi.fn().mockReturnValue({ data: 1, isLoading: false, error: null }),
  };
});

// Mock PaymentActionService prototype
const mockHandleUnmarkAsPaidService = vi.fn();
PaymentActionService.prototype.handleUnmarkAsPaid = mockHandleUnmarkAsPaidService;

describe('usePaymentsLogic - handleUnmarkAsPaid', () => {
  let mockQueryClient: {
    invalidateQueries: ReturnType<typeof vi.fn>;
    refetchQueries: ReturnType<typeof vi.fn>;
    getQueryData: ReturnType<typeof vi.fn>;
    setQueryData: ReturnType<typeof vi.fn>;
  };
  let mockToastFn: ReturnType<typeof vi.fn>;

  const mockUser = { id: 'user123', email: '<EMAIL>' };
  const mockPaymentItem: PaymentItem = {
    id: 'payment1',
    type: 'expense',
    name: 'Test Payment',
    amount: 100,
    currency: 'USD',
    dueDate: '2024-08-15',
    status: 'paid',
    paidDate: '2024-08-01',
    recordId: 'record1',
    referenceId: 'ref1',
  };

  // Mock implementations for useFinanceData
  const mockUpdatePaymentRecord = vi.fn();
  const mockDeletePersonalDebtPayment = vi.fn();
  const mockUpdatePersonalDebt = vi.fn();
  const mockPersonalDebts: any[] = [];

  beforeEach(() => {
    vi.clearAllMocks();

    mockToastFn = vi.fn();
    (useToast as ReturnType<typeof vi.fn>).mockReturnValue({ toast: mockToastFn });

    mockQueryClient = {
      invalidateQueries: vi.fn().mockResolvedValue(undefined),
      refetchQueries: vi.fn().mockResolvedValue(undefined),
      getQueryData: vi.fn().mockReturnValue([]),
      setQueryData: vi.fn(),
    };
    (useQueryClient as ReturnType<typeof vi.fn>).mockReturnValue(mockQueryClient);

    (useAuth as ReturnType<typeof vi.fn>).mockReturnValue({ user: mockUser });

    (useFinanceData as ReturnType<typeof vi.fn>).mockReturnValue({
      personalDebts: mockPersonalDebts,
      updatePaymentRecord: mockUpdatePaymentRecord,
      deletePersonalDebtPayment: mockDeletePersonalDebtPayment,
      updatePersonalDebt: mockUpdatePersonalDebt,
      creditCards: [], loans: [], subscriptions: [], expenses: [], paymentRecords: [],
      addPaymentRecord: vi.fn(), markPaymentAsPaid: vi.fn(), addPersonalDebtPayment: vi.fn(),
      rate: 1
    });
  });

  test('should throw error if user is not authenticated', async () => {
    (useAuth as ReturnType<typeof vi.fn>).mockReturnValue({ user: null });
    const { result } = renderHook(() => usePaymentsLogic());

    await expect(result.current.handleUnmarkAsPaid(mockPaymentItem)).rejects.toThrow(
      'User not authenticated for unmarking payment.'
    );
    expect(logger.error).toHaveBeenCalledWith('handleUnmarkAsPaid: User not authenticated');
    expect(mockHandleUnmarkAsPaidService).not.toHaveBeenCalled();
  });

  test('should successfully unmark payment and show success toast', async () => {
    mockHandleUnmarkAsPaidService.mockResolvedValueOnce(undefined);
    const { result } = renderHook(() => usePaymentsLogic());

    await act(async () => {
      await result.current.handleUnmarkAsPaid(mockPaymentItem);
    });

    expect(mockQueryClient.getQueryData).toHaveBeenCalledWith(['payment-records', mockUser.id]);
    expect(mockQueryClient.setQueryData).toHaveBeenCalledTimes(1);
    expect(mockQueryClient.setQueryData).toHaveBeenCalledWith(
      ['payment-records', mockUser.id],
      expect.any(Function)
    );

    expect(mockHandleUnmarkAsPaidService).toHaveBeenCalledWith(
      mockPaymentItem,
      expect.any(Function)
    );

    const expectedQueryKeys = [
      ['payment-records', mockUser.id],
      ['debts', mockUser.id],
      ['personal-debt-payments', mockUser.id],
      ['expenses', mockUser.id],
      ['loans', mockUser.id],
      ['subscriptions', mockUser.id],
    ];

    expectedQueryKeys.forEach(queryKey => {
      expect(mockQueryClient.invalidateQueries).toHaveBeenCalledWith({ queryKey, exact: true });
      expect(mockQueryClient.refetchQueries).toHaveBeenCalledWith({ queryKey, exact: true });
    });

    expect(mockToastFn).toHaveBeenCalledWith({
      title: 'Pago Desmarcado',
      description: `El pago "${mockPaymentItem.name}" ha sido marcado como pendiente.`,
    });
    expect(logger.info).toHaveBeenCalledWith('handleUnmarkAsPaid: Success toast displayed for payment ID:', mockPaymentItem.id);
  });

  test('should show error toast and re-throw if service call fails', async () => {
    const serviceError = new Error('Service failed');
    mockHandleUnmarkAsPaidService.mockRejectedValueOnce(serviceError);
    const { result } = renderHook(() => usePaymentsLogic());

    await expect(result.current.handleUnmarkAsPaid(mockPaymentItem)).rejects.toThrow('Service failed');

    expect(mockQueryClient.getQueryData).toHaveBeenCalledWith([
      'payment-records',
      mockUser.id,
    ]);
    expect(mockQueryClient.setQueryData).toHaveBeenNthCalledWith(
      1,
      ['payment-records', mockUser.id],
      expect.any(Function)
    );
    expect(mockQueryClient.setQueryData).toHaveBeenNthCalledWith(
      2,
      ['payment-records', mockUser.id],
      expect.anything()
    );

    expect(mockHandleUnmarkAsPaidService).toHaveBeenCalledTimes(1);
    expect(mockHandleUnmarkAsPaidService).toHaveBeenCalledWith(
      mockPaymentItem,
      expect.any(Function)
    );
    expect(mockToastFn).toHaveBeenCalledWith({
      title: 'Error al Desmarcar Pago',
      description: `Hubo un problema al desmarcar el pago "${mockPaymentItem.name}". Por favor, inténtalo de nuevo.`,
      variant: 'destructive',
    });
    expect(logger.error).toHaveBeenCalledWith(
      'handleUnmarkAsPaid: Error calling paymentActionService.handleUnmarkAsPaid',
      expect.objectContaining({ paymentId: mockPaymentItem.id, error: serviceError })
    );
  });

  test('should log initial and final messages', async () => {
    mockHandleUnmarkAsPaidService.mockResolvedValueOnce(undefined);
    const { result } = renderHook(() => usePaymentsLogic());

    await act(async () => {
      await result.current.handleUnmarkAsPaid(mockPaymentItem);
    });

    expect(logger.info).toHaveBeenCalledWith('handleUnmarkAsPaid: Function entry for payment ID:', mockPaymentItem.id);
    expect(logger.debug).toHaveBeenCalledWith('handleUnmarkAsPaid: Payment object to unmark', mockPaymentItem);
    // This specific test focuses on entry/exit and core debug logs. Other info logs are implicitly covered by successful execution.
    expect(logger.info).toHaveBeenCalledWith('handleUnmarkAsPaid: Function exit for payment ID:', mockPaymentItem.id);
  });
});
