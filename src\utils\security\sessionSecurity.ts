
import { supabase } from '@/integrations/supabase/client';
import { auditLog } from '@/utils/securityUtils';

interface SessionSecurityConfig {
  maxAge: number; // in milliseconds
  refreshThreshold: number; // in milliseconds
  maxInactivity: number; // in milliseconds
}

export class SessionSecurityManager {
  private config: SessionSecurityConfig;
  private lastActivity: number;
  private refreshTimer: NodeJS.Timeout | null = null;
  private inactivityTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<SessionSecurityConfig> = {}) {
    this.config = {
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      refreshThreshold: 15 * 60 * 1000, // 15 minutes before expiry
      maxInactivity: 30 * 60 * 1000, // 30 minutes
      ...config
    };
    this.lastActivity = Date.now();
    this.setupActivityTracking();
  }

  private setupActivityTracking(): void {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    activityEvents.forEach(event => {
      document.addEventListener(event, this.updateActivity.bind(this), true);
    });
  }

  private updateActivity(): void {
    this.lastActivity = Date.now();
    localStorage.setItem('last_activity', this.lastActivity.toString());
    
    // Reset inactivity timer
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }
    
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivity();
    }, this.config.maxInactivity);
  }

  private async handleInactivity(): Promise<void> {
    auditLog('session_inactivity_detected', {
      inactivityDuration: Date.now() - this.lastActivity
    });
    
    // Lock screen or logout user
    window.dispatchEvent(new CustomEvent('security:sessionInactive'));
  }

  public async checkSessionSecurity(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        auditLog('session_security_check_failed', { error: error?.message });
        return false;
      }

      // Check if session is close to expiry
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const timeUntilExpiry = expiresAt - Date.now();
      
      if (timeUntilExpiry < this.config.refreshThreshold) {
        auditLog('session_refresh_required', { timeUntilExpiry });
        return this.refreshSession();
      }

      return true;
    } catch (error) {
      auditLog('session_security_check_error', { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      return false;
    }
  }

  private async refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error || !data.session) {
        auditLog('session_refresh_failed', { error: error?.message });
        return false;
      }

      auditLog('session_refresh_success', {});
      return true;
    } catch (error) {
      auditLog('session_refresh_error', { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      return false;
    }
  }

  public startSessionMonitoring(): void {
    // Check session security every 5 minutes
    this.refreshTimer = setInterval(() => {
      this.checkSessionSecurity();
    }, 5 * 60 * 1000);

    // Initial activity timer
    this.updateActivity();
  }

  public stopSessionMonitoring(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
    
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }
  }

  public getSessionStatus(): {
    isActive: boolean;
    lastActivity: number;
    timeSinceLastActivity: number;
  } {
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    
    return {
      isActive: timeSinceLastActivity < this.config.maxInactivity,
      lastActivity: this.lastActivity,
      timeSinceLastActivity
    };
  }
}

// Global session security manager instance
export const sessionSecurity = new SessionSecurityManager();
