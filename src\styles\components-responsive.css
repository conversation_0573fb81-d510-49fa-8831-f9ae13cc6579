/* ==============================================
   ESTILOS RESPONSIVOS PARA COMPONENTES ESPECÍFICOS
   ============================================== */

/* ==============================================
   TARJETAS DE ESTADÍSTICAS (STATS CARDS)
   ============================================== */

/* Mobile-first para tarjetas de estadísticas */
.stats-card-grid,
.stats-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .stats-card-grid,
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .stats-card-grid,
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .stats-card-grid,
  .stats-cards {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.25rem;
  }
}

/* ==============================================
   LISTAS DE TRANSACCIONES
   ============================================== */

.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.transaction-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  border-radius: 0.375rem;
  background: var(--card);
  border: 1px solid var(--border);
}

@media (min-width: 768px) {
  .transaction-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
  }
}

/* ==============================================
   GRILLAS DE GASTOS/INGRESOS
   ============================================== */

.finance-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .finance-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .finance-grid.three-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* ==============================================
   FORMULARIOS RESPONSIVOS
   ============================================== */

/* Formularios 100% ancho en móvil */
@media (max-width: 767px) {
  .form-responsive input,
  .form-responsive textarea,
  .form-responsive select,
  .form-responsive button[type="submit"],
  .form-responsive .input,
  .form-responsive .textarea,
  .form-responsive .select {
    width: 100% !important;
  }

  /* Grupos de campos en columna en móvil */
  .form-row,
  .form-group-horizontal {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (min-width: 768px) {
  .form-row,
  .form-group-horizontal {
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }
  
  .form-row > *,
  .form-group-horizontal > * {
    flex: 1;
  }
}

/* ==============================================
   GRÁFICOS RESPONSIVOS
   ============================================== */


.chart-container-responsive {
  width: 100%;
  height: 200px;
  position: relative;
}

@media (min-width: 768px) {
  .chart-container-responsive {
    height: 250px;
  }
}

@media (min-width: 1024px) {
  .chart-container-responsive {
    height: 300px;
  }
}

/* Canvas de gráficos responsivos */
.chart-container-responsive canvas {
  max-width: 100% !important;
  height: auto !important;
}

/* ==============================================
   NAVEGACIÓN INFERIOR MÓVIL
   ============================================== */

@media (max-width: 767px) {
  .bottom-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--background);
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: space-around;
    padding: 0.5rem;
    padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
    z-index: 50;
  }

  .bottom-navigation-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    font-size: 0.75rem;
    color: var(--muted-foreground);
    transition: all 0.2s;
  }

  .bottom-navigation-item.active {
    color: var(--primary);
  }

  /* Ajustar padding inferior del contenido principal */
  .main-content-mobile {
    padding-bottom: calc(60px + env(safe-area-inset-bottom));
  }
}

/* ==============================================
   MODALES RESPONSIVOS
   ============================================== */

@media (max-width: 767px) {
  /* Modales full screen en móvil */
  .modal-responsive,
  .dialog-responsive {
    position: fixed !important;
    inset: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .modal-content-responsive,
  .dialog-content-responsive {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .modal-body-responsive,
  .dialog-body-responsive {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* ==============================================
   TABLAS RESPONSIVAS
   ============================================== */

@media (max-width: 767px) {
  .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-responsive table {
    min-width: 100%;
  }

  /* Opción: Convertir tabla en cards en móvil */
  .table-cards tbody,
  .table-cards tr,
  .table-cards td {
    display: block;
  }

  .table-cards tr {
    margin-bottom: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 0.75rem;
  }

  .table-cards td {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
  }

  .table-cards td::before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 0.5rem;
  }
}

/* ==============================================
   COMPONENTES DE PAGO
   ============================================== */

.payment-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .payment-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .payment-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* ==============================================
   UTILIDADES ADICIONALES
   ============================================== */

/* Texto responsivo */
.text-responsive {
  font-size: 0.875rem; /* 14px */
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: 1rem; /* 16px */
  }
}

/* Espaciado responsivo */
.spacing-responsive {
  padding: 0.75rem;
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: 1.5rem;
  }
}

/* Botones responsivos */
.btn-responsive {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  width: 100%;
}

@media (min-width: 768px) {
  .btn-responsive {
    width: auto;
    padding: 0.625rem 1.25rem;
    font-size: 1rem;
  }
}