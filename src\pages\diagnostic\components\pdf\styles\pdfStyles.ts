
export const pdfStyles = {
  // Colores del sistema con mejor contraste
  colors: {
    primary: '#1E40AF', // Azul más oscuro para mejor contraste
    secondary: '#6B21A8', // Púrpura más oscuro
    success: '#047857', // Verde más oscuro
    warning: '#B45309', // Naranja más oscuro
    danger: '#B91C1C', // Rojo más oscuro
    neutral: {
      50: '#F8FAFC',
      100: '#F1F5F9',
      200: '#E2E8F0',
      300: '#CBD5E1',
      400: '#94A3B8',
      500: '#64748B',
      600: '#475569',
      700: '#334155',
      800: '#1E293B',
      900: '#0F172A'
    },
    // Colores específicos para texto legible
    text: {
      primary: '#0F172A', // Negro más suave
      secondary: '#1E293B', // <PERSON><PERSON> muy oscuro
      muted: '#475569' // Gris oscuro para texto secundario
    }
  },

  // Tipografía mejorada
  typography: {
    fontFamily: "'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif",
    fontSize: {
      xs: '11px', // Aumentado de 10px
      sm: '12px', // Aumentado de 11px
      base: '13px', // Aumentado de 12px
      lg: '15px', // Aumentado de 14px
      xl: '17px', // Aumentado de 16px
      '2xl': '20px', // Aumentado de 18px
      '3xl': '24px', // Aumentado de 22px
      '4xl': '28px' // Aumentado de 24px
    },
    lineHeight: {
      tight: '1.3', // Mejorado de 1.2
      normal: '1.5', // Mejorado de 1.4
      relaxed: '1.7' // Mejorado de 1.6
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700'
    }
  },

  // Espaciado
  spacing: {
    xs: '4px', // Aumentado de 3px
    sm: '6px',
    md: '8px',
    lg: '12px', // Aumentado de 10px
    xl: '15px', // Aumentado de 12px
    '2xl': '18px', // Aumentado de 15px
    '3xl': '24px', // Aumentado de 20px
    '4xl': '30px' // Aumentado de 25px
  },

  // Layout
  layout: {
    pageWidth: '210mm',
    pageHeight: '297mm',
    padding: '20mm'
  }
};

// Funciones helper para estilos con mejor legibilidad
export const createBaseStyle = (overrides: React.CSSProperties = {}): React.CSSProperties => ({
  fontFamily: pdfStyles.typography.fontFamily,
  fontSize: pdfStyles.typography.fontSize.base,
  lineHeight: pdfStyles.typography.lineHeight.normal,
  color: pdfStyles.colors.text.primary, // Usando color de texto más legible
  fontWeight: pdfStyles.typography.fontWeight.normal,
  ...overrides
});

export const createPageStyle = (overrides: React.CSSProperties = {}): React.CSSProperties => ({
  height: pdfStyles.layout.pageHeight,
  width: pdfStyles.layout.pageWidth,
  padding: pdfStyles.layout.padding,
  boxSizing: 'border-box',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#FFFFFF',
  color: pdfStyles.colors.text.primary,
  ...overrides
});

export const createHeaderStyle = (overrides: React.CSSProperties = {}): React.CSSProperties => ({
  borderBottom: `3px solid ${pdfStyles.colors.primary}`,
  paddingBottom: pdfStyles.spacing['3xl'],
  marginBottom: pdfStyles.spacing['4xl'],
  flexShrink: 0,
  ...overrides
});

export const createCardStyle = (overrides: React.CSSProperties = {}): React.CSSProperties => ({
  backgroundColor: '#FFFFFF', // Fondo blanco para mejor contraste
  border: `2px solid ${pdfStyles.colors.neutral[300]}`, // Borde más visible
  borderRadius: '8px',
  padding: pdfStyles.spacing['3xl'],
  ...overrides
});

export const createTableStyle = (): React.CSSProperties => ({
  width: '100%',
  borderCollapse: 'collapse',
  fontSize: pdfStyles.typography.fontSize.sm,
  color: pdfStyles.colors.text.primary
});

export const createTableCellStyle = (align: 'left' | 'center' | 'right' = 'left'): React.CSSProperties => ({
  padding: `${pdfStyles.spacing.md} ${pdfStyles.spacing.lg}`, // Más padding
  border: `1px solid ${pdfStyles.colors.neutral[400]}`, // Borde más visible
  textAlign: align,
  fontSize: pdfStyles.typography.fontSize.sm,
  color: pdfStyles.colors.text.primary,
  lineHeight: pdfStyles.typography.lineHeight.normal
});

// Nuevas funciones para elementos específicos con alta legibilidad
export const createTitleStyle = (level: 'h1' | 'h2' | 'h3' = 'h1'): React.CSSProperties => {
  const sizes = {
    h1: pdfStyles.typography.fontSize['4xl'],
    h2: pdfStyles.typography.fontSize['2xl'],
    h3: pdfStyles.typography.fontSize.xl
  };
  
  return createBaseStyle({
    fontSize: sizes[level],
    fontWeight: pdfStyles.typography.fontWeight.bold,
    color: pdfStyles.colors.text.primary,
    lineHeight: pdfStyles.typography.lineHeight.tight
  });
};

export const createTextStyle = (variant: 'primary' | 'secondary' | 'muted' = 'primary'): React.CSSProperties => {
  const colors = {
    primary: pdfStyles.colors.text.primary,
    secondary: pdfStyles.colors.text.secondary,
    muted: pdfStyles.colors.text.muted
  };
  
  return createBaseStyle({
    color: colors[variant]
  });
};
