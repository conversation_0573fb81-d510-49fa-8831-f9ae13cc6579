
-- Actualizar la función para contar usuarios correctamente desde auth.users
CREATE OR REPLACE FUNCTION public.calculate_dynamic_statistics()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
DECLARE
  user_count integer;
  transaction_count integer;
  achieved_goals integer;
  total_savings_amount numeric;
  avg_income numeric;
  debt_reduction numeric;
BEGIN
  -- Contar usuarios activos desde auth.users (usuarios realmente registrados)
  SELECT COUNT(*) INTO user_count FROM auth.users;
  
  -- Contar transacciones totales (ingresos + gastos + reembolsos)
  SELECT 
    COALESCE((SELECT COUNT(*) FROM public.incomes), 0) +
    COALESCE((SELECT COUNT(*) FROM public.expenses), 0) +
    COALESCE((SELECT COUNT(*) FROM public.reimbursements), 0)
  INTO transaction_count;
  
  -- Contar metas financieras alcanzadas
  SELECT COUNT(*) INTO achieved_goals 
  FROM public.financial_goals 
  WHERE current_amount >= amount AND is_active = true;
  
  -- Calcular ahorros totales (suma de metas alcanzadas)
  SELECT COALESCE(SUM(current_amount), 0) INTO total_savings_amount
  FROM public.financial_goals 
  WHERE current_amount >= amount AND is_active = true;
  
  -- Calcular promedio de ingresos mensuales
  SELECT COALESCE(AVG(net_income), 0) INTO avg_income
  FROM public.incomes;
  
  -- Calcular porcentaje de reducción de deuda (estimado)
  debt_reduction := 15.0;
  
  -- Actualizar estadísticas
  UPDATE public.app_statistics 
  SET 
    active_users = user_count,
    total_transactions = transaction_count,
    goals_achieved = achieved_goals,
    total_savings = total_savings_amount,
    avg_monthly_income = avg_income,
    debt_reduction_percentage = debt_reduction,
    updated_at = NOW()
  WHERE id = (
    SELECT id FROM public.app_statistics 
    ORDER BY created_at DESC 
    LIMIT 1
  );
  
  -- Si no existe registro, crear uno
  IF NOT FOUND THEN
    INSERT INTO public.app_statistics (
      active_users, 
      total_transactions, 
      goals_achieved, 
      total_savings, 
      avg_monthly_income, 
      debt_reduction_percentage,
      uptime_percentage, 
      rating, 
      support_hours
    ) VALUES (
      user_count, 
      transaction_count, 
      achieved_goals, 
      total_savings_amount, 
      avg_income, 
      debt_reduction,
      99.9, 
      4.9, 
      '24/7'
    );
  END IF;
END;
$function$;

-- Mejorar la función handle_new_user para asegurar que siempre se creen perfiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER 
SET search_path TO 'public'
AS $function$
BEGIN
  -- Crear perfil para el nuevo usuario
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'name'),
    new.raw_user_meta_data->>'avatar_url'
  );
  
  -- Actualizar estadísticas inmediatamente
  PERFORM public.calculate_dynamic_statistics();
  
  RETURN new;
END;
$function$;

-- Asegurar que el trigger existe
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Crear perfiles para usuarios existentes que no los tengan
INSERT INTO public.profiles (id, email, full_name)
SELECT 
  u.id,
  u.email,
  COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name', u.email)
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- Actualizar las estadísticas con el conteo correcto
SELECT public.calculate_dynamic_statistics();
