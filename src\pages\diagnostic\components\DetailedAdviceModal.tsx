
// import { Badge } from '@/components/ui/badge'; // Unused import
import { Star, AlertTriangle } from 'lucide-react';

interface DetailedAdviceModalProps {
  netBalance: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
}

export function DetailedAdviceModal({
  netBalance,
  savingsRate,
  debtToIncomeRatio,
  emergencyFundMonths
}: DetailedAdviceModalProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-blue-500/5 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 flex items-center">
            <Star className="w-4 h-4 mr-2 text-blue-500" />
            Fortalezas Identificadas
          </h4>
          <ul className="space-y-1 text-sm">
            {netBalance > 0 && <li>• Mantienes un balance positivo</li>}
            {savingsRate >= 15 && <li>• Tienes buenos hábitos de ahorro</li>}
            {debtToIncomeRatio <= 35 && <li>• Tu nivel de deuda es manejable</li>}
            {emergencyFundMonths >= 3 && <li>• Tienes un fondo de emergencia</li>}
          </ul>
        </div>
        
        <div className="bg-yellow-500/5 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 flex items-center">
            <AlertTriangle className="w-4 h-4 mr-2 text-yellow-500" />
            Áreas de Mejora
          </h4>
          <ul className="space-y-1 text-sm">
            {savingsRate < 20 && <li>• Incrementar tasa de ahorro</li>}
            {debtToIncomeRatio > 30 && <li>• Reducir nivel de endeudamiento</li>}
            {emergencyFundMonths < 6 && <li>• Fortalecer fondo de emergencia</li>}
            {netBalance < 0 && <li>• Ajustar gastos para balance positivo</li>}
          </ul>
        </div>
      </div>
      
      <div>
        <h4 className="font-semibold mb-3">Consejos Personalizados por Categoría</h4>
        
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h5 className="font-medium text-green-500 mb-2">💰 Gestión de Ingresos</h5>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• Diversifica tus fuentes de ingreso cuando sea posible</li>
              <li>• Negocia aumentos salariales basados en tu rendimiento</li>
              <li>• Considera ingresos pasivos o de inversión</li>
              <li>• Mantén un registro detallado de todos tus ingresos</li>
            </ul>
          </div>
          
          <div className="border rounded-lg p-4">
            <h5 className="font-medium text-red-500 mb-2">📊 Control de Gastos</h5>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• Categoriza todos tus gastos mensualmente</li>
              <li>• Aplica la regla 50/30/20 (necesidades/deseos/ahorros)</li>
              <li>• Revisa suscripciones y servicios periódicamente</li>
              <li>• Usa apps de presupuesto para mejor control</li>
            </ul>
          </div>
          
          <div className="border rounded-lg p-4">
            <h5 className="font-medium text-yellow-500 mb-2">🏦 Manejo de Deudas</h5>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• Prioriza pagar deudas con mayor tasa de interés</li>
              <li>• Considera consolidación si reduce costos totales</li>
              <li>• Evita el endeudamiento para gastos no esenciales</li>
              <li>• Mantén un historial crediticio positivo</li>
            </ul>
          </div>
          
          <div className="border rounded-lg p-4">
            <h5 className="font-medium text-blue-500 mb-2">🎯 Planificación Futura</h5>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• Establece metas financieras específicas y medibles</li>
              <li>• Revisa y ajusta tu plan financiero trimestralmente</li>
              <li>• Considera asesoría profesional para inversiones</li>
              <li>• Mantente informado sobre tendencias económicas</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
