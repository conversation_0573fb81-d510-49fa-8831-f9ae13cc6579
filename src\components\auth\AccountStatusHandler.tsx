
import React from 'react';
import { User } from '@supabase/supabase-js';
import { EmailVerificationAlert } from './EmailVerificationAlert';
import { useEmailVerification } from '@/hooks/useEmailVerification';

interface AccountStatusHandlerProps {
  user: User | null;
  showInline?: boolean;
  onDismiss?: () => void;
}

export const AccountStatusHandler: React.FC<AccountStatusHandlerProps> = ({ 
  user, 
  showInline = false,
  onDismiss 
}) => {
  const { isVerified, isChecking } = useEmailVerification(user);

  // No mostrar nada si no hay usuario o está verificando
  if (!user || isChecking) {
    return null;
  }

  // Solo mostrar alerta si el email no está verificado
  if (!isVerified) {
    return (
      <div className={showInline ? '' : 'mb-4'}>
        <EmailVerificationAlert
          userEmail={user.email}
          isVerified={isVerified}
          onDismiss={onDismiss}
          showDismiss={!!onDismiss}
        />
      </div>
    );
  }

  // Si está verificado y es inline, mostrar confirmación
  if (showInline && isVerified) {
    return (
      <div className="mb-4">
        <EmailVerificationAlert
          userEmail={user.email}
          isVerified={isVerified}
          onDismiss={onDismiss}
          showDismiss={!!onDismiss}
        />
      </div>
    );
  }

  return null;
};
