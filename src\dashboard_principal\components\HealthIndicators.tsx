
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface HealthIndicatorsProps {
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
}

type HealthStatus = {
  status: 'good' | 'warning' | 'danger';
  colorClasses: string;
  description: string;
};

const getHealthStatus = (
  value: number,
  goodThreshold: number,
  warningThreshold: number,
  lowerIsBetter: boolean = false,
  descriptions: { good: string; warning: string; danger: string }
): HealthStatus => {
  if (lowerIsBetter) {
    if (value <= goodThreshold) {
      return { status: 'good', colorClasses: 'text-green-700 bg-green-100', description: descriptions.good };
    }
    if (value <= warningThreshold) {
      return { status: 'warning', colorClasses: 'text-yellow-700 bg-yellow-100', description: descriptions.warning };
    }
    return { status: 'danger', colorClasses: 'text-red-700 bg-red-100', description: descriptions.danger };
  } else {
    if (value >= goodThreshold) {
      return { status: 'good', colorClasses: 'text-green-700 bg-green-100', description: descriptions.good };
    }
    if (value >= warningThreshold) {
      return { status: 'warning', colorClasses: 'text-yellow-700 bg-yellow-100', description: descriptions.warning };
    }
    return { status: 'danger', colorClasses: 'text-red-700 bg-red-100', description: descriptions.danger };
  }
};

export function HealthIndicators({ 
  savingsRate, 
  debtToIncomeRatio, 
  emergencyFundMonths 
}: HealthIndicatorsProps) {

  const indicatorsConfig = [
    {
      id: 'savingsRate',
      title: 'Tasa de Ahorro',
      rawValue: savingsRate,
      valueFormatted: `${savingsRate.toFixed(1)}%`,
      progressMax: 40,
      unit: '%',
      lowerIsBetter: false,
      thresholds: { good: 20, warning: 10 },
      descriptions: { good: 'Excelente', warning: 'Bueno', danger: 'Mejorar' },
    },
    {
      id: 'debtToIncomeRatio',
      title: 'Ratio Deuda/Ingresos',
      rawValue: debtToIncomeRatio,
      valueFormatted: `${debtToIncomeRatio.toFixed(1)}%`,
      progressMax: 60,
      unit: '%',
      lowerIsBetter: true,
      thresholds: { good: 30, warning: 40 },
      descriptions: { good: 'Saludable', warning: 'Moderado', danger: 'Alto Riesgo' },
    },
    {
      id: 'emergencyFundMonths',
      title: 'Fondo de Emergencia',
      rawValue: emergencyFundMonths,
      valueFormatted: `${emergencyFundMonths.toFixed(1)} meses`,
      progressMax: 12,
      unit: 'meses',
      lowerIsBetter: false,
      thresholds: { good: 6, warning: 3 },
      descriptions: { good: 'Excelente', warning: 'Adecuado', danger: 'Insuficiente' },
    },
  ];

  return (
    <Card className="border-finanz-border">
      <CardHeader>
        <CardTitle className="text-finanz-primary">Indicadores de Salud Financiera</CardTitle>
        <CardDescription>
          Métricas clave para evaluar tu estabilidad financiera.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {indicatorsConfig.map((indicator) => {
          const health = getHealthStatus(
            indicator.rawValue,
            indicator.thresholds.good,
            indicator.thresholds.warning,
            indicator.lowerIsBetter,
            indicator.descriptions
          );
          return (
            <div key={indicator.id} className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{indicator.title}</span>
                <Badge className={`text-xs ${health.colorClasses}`}>
                  {indicator.valueFormatted} - {health.description}
                </Badge>
              </div>
              <Progress
                value={indicator.rawValue}
                max={indicator.progressMax}
                className="h-2"
                indicatorClassName={
                  health.status === 'good' ? 'bg-green-500' :
                  health.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }
              />
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
