
import { useState, useCallback } from 'react';
import { Expense, defaultCategories } from '@/types';
import { useToast } from '@/hooks/use-toast';

// Helper function to get current date in YYYY-MM-DD format
const getCurrentDate = () => {
  return new Date().toISOString().split('T')[0];
};

// Initial form state factory
const createInitialFormData = (defaultCurrency: 'DOP' | 'USD') => ({
  date: getCurrentDate(),
  currency: defaultCurrency,
  amount: 0,
  type: 'Variable' as const,
  categoryId: '',
  description: '',
  paymentMethod: 'debit-card' as const,
  // New expenses start as 'pending' until marked as paid
  status: 'pending' as const,
  paymentDate: getCurrentDate(),
  isRecurring: false
});

export function useExpenseForm(
  onAddExpense: (expense: Omit<Expense, 'id' | 'month' | 'createdAt' | 'updatedAt'>) => void,
  defaultCurrency: 'DOP' | 'USD'
) {
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<Partial<Expense>>(() => 
    createInitialFormData(defaultCurrency)
  );

  const resetForm = useCallback(() => {
    setFormData(createInitialFormData(defaultCurrency));
  }, [defaultCurrency]); // createInitialFormData is stable as it's defined outside the hook

  const handleFormDataChange = useCallback((updates: Partial<Expense>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.date || !formData.amount || !formData.categoryId) {
      toast({
        title: "Error",
        description: "Por favor complete todos los campos requeridos",
        variant: "destructive"
      });
      return;
    }

    const category = defaultCategories.find(cat => cat.id === formData.categoryId);
    
    const expenseData: Omit<Expense, 'id' | 'month' | 'createdAt' | 'updatedAt'> = {
      date: formData.date!,
      currency: formData.currency || defaultCurrency,
      amount: formData.amount,
      type: formData.type!,
      categoryId: formData.categoryId!,
      categoryName: category?.name || '',
      description: formData.description || '',
      paymentMethod: formData.paymentMethod!,
      status: formData.status!,
      paymentDate: formData.isRecurring
        ? (formData.paymentDate ? formData.paymentDate.padStart(2, '0') : undefined)
        : formData.paymentDate,
      isRecurring: formData.isRecurring ?? false
    };

    // Assuming onAddExpense will be made async by the caller
    await onAddExpense(expenseData);
    
    toast({
      title: "Éxito",
      description: "Gasto registrado correctamente",
    });

    // Form reset is now handled by the caller via resetForm
    // setFormData(createInitialFormData(defaultCurrency));
  }, [formData, onAddExpense, defaultCurrency, toast]);

  return {
    formData,
    handleFormDataChange,
    handleSubmit,
    resetForm
  };
}
