
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { ThemeSelector } from './appearance/ThemeSelector';
import { LanguageSelector } from './appearance/LanguageSelector';
import { DataFormatSettings } from './appearance/DataFormatSettings';
import type { AppearanceFormData } from '../schemas/settingsSchemas';

interface AppearanceSettingsTabProps {
  appearanceForm: UseFormReturn<AppearanceFormData>;
  saveAppearance: (data: AppearanceFormData) => Promise<void>;
  applyThemeImmediately: (theme: 'light' | 'dark') => void;
  isLoading: boolean;
}

export const AppearanceSettingsTab: React.FC<AppearanceSettingsTabProps> = ({
  appearanceForm,
  saveAppearance,
  applyThemeImmediately,
  isLoading,
}) => {
  const onSubmit = async (data: AppearanceFormData) => {
    await saveAppearance(data);
  };

  return (
    <Form {...appearanceForm}>
      <form onSubmit={appearanceForm.handleSubmit(onSubmit)} className="space-y-6">
        <ThemeSelector 
          appearanceForm={appearanceForm}
          applyThemeImmediately={applyThemeImmediately}
        />
        
        <div className="px-6">
          <LanguageSelector appearanceForm={appearanceForm} />
        </div>

        <DataFormatSettings 
          appearanceForm={appearanceForm}
          isLoading={isLoading}
        />
      </form>
    </Form>
  );
};
