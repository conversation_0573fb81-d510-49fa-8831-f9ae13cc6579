import { renderHook, act } from '@testing-library/react';
import { useTemporalNavigation } from './useTemporalNavigation';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { format, startOfMonth, endOfMonth, addMonths, subMonths } from 'date-fns';
import { es } from 'date-fns/locale';

describe('useTemporalNavigation Hook', () => {
  const MOCK_TODAY = '2024-07-15T10:00:00.000Z'; // July 15, 2024

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date(MOCK_TODAY));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  const today = new Date(MOCK_TODAY);
  const startOfCurrentMonth = startOfMonth(today);
  const endOfCurrentMonth = endOfMonth(today);

  it('should initialize with the current month correctly', () => {
    const { result } = renderHook(() => useTemporalNavigation());

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({
      from: startOfCurrentMonth,
      to: endOfCurrentMonth,
    });
    expect(result.current.currentPeriod.startDate).toBe(format(startOfCurrentMonth, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.endDate).toBe(format(endOfCurrentMonth, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.displayName).toBe(format(today, 'MMMM yyyy', { locale: es }));
    expect(result.current.currentPeriod.isCurrentMonth).toBe(true);
    expect(result.current.currentPeriod.isPastMonth).toBe(false);
    expect(result.current.currentPeriod.isFutureMonth).toBe(false);
    expect(result.current.currentPeriod.year).toBe(2024);
    expect(result.current.currentPeriod.month).toBe(7); // July
  });

  it('setDateRange: should switch to range mode with a valid custom date range', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const fromDate = new Date(2024, 6, 10); // July 10, 2024
    const toDate = new Date(2024, 6, 20);   // July 20, 2024

    act(() => {
      result.current.setDateRange({ from: fromDate, to: toDate });
    });

    expect(result.current.currentDisplayMode).toBe('range');
    expect(result.current.selectedDateInput).toEqual({ from: fromDate, to: toDate });
    expect(result.current.currentPeriod.startDate).toBe(format(fromDate, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.endDate).toBe(format(toDate, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.displayName).toBe('10/07/2024 - 20/07/2024');
    expect(result.current.currentPeriod.isCurrentMonth).toBe(false);
    expect(result.current.currentPeriod.isPastMonth).toBe(false);
    expect(result.current.currentPeriod.isFutureMonth).toBe(false);
  });

  it('setDateRange: should switch to month mode if "from" and "to" are the same day', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const singleDate = new Date(2024, 7, 5); // August 5, 2024
    const startOfAugust = startOfMonth(singleDate);
    const endOfAugust = endOfMonth(singleDate);

    act(() => {
      result.current.setDateRange({ from: singleDate, to: singleDate });
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({ from: startOfAugust, to: endOfAugust });
    expect(result.current.currentPeriod.startDate).toBe(format(startOfAugust, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.endDate).toBe(format(endOfAugust, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.displayName).toBe(format(singleDate, 'MMMM yyyy', { locale: es }));
  });

  it('setDateRange: should switch to month mode if only "from" is provided', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const fromDateOnly = new Date(2024, 7, 10); // August 10, 2024
    const startOfAugust = startOfMonth(fromDateOnly);
    const endOfAugust = endOfMonth(fromDateOnly);

    act(() => {
      result.current.setDateRange({ from: fromDateOnly, to: undefined });
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({ from: startOfAugust, to: endOfAugust });
    expect(result.current.currentPeriod.displayName).toBe(format(fromDateOnly, 'MMMM yyyy', { locale: es }));
  });

  it('setDateRange: should revert to current month if dateRange is undefined', () => {
    const { result } = renderHook(() => useTemporalNavigation());
     act(() => { // First change to something else
      result.current.setDateRange({ from: new Date(2024, 5, 1), to: new Date(2024, 5, 5) });
    });
    act(() => {
      result.current.setDateRange(undefined);
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({
      from: startOfCurrentMonth,
      to: endOfCurrentMonth,
    });
    expect(result.current.currentPeriod.displayName).toBe(format(today, 'MMMM yyyy', { locale: es }));
  });

  it('navigateToMonth: should navigate to specified month and set mode to "month"', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const targetYear = 2025;
    const targetMonth = 3; // March
    const expectedDate = new Date(targetYear, targetMonth - 1, 1);
    const startOfTargetMonth = startOfMonth(expectedDate);
    const endOfTargetMonth = endOfMonth(expectedDate);

    act(() => {
      result.current.navigateToMonth(targetYear, targetMonth);
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({ from: startOfTargetMonth, to: endOfTargetMonth });
    expect(result.current.currentPeriod.startDate).toBe(format(startOfTargetMonth, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.endDate).toBe(format(endOfTargetMonth, 'yyyy-MM-dd'));
    expect(result.current.currentPeriod.displayName).toBe(format(expectedDate, 'MMMM yyyy', { locale: es }));
    expect(result.current.currentPeriod.isFutureMonth).toBe(true);
  });

  it('navigateToPreviousMonth: should navigate to the previous month', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const previousMonthDate = subMonths(startOfCurrentMonth, 1);
    const startOfPreviousMonth = startOfMonth(previousMonthDate);
    const endOfPreviousMonth = endOfMonth(previousMonthDate);

    act(() => {
      result.current.navigateToPreviousMonth();
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({ from: startOfPreviousMonth, to: endOfPreviousMonth });
    expect(result.current.currentPeriod.displayName).toBe(format(previousMonthDate, 'MMMM yyyy', { locale: es }));
    expect(result.current.currentPeriod.isPastMonth).toBe(true); // June 2024 is past July 2024
  });

  it('navigateToNextMonth: should navigate to the next month', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const nextMonthDate = addMonths(startOfCurrentMonth, 1);
    const startOfNextMonth = startOfMonth(nextMonthDate);
    const endOfNextMonth = endOfMonth(nextMonthDate);

    act(() => {
      result.current.navigateToNextMonth();
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({ from: startOfNextMonth, to: endOfNextMonth });
    expect(result.current.currentPeriod.displayName).toBe(format(nextMonthDate, 'MMMM yyyy', { locale: es }));
    expect(result.current.currentPeriod.isFutureMonth).toBe(true); // August 2024 is future to July 2024
  });

  it('navigateToCurrentMonth: should navigate to the current calendar month', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    // First, navigate away
    act(() => {
      result.current.navigateToMonth(2023, 1, 1); // Jan 2023
    });
    // Then navigate back to current
    act(() => {
      result.current.navigateToCurrentMonth();
    });

    expect(result.current.currentDisplayMode).toBe('month');
    expect(result.current.selectedDateInput).toEqual({ from: startOfCurrentMonth, to: endOfCurrentMonth });
    expect(result.current.currentPeriod.displayName).toBe(format(today, 'MMMM yyyy', { locale: es }));
    expect(result.current.currentPeriod.isCurrentMonth).toBe(true);
  });

  describe('currentPeriod properties', () => {
    it('isPastMonth, isCurrentMonth, isFutureMonth flags for a past month', () => {
      const { result } = renderHook(() => useTemporalNavigation());
      act(() => result.current.navigateToMonth(2024, 6)); // June 2024 (past relative to July 2024)

      expect(result.current.currentPeriod.isPastMonth).toBe(true);
      expect(result.current.currentPeriod.isCurrentMonth).toBe(false);
      expect(result.current.currentPeriod.isFutureMonth).toBe(false);
      expect(result.current.currentPeriod.year).toBe(2024);
      expect(result.current.currentPeriod.month).toBe(6);
    });

    it('isPastMonth, isCurrentMonth, isFutureMonth flags for a future month', () => {
      const { result } = renderHook(() => useTemporalNavigation());
      act(() => result.current.navigateToMonth(2024, 8)); // August 2024 (future relative to July 2024)

      expect(result.current.currentPeriod.isPastMonth).toBe(false);
      expect(result.current.currentPeriod.isCurrentMonth).toBe(false);
      expect(result.current.currentPeriod.isFutureMonth).toBe(true);
      expect(result.current.currentPeriod.year).toBe(2024);
      expect(result.current.currentPeriod.month).toBe(8);
    });

    it('year and month properties for currentPeriod', () => {
      const { result } = renderHook(() => useTemporalNavigation());
      // Initial (July 2024)
      expect(result.current.currentPeriod.year).toBe(2024);
      expect(result.current.currentPeriod.month).toBe(7);

      act(() => result.current.navigateToMonth(2025, 1)); // Jan 2025
      expect(result.current.currentPeriod.year).toBe(2025);
      expect(result.current.currentPeriod.month).toBe(1);

       act(() => { // Range selection
        result.current.setDateRange({ from: new Date(2023, 11, 5), to: new Date(2023, 11, 10) }); // Dec 5-10, 2023
      });
      expect(result.current.currentPeriod.year).toBe(2023); // Year of the start of the range
      expect(result.current.currentPeriod.month).toBe(12); // Month of the start of the range
    });
  });

  it('getAvailableMonths: should return correct list of months', () => {
    const { result } = renderHook(() => useTemporalNavigation());
    const months = result.current.getAvailableMonths(2, 1); // 2 past, current, 1 future = 4 total

    expect(months).toHaveLength(2 + 1 + 1);

    // May 2024 (Past)
    expect(months[0].displayName).toBe(format(subMonths(today,2), 'MMMM yyyy', { locale: es }));
    expect(months[0].isPastMonth).toBe(true);
    expect(months[0].isCurrentMonth).toBe(false);

    // June 2024 (Past)
    expect(months[1].displayName).toBe(format(subMonths(today,1), 'MMMM yyyy', { locale: es }));
    expect(months[1].isPastMonth).toBe(true);
    expect(months[1].isCurrentMonth).toBe(false);

    // July 2024 (Current)
    expect(months[2].displayName).toBe(format(today, 'MMMM yyyy', { locale: es }));
    expect(months[2].isCurrentMonth).toBe(true);

    // August 2024 (Future)
    expect(months[3].displayName).toBe(format(addMonths(today,1), 'MMMM yyyy', { locale: es }));
    expect(months[3].isFutureMonth).toBe(true);
    expect(months[3].isCurrentMonth).toBe(false);
  });
});
