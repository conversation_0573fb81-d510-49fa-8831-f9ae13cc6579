
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useFinanceData } from '@/hooks/useFinanceData';
import { dataExportService } from '../services/dataExportService';

export const useDataManagement = () => {
  const { toast } = useToast();
  const { defaultCurrency, setDefaultCurrency } = useFinanceData();
  const [isLoading, setIsLoading] = useState(false);

  const deleteAllData = async () => {
    try {
      setIsLoading(true);
      await dataExportService.deleteAllUserData();
      
      toast({
        title: 'Datos eliminados',
        description: 'Todos los datos han sido eliminados exitosamente',
      });
    } catch (error) {
      console.error('Error deleting data:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron eliminar los datos',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    deleteAllData,
    defaultCurrency,
    setDefaultCurrency,
    isLoading,
  };
};
