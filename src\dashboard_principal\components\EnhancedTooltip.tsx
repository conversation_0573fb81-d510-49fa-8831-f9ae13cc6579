
import React from 'react';
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus, Info } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';

interface TrendIndicatorProps {
  trend: 'up' | 'down' | 'stable';
  value: number;
  isPositive?: boolean;
}

const TrendIndicator: React.FC<TrendIndicatorProps> = ({ trend, value, isPositive = true }) => {
  const getTrendColor = () => {
    if (trend === 'stable') return 'text-gray-500';
    if (trend === 'up') return isPositive ? 'text-green-500' : 'text-red-500';
    return isPositive ? 'text-red-500' : 'text-green-500';
  };

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus;

  return (
    <div className={`flex items-center gap-1 ${getTrendColor()}`}>
      <TrendIcon className="w-3 h-3" />
      <span className="text-xs font-medium">{Math.abs(value).toFixed(1)}%</span>
    </div>
  );
};

interface EnhancedTooltipProps {
  title: string;
  currentValue: number;
  previousValue?: number;
  currency?: 'DOP' | 'USD';
  trend?: 'up' | 'down' | 'stable';
  changePercentage?: number;
  context?: string;
  recommendations?: string[];
  isPositiveTrend?: boolean;
  children: React.ReactNode;
}

export const EnhancedTooltip: React.FC<EnhancedTooltipProps> = ({
  title,
  currentValue,
  previousValue,
  currency = 'DOP',
  trend,
  changePercentage,
  context,
  recommendations = [],
  isPositiveTrend = true,
  children
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="cursor-help">
            {children}
          </div>
        </TooltipTrigger>
        <TooltipContent className="max-w-sm p-4 space-y-3">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Info className="w-4 h-4 text-blue-500" />
              <h4 className="font-semibold text-sm">{title}</h4>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Valor Actual:</span>
                <span className="font-medium text-sm">
                  {formatCurrency(currentValue, currency)}
                </span>
              </div>
              
              {previousValue !== undefined && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-600">Mes Anterior:</span>
                    <span className="text-sm">
                      {formatCurrency(previousValue, currency)}
                    </span>
                  </div>
                  
                  {trend && changePercentage !== undefined && (
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-600">Cambio:</span>
                      <TrendIndicator 
                        trend={trend} 
                        value={changePercentage} 
                        isPositive={isPositiveTrend}
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {context && (
            <div className="border-t pt-2">
              <p className="text-xs text-gray-600 leading-relaxed">{context}</p>
            </div>
          )}

          {recommendations.length > 0 && (
            <div className="border-t pt-2 space-y-1">
              <div className="flex items-center gap-1 mb-1">
                <Badge variant="outline" className="text-xs px-1 py-0">
                  Consejos
                </Badge>
              </div>
              {recommendations.map((rec, index) => (
                <p key={index} className="text-xs text-blue-600 leading-relaxed">
                  • {rec}
                </p>
              ))}
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
