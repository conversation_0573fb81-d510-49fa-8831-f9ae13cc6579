
import { useEffect, useRef } from 'react';
import { logger } from '@/utils/logger';

export const usePaymentsPerformance = (componentName: string) => {
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);
  
  useEffect(() => {
    renderCountRef.current += 1;
    const now = performance.now();
    
    if (lastRenderTimeRef.current > 0) {
      const timeSinceLastRender = now - lastRenderTimeRef.current;
      
      // Log de performance si hay muchos re-renders en poco tiempo
      if (timeSinceLastRender < 100 && renderCountRef.current > 3) {
        logger.warn(`usePaymentsPerformance: ${componentName} - Frequent re-renders detected`, {
          renderCount: renderCountRef.current,
          timeSinceLastRender
        });
      }
    }
    
    lastRenderTimeRef.current = now;
    
    // Reset counter cada 5 segundos
    const resetTimer = setTimeout(() => {
      renderCountRef.current = 0;
    }, 5000);
    
    return () => clearTimeout(resetTimer);
  });

  return {
    renderCount: renderCountRef.current
  };
};
