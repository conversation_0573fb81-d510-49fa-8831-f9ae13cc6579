
import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

export const useEmailVerification = (user: User | null) => {
  const [isVerified, setIsVerified] = useState(false);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    if (!user) {
      setIsVerified(false);
      return;
    }

    // Verificar si el email está confirmado
    const checkVerificationStatus = () => {
      setIsChecking(true);
      
      // En Supabase, el email se considera verificado cuando email_confirmed_at no es null
      const verified = !!(user.email_confirmed_at);
      setIsVerified(verified);
      setIsChecking(false);
    };

    checkVerificationStatus();

    // Escuchar cambios de autenticación para actualizar el estado
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'TOKEN_REFRESHED' && session?.user) {
          const verified = !!(session.user.email_confirmed_at);
          setIsVerified(verified);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [user]);

  const refreshVerificationStatus = async () => {
    if (!user) return;
    
    try {
      setIsChecking(true);
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        const verified = !!(session.user.email_confirmed_at);
        setIsVerified(verified);
      }
    } catch (error) {
      console.error('Error refreshing verification status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  return {
    isVerified,
    isChecking,
    refreshVerificationStatus
  };
};
