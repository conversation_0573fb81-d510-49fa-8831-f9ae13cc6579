
import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Circle, AlertCircle } from 'lucide-react';

interface RegistrationStep {
  id: string;
  label: string;
  completed: boolean;
  current: boolean;
  error?: boolean;
}

interface RegistrationProgressProps {
  steps: RegistrationStep[];
  currentStep: number;
  totalSteps: number;
}

export const RegistrationProgress: React.FC<RegistrationProgressProps> = ({
  steps,
  currentStep,
  totalSteps
}) => {
  const progressPercentage = (currentStep / totalSteps) * 100;

  const getStepIcon = (step: RegistrationStep) => {
    if (step.error) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    if (step.completed) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <Circle className="w-4 h-4 text-gray-400" />;
  };

  const getStepBadge = (step: RegistrationStep) => {
    if (step.error) {
      return <Badge variant="destructive" className="text-xs">Error</Badge>;
    }
    if (step.completed) {
      return <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">Completado</Badge>;
    }
    if (step.current) {
      return <Badge variant="default" className="text-xs">En progreso</Badge>;
    }
    return <Badge variant="outline" className="text-xs">Pendiente</Badge>;
  };

  return (
    <div className="space-y-4">
      {/* Barra de progreso principal */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium text-gray-700">Progreso del registro</h3>
          <span className="text-sm text-gray-500">
            {currentStep} de {totalSteps}
          </span>
        </div>
        <Progress
          value={progressPercentage}
          className="h-2"
          indicatorClassName="bg-finanz-primary"
        />
        <p className="text-xs text-gray-500">
          {Math.round(progressPercentage)}% completado
        </p>
      </div>

      {/* Lista de pasos */}
      <div className="space-y-2">
        {steps.map((step) => (
          <div 
            key={step.id}
            className={`flex items-center justify-between p-2 rounded-lg transition-colors ${
              step.current 
                ? 'bg-blue-50 border border-blue-200' 
                : step.completed 
                  ? 'bg-green-50' 
                  : step.error
                    ? 'bg-red-50'
                    : 'bg-gray-50'
            }`}
          >
            <div className="flex items-center gap-3">
              {getStepIcon(step)}
              <span className={`text-sm ${
                step.current ? 'font-medium text-blue-700' 
                : step.completed ? 'text-green-700'
                : step.error ? 'text-red-700'
                : 'text-gray-600'
              }`}>
                {step.label}
              </span>
            </div>
            {getStepBadge(step)}
          </div>
        ))}
      </div>

      {/* Indicador de tiempo estimado */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Tiempo estimado restante: {Math.max(0, (totalSteps - currentStep) * 30)} segundos
        </p>
      </div>
    </div>
  );
};
