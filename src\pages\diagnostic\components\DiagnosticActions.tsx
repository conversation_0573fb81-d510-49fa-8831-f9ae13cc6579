import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Download, Calendar, BookOpen, Eye } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { DetailedAdviceModal } from './DetailedAdviceModal';
import { DiagnosticExporter } from './DiagnosticExporter';

interface DiagnosticActionsProps {
  overallScore: number;
  netIncome: number;
  totalExpenses: number;
  netBalance: number;
  totalDebt: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
}

export function DiagnosticActions({
  overallScore,
  // netIncome, // Unused
  // totalExpenses, // Unused
  netBalance,
  // totalDebt, // Unused
  savingsRate,
  debtToIncomeRatio,
  emergencyFundMonths
}: DiagnosticActionsProps) {
  const { toast } = useToast();
  const [showExporter, setShowExporter] = useState(false);
  const [exporterConfig, setExporterConfig] = useState({
    initialTab: 'format' as 'format' | 'preview' | 'settings',
    initialShowPreview: false
  });

  const openExporter = (initialTab: 'format' | 'preview' | 'settings', initialShowPreview: boolean) => {
    setExporterConfig({ initialTab, initialShowPreview });
    setShowExporter(true);
  };

  function handleScheduleReview() {
    const reviewDate = new Date();
    reviewDate.setDate(reviewDate.getDate() + 30);
    
    const scheduledReview = {
      date: reviewDate.toISOString(),
      type: 'financial_diagnostic',
      status: 'scheduled',
      createdAt: new Date().toISOString()
    };
    
    const existingReviews = JSON.parse(localStorage.getItem('scheduledReviews') || '[]');
    localStorage.setItem('scheduledReviews', JSON.stringify([...existingReviews, scheduledReview]));
    
    toast({
      title: 'Revisión Programada',
      description: `Te recordaremos revisar tu diagnóstico financiero el ${reviewDate.toLocaleDateString('es-ES')}`,
    });
  }

  return (
    <Card className="bg-card">
      <CardContent className="pt-6">
        <div className="flex flex-wrap gap-4">
          <Button
            className="bg-blue-500 hover:bg-blue-600 text-white"
            onClick={() => openExporter('format', false)}
          >
            <Download className="w-4 h-4 mr-2" />
            Exportar Diagnóstico Completo
          </Button>

          <Button
            variant="outline"
            onClick={() => openExporter('preview', true)}
          >
            <Eye className="w-4 h-4 mr-2" />
            Vista Previa Interactiva
          </Button>
          
          <Button 
            variant="outline"
            onClick={handleScheduleReview}
          >
            <Calendar className="w-4 h-4 mr-2" />
            Programar Revisión
          </Button>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">
                <BookOpen className="w-4 h-4 mr-2" />
                Ver Consejos Detallados
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-2">
                  <BookOpen className="w-5 h-5" />
                  <span>Consejos Financieros Detallados</span>
                </DialogTitle>
                <DialogDescription>
                  Guía completa personalizada basada en tu situación financiera actual
                </DialogDescription>
              </DialogHeader>
              
              <DetailedAdviceModal
                netBalance={netBalance}
                savingsRate={savingsRate}
                debtToIncomeRatio={debtToIncomeRatio}
                emergencyFundMonths={emergencyFundMonths}
              />
            </DialogContent>
          </Dialog>
        </div>

        <DiagnosticExporter
          isOpen={showExporter}
          onClose={() => setShowExporter(false)}
          initialTab={exporterConfig.initialTab}
          initialShowPreview={exporterConfig.initialShowPreview}
          overallScore={overallScore}
        />
      </CardContent>
    </Card>
  );
}
