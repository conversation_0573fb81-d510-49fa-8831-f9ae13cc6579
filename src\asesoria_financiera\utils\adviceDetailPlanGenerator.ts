
interface ActionStep {
  title: string;
  description: string;
  timeframe: string;
}

interface DetailedPlan {
  overview: string;
  steps: ActionStep[];
  tips: string[];
  potentialSavings: number;
  targetAmount: number;
}

interface AdviceItem {
  id: string;
  title: string;
  description: string;
  type: 'success' | 'warning' | 'danger' | 'info';
  category: 'savings' | 'debt' | 'budget' | 'investment' | 'emergency';
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
}

interface FinancialData {
  netIncome: number;
  totalExpenses: number;
  totalDebt: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
}

export const getDetailedPlan = (advice: AdviceItem, financialData: FinancialData): DetailedPlan => {
  switch (advice.id) {
    case 'low-savings-rate':
    case 'moderate-savings-rate':
      return {
        overview: 'Plan estructurado para mejorar tu tasa de ahorro y construir un futuro financiero sólido.',
        steps: [
          {
            title: '<PERSON><PERSON><PERSON><PERSON> de Gas<PERSON>',
            description: 'Revisa todos tus gastos de los últimos 3 meses y categorízalos en esenciales y no esenciales.',
            timeframe: '1 semana'
          },
          {
            title: 'Identificar Oportunidades',
            description: 'Encuentra áreas donde puedes reducir gastos sin afectar significativamente tu calidad de vida.',
            timeframe: '3-5 días'
          },
          {
            title: 'Establecer Meta de Ahorro',
            description: 'Define un porcentaje específico de tus ingresos que destinarás al ahorro mensualmente.',
            timeframe: '1 día'
          },
          {
            title: 'Automatizar Ahorros',
            description: 'Configura transferencias automáticas a una cuenta de ahorros separada.',
            timeframe: '1 día'
          },
          {
            title: 'Monitoreo Mensual',
            description: 'Revisa mensualmente tu progreso y ajusta tu presupuesto según sea necesario.',
            timeframe: 'Continuo'
          }
        ],
        tips: [
          'Usa la regla 50/30/20: 50% necesidades, 30% deseos, 20% ahorros',
          'Considera usar aplicaciones de presupuesto para trackear gastos',
          'Empieza con una meta pequeña y aumenta gradualmente',
          'Trata el ahorro como un gasto fijo, no opcional'
        ],
        potentialSavings: financialData.netIncome * 0.1,
        targetAmount: financialData.netIncome * 0.2
      };

    case 'low-emergency-fund':
    case 'moderate-emergency-fund':
      return {
        overview: 'Estrategia para construir un fondo de emergencia que te proteja ante imprevistos financieros.',
        steps: [
          {
            title: 'Calcular Meta del Fondo',
            description: 'Determina el monto necesario para cubrir 6 meses de gastos esenciales.',
            timeframe: '1 día'
          },
          {
            title: 'Abrir Cuenta Separada',
            description: 'Crea una cuenta de ahorros específica para emergencias, preferiblemente de fácil acceso.',
            timeframe: '2-3 días'
          },
          {
            title: 'Establecer Contribución Mensual',
            description: 'Define un monto fijo mensual que destinarás al fondo de emergencia.',
            timeframe: '1 día'
          },
          {
            title: 'Automatizar Transferencias',
            description: 'Programa transferencias automáticas mensuales a tu fondo de emergencia.',
            timeframe: '1 día'
          },
          {
            title: 'Revisar y Ajustar',
            description: 'Evalúa trimestralmente si necesitas ajustar tu contribución mensual.',
            timeframe: 'Trimestral'
          }
        ],
        tips: [
          'Mantén el fondo en una cuenta líquida pero separada de tus gastos diarios',
          'Define claramente qué constituye una "emergencia"',
          'Si usas el fondo, prioriza reponerlo lo antes posible',
          'Considera aumentar el fondo si tu situación laboral es inestable'
        ],
        potentialSavings: 0,
        targetAmount: financialData.totalExpenses * 6
      };

    case 'high-debt-ratio':
    case 'moderate-debt-ratio':
      return {
        overview: 'Plan integral para reducir tu carga de deudas y mejorar tu salud financiera.',
        steps: [
          {
            title: 'Inventario de Deudas',
            description: 'Lista todas tus deudas con montos, tasas de interés y pagos mínimos requeridos.',
            timeframe: '2-3 días'
          },
          {
            title: 'Priorizar Deudas',
            description: 'Ordena las deudas por tasa de interés (método avalancha) o por monto (método bola de nieve).',
            timeframe: '1 día'
          },
          {
            title: 'Negociar Términos',
            description: 'Contacta a tus acreedores para negociar mejores tasas o planes de pago.',
            timeframe: '1-2 semanas'
          },
          {
            title: 'Aumentar Pagos',
            description: 'Destina cualquier dinero extra al pago de la deuda prioritaria.',
            timeframe: 'Continuo'
          },
          {
            title: 'Evitar Nuevas Deudas',
            description: 'Implementa estrategias para no adquirir nuevas deudas mientras pagas las existentes.',
            timeframe: 'Continuo'
          }
        ],
        tips: [
          'Considera consolidar deudas si consigues una tasa de interés menor',
          'Usa cualquier ingreso extra (bonos, regalos) para pagar deudas',
          'Evita usar tarjetas de crédito mientras pagas las deudas',
          'Celebra cada deuda que liquides para mantener la motivación'
        ],
        potentialSavings: financialData.totalDebt * 0.05,
        targetAmount: financialData.netIncome * 12 * 0.2
      };

    case 'high-credit-utilization':
      return {
        overview: 'Estrategia para reducir la utilización de tarjetas de crédito y mejorar tu score crediticio.',
        steps: [
          {
            title: 'Reducir Saldos',
            description: 'Prioriza reducir los saldos de las tarjetas por debajo del 30% del límite.',
            timeframe: '2-3 meses'
          },
          {
            title: 'Pagos Múltiples',
            description: 'Realiza pagos múltiples durante el mes para mantener saldos bajos.',
            timeframe: 'Continuo'
          },
          {
            title: 'Solicitar Aumento de Límite',
            description: 'Contacta a tu banco para solicitar un aumento en el límite de crédito.',
            timeframe: '1 semana'
          },
          {
            title: 'Monitorear Score',
            description: 'Revisa mensualmente tu score crediticio para ver las mejoras.',
            timeframe: 'Mensual'
          }
        ],
        tips: [
          'Paga antes de la fecha de corte para mantener saldos reportados bajos',
          'Usa solo un pequeño porcentaje de tu límite disponible',
          'No cierres tarjetas antiguas, solo deja de usarlas',
          'Mantén un historial de pagos puntuales'
        ],
        potentialSavings: 0,
        targetAmount: 0
      };

    default:
      return {
        overview: 'Plan general para mejorar tu situación financiera.',
        steps: [
          {
            title: 'Evaluar Situación Actual',
            description: 'Revisa tu situación financiera actual en detalle.',
            timeframe: '1 semana'
          },
          {
            title: 'Implementar Cambios',
            description: 'Implementa los cambios recomendados gradualmente.',
            timeframe: '1-3 meses'
          }
        ],
        tips: ['Mantén disciplina financiera', 'Revisa tu progreso regularmente'],
        potentialSavings: 0,
        targetAmount: 0
      };
  }
};
