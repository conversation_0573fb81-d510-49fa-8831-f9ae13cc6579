
// import { useMemo } from 'react'; // Unused import

export interface FinancialProjection {
  year: number;
  income: number;
  expenses: number;
  savings: number;
  totalAssets: number;
  netWorth: number;
}

export interface ProjectionParameters {
  currentNetWorth: number;
  annualIncome: number;
  annualExpenses: number;
  investmentReturn: number;
  incomeGrowthRate: number;
  expenseGrowthRate: number;
}

export const generateFinancialProjections = (
  params: ProjectionParameters,
  years: number = 5
): FinancialProjection[] => {
  const projections: FinancialProjection[] = [];
  let currentAssets = params.currentNetWorth;
  
  for (let year = 1; year <= years; year++) {
    const yearlyIncome = params.annualIncome * Math.pow(1 + params.incomeGrowthRate, year);
    const yearlyExpenses = params.annualExpenses * Math.pow(1 + params.expenseGrowthRate, year);
    const yearlySavings = yearlyIncome - yearlyExpenses;
    
    // Aplicar retorno de inversión a los activos existentes
    currentAssets = currentAssets * (1 + params.investmentReturn) + yearlySavings;
    
    projections.push({
      year: new Date().getFullYear() + year,
      income: yearlyIncome,
      expenses: yearlyExpenses,
      savings: yearlySavings,
      totalAssets: currentAssets,
      netWorth: currentAssets
    });
  }
  
  return projections;
};

export const calculateTimeToGoal = (
  targetAmount: number,
  currentAmount: number,
  monthlyContribution: number,
  annualReturn: number = 0.08
): number => {
  if (currentAmount >= targetAmount) return 0;
  if (monthlyContribution <= 0) return Infinity;
  
  const monthlyReturn = annualReturn / 12;
  const remaining = targetAmount - currentAmount;
  
  // Fórmula de anualidad para valor futuro
  if (monthlyReturn === 0) {
    return Math.ceil(remaining / monthlyContribution / 12);
  }
  
  const months = Math.log(1 + (remaining * monthlyReturn) / monthlyContribution) / Math.log(1 + monthlyReturn);
  return Math.ceil(months / 12);
};
