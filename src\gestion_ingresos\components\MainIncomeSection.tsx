
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { NumericInput } from '@/components/ui/numeric-input';
import { Income as IncomeType } from '@/types';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface MainIncomeSectionProps {
  formData: Partial<IncomeType>;
  setFormData: React.Dispatch<React.SetStateAction<Partial<IncomeType>>>;
  isSubmitting?: boolean;
}

export function MainIncomeSection({ formData, setFormData, isSubmitting = false }: MainIncomeSectionProps) {
  const { isMobile } = useBreakpoint();
  
  return (
    <Card>
      <CardHeader className={isMobile ? 'pb-2' : ''}>
        <CardTitle className={`
          text-finanz-success
          ${isMobile ? 'text-base' : 'text-lg'}
        `}>
          INGRESOS PRINCIPALES
        </CardTitle>
      </CardHeader>
      <CardContent className={`
        ${isMobile ? 'space-y-3 pt-0' : 'space-y-4'}
      `}>
        <div className={isMobile ? 'space-y-1' : 'space-y-2'}>
          <Label 
            htmlFor="fixedSalary"
            className={isMobile ? 'text-xs font-medium' : ''}
          >
            Sueldo Fijo *
          </Label>
          <NumericInput
            id="fixedSalary"
            value={formData.fixedSalary || 0}
            onChange={(value) => setFormData(prev => ({ ...prev, fixedSalary: value || 0 }))}
            currency={formData.currency}
            showCurrency
            required
            disabled={isSubmitting}
            className={isMobile ? 'h-8 text-sm' : ''}
            autoComplete="off"
          />
        </div>
        <div className={isMobile ? 'space-y-1' : 'space-y-2'}>
          <Label 
            htmlFor="vehicleDepreciation"
            className={isMobile ? 'text-xs font-medium' : ''}
          >
            Depreciación de Vehículo
          </Label>
          <NumericInput
            id="vehicleDepreciation"
            value={formData.vehicleDepreciation || 0}
            onChange={(value) => setFormData(prev => ({ ...prev, vehicleDepreciation: value || 0 }))}
            currency={formData.currency}
            showCurrency
            disabled={isSubmitting}
            className={isMobile ? 'h-8 text-sm' : ''}
            autoComplete="off"
          />
        </div>
      </CardContent>
    </Card>
  );
}
