import { useEffect } from 'react';

interface ServiceWorkerManagerProps {
  children?: React.ReactNode;
}

export const ServiceWorkerManager: React.FC<ServiceWorkerManagerProps> = ({ children }) => {
  useEffect(() => {
    // Solo registrar en producción
    if (import.meta.env.PROD && 'serviceWorker' in navigator) {
      registerServiceWorker();
    }
  }, []);

  return <>{children}</>;
};

async function registerServiceWorker() {
  try {
    console.log('Registrando Service Worker...');
    
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    console.log('Service Worker registrado exitosamente:', registration);

    // Manejar actualizaciones del Service Worker
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // Hay una nueva versión disponible
            console.log('Nueva versión de la aplicación disponible');
            
            // Opcional: Mostrar notificación al usuario
            if (window.confirm('Hay una nueva versión disponible. ¿Deseas actualizar?')) {
              window.location.reload();
            }
          }
        });
      }
    });

    // Limpiar cache periódicamente
    setInterval(() => {
      if (registration.active) {
        registration.active.postMessage({ type: 'CLEAN_CACHE' });
      }
    }, 30 * 60 * 1000); // Cada 30 minutos

    // Preload de recursos críticos después del registro
    setTimeout(() => {
      preloadCriticalResources(registration);
    }, 2000);

  } catch (error) {
    console.error('Error registrando Service Worker:', error);
  }
}

function preloadCriticalResources(registration: ServiceWorkerRegistration) {
  if (!registration.active) return;

  // Lista de recursos críticos para preload
  const criticalResources = [
    '/js/react-core-',
    '/css/',
    // Agregar más recursos según sea necesario
  ];

  registration.active.postMessage({
    type: 'PRELOAD_RESOURCES',
    resources: criticalResources,
  });
}


