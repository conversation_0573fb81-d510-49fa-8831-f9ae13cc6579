
import React, { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/components/ui/numeric-input';
import { TrendingUp, TrendingDown, Calendar, AlertTriangle, CheckCircle } from 'lucide-react';
import { PaymentItem } from '../types/paymentTypes';
import { format, subMonths, eachMonthOfInterval } from 'date-fns';
import { es } from 'date-fns/locale';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface PaymentTrendsAnalysisProps {
  payments: PaymentItem[];
  className?: string;
}

interface MonthlyTrend {
  month: string;
  totalAmount: number;
  totalPayments: number;
  paidAmount: number;
  paidCount: number;
  overdueAmount: number;
  overdueCount: number;
  completionRate: number;
}

interface TrendInsight {
  type: 'positive' | 'negative' | 'neutral';
  title: string;
  description: string;
  value: string;
  icon: React.ReactNode;
}

export const PaymentTrendsAnalysis: React.FC<PaymentTrendsAnalysisProps> = ({
  payments,
  className
}) => {
  const { isMobile } = useBreakpoint();
  // Calcular tendencias mensuales
  const monthlyTrends = useMemo((): MonthlyTrend[] => {
    const endDate = new Date();
    const startDate = subMonths(endDate, 11); // Últimos 12 meses
    const months = eachMonthOfInterval({ start: startDate, end: endDate });

    return months.map(month => {
      const monthKey = format(month, 'yyyy-MM');
      
      const monthPayments = payments.filter(payment => {
        const paymentMonth = format(new Date(payment.dueDate), 'yyyy-MM');
        return paymentMonth === monthKey;
      });

      const paidPayments = monthPayments.filter(p => p.status === 'paid');
      const overduePayments = monthPayments.filter(p => p.status === 'overdue');

      const totalAmount = monthPayments.reduce((sum, p) => sum + p.amount, 0);
      const paidAmount = paidPayments.reduce((sum, p) => sum + p.amount, 0);
      const overdueAmount = overduePayments.reduce((sum, p) => sum + p.amount, 0);

      const completionRate = monthPayments.length > 0 
        ? (paidPayments.length / monthPayments.length) * 100 
        : 0;

      return {
        month: format(month, 'MMM yyyy', { locale: es }),
        totalAmount,
        totalPayments: monthPayments.length,
        paidAmount,
        paidCount: paidPayments.length,
        overdueAmount,
        overdueCount: overduePayments.length,
        completionRate: Math.round(completionRate)
      };
    });
  }, [payments]);

  // Generar insights
  const insights = useMemo((): TrendInsight[] => {
    const insights: TrendInsight[] = [];
    
    if (monthlyTrends.length < 2) return insights;

    const currentMonth = monthlyTrends[monthlyTrends.length - 1];
    const previousMonth = monthlyTrends[monthlyTrends.length - 2];
    const threeMonthsAgo = monthlyTrends[monthlyTrends.length - 4];

    // Tendencia de monto total
    const amountChange = ((currentMonth.totalAmount - previousMonth.totalAmount) / previousMonth.totalAmount) * 100;
    if (Math.abs(amountChange) > 5) {
      insights.push({
        type: amountChange > 0 ? 'negative' : 'positive',
        title: amountChange > 0 ? 'Aumento en Pagos' : 'Reducción en Pagos',
        description: `Los pagos ${amountChange > 0 ? 'aumentaron' : 'disminuyeron'} ${Math.abs(amountChange).toFixed(1)}% vs el mes anterior`,
        value: `${amountChange > 0 ? '+' : ''}${amountChange.toFixed(1)}%`,
        icon: amountChange > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />
      });
    }

    // Tendencia de tasa de completitud
    const completionChange = currentMonth.completionRate - previousMonth.completionRate;
    if (Math.abs(completionChange) > 5) {
      insights.push({
        type: completionChange > 0 ? 'positive' : 'negative',
        title: completionChange > 0 ? 'Mejor Cumplimiento' : 'Menor Cumplimiento',
        description: `La tasa de pagos completados ${completionChange > 0 ? 'mejoró' : 'empeoró'} ${Math.abs(completionChange).toFixed(1)} puntos`,
        value: `${currentMonth.completionRate}%`,
        icon: completionChange > 0 ? <CheckCircle className="w-4 h-4" /> : <AlertTriangle className="w-4 h-4" />
      });
    }

    // Tendencia de pagos vencidos
    if (currentMonth.overdueCount > previousMonth.overdueCount) {
      insights.push({
        type: 'negative',
        title: 'Aumento de Vencidos',
        description: `Los pagos vencidos aumentaron de ${previousMonth.overdueCount} a ${currentMonth.overdueCount}`,
        value: `+${currentMonth.overdueCount - previousMonth.overdueCount}`,
        icon: <AlertTriangle className="w-4 h-4" />
      });
    }

    // Tendencia trimestral
    if (threeMonthsAgo) {
      const avgLast3Months = (currentMonth.totalAmount + previousMonth.totalAmount + monthlyTrends[monthlyTrends.length - 3].totalAmount) / 3;
      const avgPrevious3Months = monthlyTrends.slice(-6, -3).reduce((sum, m) => sum + m.totalAmount, 0) / 3;
      
      if (avgPrevious3Months > 0) {
        const quarterlyChange = ((avgLast3Months - avgPrevious3Months) / avgPrevious3Months) * 100;
        if (Math.abs(quarterlyChange) > 10) {
          insights.push({
            type: quarterlyChange > 0 ? 'neutral' : 'positive',
            title: 'Tendencia Trimestral',
            description: `Promedio trimestral ${quarterlyChange > 0 ? 'aumentó' : 'disminuyó'} ${Math.abs(quarterlyChange).toFixed(1)}%`,
            value: formatCurrency(avgLast3Months),
            icon: <Calendar className="w-4 h-4" />
          });
        }
      }
    }

    return insights.slice(0, 4); // Máximo 4 insights
  }, [monthlyTrends]);

  const getInsightColor = (type: TrendInsight['type']) => {
    switch (type) {
      case 'positive': return 'border-green-200 bg-green-50';
      case 'negative': return 'border-red-200 bg-red-50';
      default: return 'border-blue-200 bg-blue-50';
    }
  };

  const getInsightIconColor = (type: TrendInsight['type']) => {
    switch (type) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      default: return 'text-blue-600';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Insights principales */}
      {insights.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {insights.map((insight, index) => (
            <Card key={index} className={`${getInsightColor(insight.type)} border`}>
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={getInsightIconColor(insight.type)}>
                        {insight.icon}
                      </span>
                      <h4 className="font-medium text-sm">{insight.title}</h4>
                    </div>
                    <p className="text-xs text-finanz-text-secondary mb-2">
                      {insight.description}
                    </p>
                    <Badge variant="outline" className="text-xs">
                      {insight.value}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Gráfico de tendencias */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Tendencia de Pagos (12 meses)</CardTitle>
        </CardHeader>
        <CardContent>
          <div style={{ width: '100%', height: isMobile ? 200 : 256 }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  fontSize={12}
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  fontSize={12}
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
                />
                <Tooltip 
                  formatter={(value, name) => [
                    formatCurrency(Number(value)),
                    name === 'totalAmount' ? 'Total' : 
                    name === 'paidAmount' ? 'Pagado' : 'Vencido'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="totalAmount" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name="Total"
                />
                <Line 
                  type="monotone" 
                  dataKey="paidAmount" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  name="Pagado"
                />
                <Line 
                  type="monotone" 
                  dataKey="overdueAmount" 
                  stroke="#ef4444" 
                  strokeWidth={2}
                  name="Vencido"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Gráfico de tasa de completitud */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Tasa de Completitud de Pagos</CardTitle>
        </CardHeader>
        <CardContent>
          <div style={{ width: '100%', height: isMobile ? 160 : 192 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  fontSize={12}
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  fontSize={12}
                  tick={{ fontSize: 12 }}
                  domain={[0, 100]}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip 
                  formatter={(value) => [`${value}%`, 'Tasa de Completitud']}
                />
                <Bar 
                  dataKey="completionRate" 
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
