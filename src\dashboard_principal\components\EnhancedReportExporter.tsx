
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card'; // Removed CardHeader, CardTitle
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Download, 
  FileText, 
  Table, 
  BarChart3, 
  Calendar,
  Settings,
  CheckCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useDashboardData } from '../hooks/useDashboardData';
import { useOptimizedCalculations } from '../hooks/useOptimizedCalculations';
import { formatCurrency } from '@/components/ui/numeric-input';

type ExportFormat = 'pdf' | 'excel' | 'csv' | 'json';
type ReportType = 'summary' | 'detailed' | 'comparison' | 'custom';

interface ExportOptions {
  format: ExportFormat;
  reportType: ReportType;
  includeSections: {
    metrics: boolean;
    charts: boolean;
    alerts: boolean;
    recommendations: boolean;
    comparison: boolean;
  };
  dateRange: 'current' | 'last3months' | 'last6months' | 'lastyear';
}

// Define types for the report data structure
interface ReportMetadata {
  title: string;
  generatedAt: string;
  period?: string;
  format: ExportFormat;
}

interface ReportMetrics {
  netIncome: number;
  totalMonthlyPayments: number;
  netBalance: number;
  totalDebt: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
  financialHealthScore: number;
}

interface ReportComparisons {
  netIncome: { current: number; previous: number; change: number };
  expenses: { current: number; previous: number; change: number };
  savings: { current: number; previous: number; change: number };
}

interface ReportAnalysis {
  efficiency: number;
  liquidityRatio: number;
  debtServiceRatio: number;
  emergencyFundAdequacy: string;
}

interface ReportDataType {
  metadata: ReportMetadata;
  metrics: ReportMetrics;
  comparisons: ReportComparisons;
  analysis: ReportAnalysis;
}

export const EnhancedReportExporter: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    reportType: 'summary',
    includeSections: {
      metrics: true,
      charts: true,
      alerts: true,
      recommendations: true,
      comparison: false
    },
    dateRange: 'current'
  });

  const { toast } = useToast();
  const dashboardData = useDashboardData();
  const optimizedMetrics = useOptimizedCalculations();

  const formatOptions = [
    { value: 'pdf', label: 'PDF', icon: FileText, description: 'Documento profesional con gráficos' },
    { value: 'excel', label: 'Excel', icon: Table, description: 'Hoja de cálculo con datos editables' },
    { value: 'csv', label: 'CSV', icon: Table, description: 'Datos tabulares para análisis' },
    { value: 'json', label: 'JSON', icon: Settings, description: 'Datos estructurados para desarrolladores' }
  ];

  const reportTypes = [
    { value: 'summary', label: 'Resumen Ejecutivo', description: 'Métricas clave y tendencias principales' },
    { value: 'detailed', label: 'Reporte Detallado', description: 'Análisis completo con todos los datos' },
    { value: 'comparison', label: 'Análisis Comparativo', description: 'Comparaciones temporales detalladas' },
    { value: 'custom', label: 'Personalizado', description: 'Selecciona qué secciones incluir' }
  ];

  const dateRangeOptions = [
    { value: 'current', label: 'Mes Actual' },
    { value: 'last3months', label: 'Últimos 3 Meses' },
    { value: 'last6months', label: 'Últimos 6 Meses' },
    { value: 'lastyear', label: 'Último Año' }
  ];

  const generateReportData = () => {
    const currentDate = new Date();
    const reportData = {
      metadata: {
        title: `Reporte Financiero - ${reportTypes.find(r => r.value === exportOptions.reportType)?.label}`,
        generatedAt: currentDate.toISOString(),
        period: dateRangeOptions.find(d => d.value === exportOptions.dateRange)?.label,
        format: exportOptions.format
      },
      metrics: {
        netIncome: dashboardData.netIncome,
        totalMonthlyPayments: dashboardData.totalMonthlyPayments,
        netBalance: dashboardData.netBalance,
        totalDebt: dashboardData.totalDebt,
        savingsRate: dashboardData.savingsRate,
        debtToIncomeRatio: dashboardData.debtToIncomeRatio,
        emergencyFundMonths: dashboardData.emergencyFundMonths,
        financialHealthScore: optimizedMetrics.financialHealthScore
      },
      comparisons: {
        netIncome: optimizedMetrics.netIncomeComparison,
        expenses: optimizedMetrics.expensesComparison,
        savings: optimizedMetrics.savingsComparison
      },
      analysis: {
        efficiency: optimizedMetrics.efficiency,
        liquidityRatio: optimizedMetrics.liquidityRatio,
        debtServiceRatio: optimizedMetrics.debtServiceRatio,
        emergencyFundAdequacy: optimizedMetrics.emergencyFundAdequacy
      }
    };

    return reportData as ReportDataType; // Cast to ensure type correctness
  };

  const exportToPDF = async (data: ReportDataType) => {
    // Simular exportación PDF
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const element = document.createElement('a');
    const file = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    element.href = URL.createObjectURL(file);
    element.download = `reporte-financiero-${new Date().toISOString().slice(0, 10)}.pdf`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const exportToExcel = async (data: ReportDataType) => {
    // Simular exportación Excel
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const csvContent = [
      ['Métrica', 'Valor'],
      ['Ingreso Neto', formatCurrency(data.metrics.netIncome)],
      ['Pagos Mensuales', formatCurrency(data.metrics.totalMonthlyPayments)],
      ['Balance Neto', formatCurrency(data.metrics.netBalance)],
      ['Deuda Total', formatCurrency(data.metrics.totalDebt)],
      ['Tasa de Ahorro', `${data.metrics.savingsRate.toFixed(1)}%`],
      ['Ratio Deuda/Ingreso', `${data.metrics.debtToIncomeRatio.toFixed(1)}%`],
      ['Score Salud Financiera', `${data.metrics.financialHealthScore.toFixed(0)}/100`]
    ].map(row => row.join(',')).join('\n');

    const element = document.createElement('a');
    const file = new Blob([csvContent], { type: 'text/csv' });
    element.href = URL.createObjectURL(file);
    element.download = `reporte-financiero-${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const exportToCSV = async (data: ReportDataType) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const csvContent = [
      ['Métrica', 'Valor', 'Tipo'],
      ['Ingreso Neto', data.metrics.netIncome, 'Moneda'],
      ['Pagos Mensuales', data.metrics.totalMonthlyPayments, 'Moneda'],
      ['Balance Neto', data.metrics.netBalance, 'Moneda'],
      ['Deuda Total', data.metrics.totalDebt, 'Moneda'],
      ['Tasa de Ahorro', data.metrics.savingsRate, 'Porcentaje'],
      ['Ratio Deuda/Ingreso', data.metrics.debtToIncomeRatio, 'Porcentaje'],
      ['Score Salud Financiera', data.metrics.financialHealthScore, 'Score']
    ].map(row => row.join(',')).join('\n');

    const element = document.createElement('a');
    const file = new Blob([csvContent], { type: 'text/csv' });
    element.href = URL.createObjectURL(file);
    element.download = `reporte-financiero-${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const exportToJSON = async (data: ReportDataType) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const element = document.createElement('a');
    const file = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    element.href = URL.createObjectURL(file);
    element.download = `reporte-financiero-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      const reportData = generateReportData();
      
      switch (exportOptions.format) {
        case 'pdf':
          await exportToPDF(reportData);
          break;
        case 'excel':
          await exportToExcel(reportData);
          break;
        case 'csv':
          await exportToCSV(reportData);
          break;
        case 'json':
          await exportToJSON(reportData);
          break;
      }

      toast({
        title: "Reporte Exportado",
        description: `Tu reporte en formato ${exportOptions.format.toUpperCase()} se ha descargado exitosamente.`,
      });

      setIsOpen(false);
    } catch {
      toast({
        title: "Error al Exportar",
        description: "Hubo un problema al generar el reporte. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const updateIncludeSection = (section: keyof ExportOptions['includeSections'], value: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      includeSections: {
        ...prev.includeSections,
        [section]: value
      }
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="w-4 h-4" />
          Exportar Reporte
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Exportar Reporte Financiero
          </DialogTitle>
          <DialogDescription>
            Configura las opciones de exportación para tu reporte personalizado
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Formato de Exportación */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Formato de Exportación</h4>
            <div className="grid grid-cols-2 gap-3">
              {formatOptions.map((format) => (
                <Card 
                  key={format.value}
                  className={`cursor-pointer transition-colors ${
                    exportOptions.format === format.value ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as ExportFormat }))}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <format.icon className="w-4 h-4" />
                      <span className="font-medium text-sm">{format.label}</span>
                      {exportOptions.format === format.value && (
                        <Badge variant="default" className="text-xs">Seleccionado</Badge>
                      )}
                    </div>
                    <p className="text-xs text-gray-600">{format.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Tipo de Reporte */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Tipo de Reporte</h4>
            <Select 
              value={exportOptions.reportType} 
              onValueChange={(value: ReportType) => 
                setExportOptions(prev => ({ ...prev, reportType: value }))
              }
            >
              <SelectTrigger id="report-type-select" autoComplete="off">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div>
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-gray-500">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Rango de Fechas */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Período del Reporte</h4>
            <Select 
              value={exportOptions.dateRange} 
              onValueChange={(value: ExportOptions['dateRange']) => 
                setExportOptions(prev => ({ ...prev, dateRange: value }))
              }
            >
              <SelectTrigger id="date-range-select" autoComplete="off">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dateRangeOptions.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Secciones a Incluir */}
          {exportOptions.reportType === 'custom' && (
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Secciones a Incluir</h4>
              <div className="space-y-2">
                {[
                  { key: 'metrics', label: 'Métricas Principales', icon: BarChart3 },
                  { key: 'charts', label: 'Gráficos y Visualizaciones', icon: BarChart3 },
                  { key: 'alerts', label: 'Alertas y Advertencias', icon: CheckCircle },
                  { key: 'recommendations', label: 'Recomendaciones', icon: CheckCircle },
                  { key: 'comparison', label: 'Comparación Temporal', icon: Calendar }
                ].map((section) => (
                  <div key={section.key} className="flex items-center space-x-2">
                    <Checkbox
                      id={section.key}
                      checked={exportOptions.includeSections[section.key as keyof ExportOptions['includeSections']]}
                      onCheckedChange={(checked) => 
                        updateIncludeSection(section.key as keyof ExportOptions['includeSections'], checked as boolean)
                      }
                    />
                    <div className="flex items-center gap-2">
                      <section.icon className="w-4 h-4 text-gray-500" />
                      <label htmlFor={section.key} className="text-sm cursor-pointer">
                        {section.label}
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Botones de Acción */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Exportando...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Download className="w-4 h-4" />
                  Exportar Reporte
                </div>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
