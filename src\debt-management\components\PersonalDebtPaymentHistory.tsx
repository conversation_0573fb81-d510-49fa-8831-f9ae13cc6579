
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Trash2, Edit } from 'lucide-react';
import { PersonalDebt, PersonalDebtPayment } from '@/types';
import { formatCurrency } from '@/components/ui/numeric-input';

interface PersonalDebtPaymentHistoryProps {
  debt: PersonalDebt;
  payments: PersonalDebtPayment[];
  onEditPayment?: (payment: PersonalDebtPayment) => void;
  onDeletePayment?: (paymentId: string) => void;
}

export function PersonalDebtPaymentHistory({ 
  debt, 
  payments, 
  onEditPayment, 
  onDeletePayment 
}: PersonalDebtPaymentHistoryProps) {
  const debtPayments = payments.filter(payment => payment.personalDebtId === debt.id);
  
  // Calcular totales
  const totalPaid = debtPayments.reduce((sum, payment) => sum + payment.totalAmount, 0);
  const totalInterest = debtPayments.reduce((sum, payment) => sum + payment.interestAmount, 0);
  const totalPrincipal = debtPayments.reduce((sum, payment) => sum + payment.principalAmount, 0);
  const remainingBalance = debt.remainingBalance || debt.amount;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Abonos - {debt.name}</CardTitle>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-finanz-text-secondary">Monto Original:</span>
            <div className="font-semibold">{formatCurrency(debt.amount, debt.currency)}</div>
          </div>
          <div>
            <span className="text-finanz-text-secondary">Total Pagado:</span>
            <div className="font-semibold text-green-600">{formatCurrency(totalPaid, debt.currency)}</div>
          </div>
          <div>
            <span className="text-finanz-text-secondary">Saldo Restante:</span>
            <div className="font-semibold text-finanz-danger">{formatCurrency(remainingBalance, debt.currency)}</div>
          </div>
          <div>
            <span className="text-finanz-text-secondary">Progreso:</span>
            <div className="font-semibold">{((debt.amount - remainingBalance) / debt.amount * 100).toFixed(1)}%</div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {debtPayments.length === 0 ? (
          <div className="text-center py-8 text-finanz-text-secondary">
            No hay abonos registrados para esta deuda.
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-sm border-b pb-2">
              <div>
                <span className="text-finanz-text-secondary">Total en Intereses:</span>
                <div className="font-semibold text-finanz-warning">{formatCurrency(totalInterest, debt.currency)}</div>
              </div>
              <div>
                <span className="text-finanz-text-secondary">Total en Capital:</span>
                <div className="font-semibold text-finanz-primary">{formatCurrency(totalPrincipal, debt.currency)}</div>
              </div>
              <div>
                <span className="text-finanz-text-secondary">Número de Pagos:</span>
                <div className="font-semibold">{debtPayments.length}</div>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fecha</TableHead>
                    <TableHead>Total Pagado</TableHead>
                    <TableHead>Interés</TableHead>
                    <TableHead>Capital</TableHead>
                    <TableHead>Saldo Restante</TableHead>
                    <TableHead>Notas</TableHead>
                    <TableHead className="w-[100px]">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {debtPayments
                    .sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime())
                    .map((payment, index) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        {new Date(payment.paymentDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="font-semibold">
                        {formatCurrency(payment.totalAmount, debt.currency)}
                      </TableCell>
                      <TableCell className="text-finanz-warning">
                        {formatCurrency(payment.interestAmount, debt.currency)}
                      </TableCell>
                      <TableCell className="text-finanz-primary">
                        {formatCurrency(payment.principalAmount, debt.currency)}
                      </TableCell>
                      <TableCell className="text-finanz-danger">
                        {formatCurrency(payment.remainingBalance, debt.currency)}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {payment.notes || '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {onEditPayment && (
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => onEditPayment(payment)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          )}
                          {onDeletePayment && (
                            <Button
                              variant="destructive"
                              size="icon"
                              onClick={() => onDeletePayment(payment.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
