
import React from 'react';
import { ChevronLeft, ChevronRight, Calendar as CalendarIconLucide, Home } from 'lucide-react'; // Renamed Calendar to avoid conflict
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import type { CurrentPeriod } from '../hooks/useTemporalNavigation';
import { DateRangePicker } from '@/components/ui/date-range-picker'; // Import DateRangePicker
import type { DateRange } from 'react-day-picker'; // Import DateRange type

// Simplified TemporalPeriod for getAvailableMonths from the hook
interface AvailableMonthInfo {
  year: number;
  month: number;
  displayName: string;
  isCurrentMonth: boolean;
  isPastMonth: boolean;
  isFutureMonth: boolean;
}

interface TemporalNavigatorProps {
  currentPeriod: CurrentPeriod;
  selectedDateRange?: DateRange; // From useTemporalNavigation's selectedDateInput
  onDateRangeSelect: (dateRange?: DateRange) => void; // From useTemporalNavigation's setDateRange
  navigateToPreviousMonth: () => void;
  navigateToNextMonth: () => void;
  navigateToCurrentMonth: () => void;
  navigateToMonth: (year: number, month: number) => void;
  getAvailableMonths: () => AvailableMonthInfo[]; // Updated return type
}

export const TemporalNavigator = ({
  currentPeriod,
  selectedDateRange,
  onDateRangeSelect,
  navigateToPreviousMonth,
  navigateToNextMonth,
  navigateToCurrentMonth,
  navigateToMonth,
  getAvailableMonths,
}: TemporalNavigatorProps) => {
  const { isMobile } = useBreakpoint();
  const availableMonths = getAvailableMonths();

  const selectValue = currentPeriod.displayMode === 'month' ? `${currentPeriod.year}-${currentPeriod.month}` : "";

  return (
    <div className={`flex items-center justify-between gap-2 ${isMobile ? 'flex-col space-y-2' : 'flex-wrap'}`}>
      {/* Navegación anterior/siguiente y Mes Actual */}
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size={isMobile ? 'sm' : 'default'}
          onClick={navigateToPreviousMonth}
          className="flex items-center gap-1"
          aria-label="Mes anterior"
        >
          <ChevronLeft className="w-4 h-4" />
          {!isMobile && 'Anterior'}
        </Button>
        <Button
          variant="outline"
          size={isMobile ? 'sm' : 'default'}
          onClick={navigateToNextMonth}
          className="flex items-center gap-1"
          aria-label="Mes siguiente"
        >
          {!isMobile && 'Siguiente'}
          <ChevronRight className="w-4 h-4" />
        </Button>
        {!currentPeriod.isCurrentMonth && currentPeriod.displayMode === 'month' && (
          <Button
            variant="default"
            size={isMobile ? 'sm' : 'default'}
            onClick={navigateToCurrentMonth}
            className="flex items-center gap-1"
            aria-label="Volver al mes actual"
          >
            <Home className="w-4 h-4" />
            {!isMobile && 'Mes Actual'}
          </Button>
        )}
      </div>

      {/* Selector de Mes y Rango de Fechas */}
      <div className={`flex items-center gap-2 ${isMobile ? 'w-full justify-around' : ''}`}>
        <div className="flex items-center gap-1">
          <CalendarIconLucide className="w-4 h-4 text-finanz-text-secondary" />
          <Select
            value={selectValue}
            onValueChange={(value) => {
              if (value) { // Ensure value is not empty (e.g. when a range is selected)
                const [year, month] = value.split('-').map(Number);
                navigateToMonth(year, month);
              }
            }}
          >
            <SelectTrigger id="month-select" className={`${isMobile ? 'w-36' : 'w-44'}`} aria-label="Seleccionar mes" autoComplete="off"> {/* Añadir id y autocomplete */}
              <SelectValue placeholder="Seleccionar mes..." />
            </SelectTrigger>
            <SelectContent>
              {availableMonths.map((monthInfo) => (
                <SelectItem
                  key={`${monthInfo.year}-${monthInfo.month}`}
                  value={`${monthInfo.year}-${monthInfo.month}`}
                  className={`${monthInfo.isCurrentMonth ? 'font-semibold bg-finanz-primary/10' : ''}`}
                >
                  <div className="flex items-center justify-between w-full">
                    <span>{monthInfo.displayName}</span>
                    {monthInfo.isCurrentMonth && (
                      <span className="text-xs text-finanz-primary ml-2">Actual</span>
                    )}
                    {/* We might not need isFutureMonth indicator here if projections are handled elsewhere */}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <DateRangePicker
          date={selectedDateRange}
          onSelect={onDateRangeSelect}
          placeholder="Rango personalizado"
          className={`${isMobile ? 'w-44' : 'w-60'}`} // Adjust width as needed
          id="payments-date-range-picker"
          autoComplete="off" // Añadir autocomplete
        />
      </div>
    </div>
  );
};
