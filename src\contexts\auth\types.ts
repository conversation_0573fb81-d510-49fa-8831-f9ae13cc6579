
import { User, Session } from '@supabase/supabase-js';

export interface Profile {
  id: string;
  email: string | null;
  full_name: string | null;
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
}

export interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: Profile | null;
  loading: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  isSessionValid: boolean;
  sessionTimeLeft: number;
  refreshSession: () => Promise<void>;
  lockScreen: () => void;
  isLocked: boolean;
  unlockScreen: (password: string) => Promise<boolean>;
}
