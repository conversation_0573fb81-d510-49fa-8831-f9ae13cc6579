
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus } from 'lucide-react';
import { NumericInput, formatCurrency } from '@/components/ui/numeric-input';
import { Income as IncomeType } from '@/types';

interface OtherIncomeSectionProps {
  formData: Partial<IncomeType>;
  setFormData: React.Dispatch<React.SetStateAction<Partial<IncomeType>>>;
  defaultCurrency: 'DOP' | 'USD';
  isSubmitting?: boolean;
}

export function OtherIncomeSection({ formData, setFormData, defaultCurrency, isSubmitting = false }: OtherIncomeSectionProps) {
  const [newOtherIncome, setNewOtherIncome] = useState({
    description: '',
    amount: 0,
    currency: defaultCurrency as 'DOP' | 'USD'
  });

  const addOtherIncome = () => {
    if (!newOtherIncome.description || !newOtherIncome.amount) return;
    
    const newItem = {
      id: Date.now().toString(),
      ...newOtherIncome
    };
    
    setFormData(prev => ({
      ...prev,
      otherIncomeItems: [...(prev.otherIncomeItems || []), newItem]
    }));
    
    setNewOtherIncome({
      description: '',
      amount: 0,
      currency: defaultCurrency as 'DOP' | 'USD'
    });
  };

  const removeOtherIncome = (id: string) => {
    setFormData(prev => ({
      ...prev,
      otherIncomeItems: (prev.otherIncomeItems || []).filter(item => item.id !== id)
    }));
  };

  const otherIncomeTotal = (formData.otherIncomeItems || []).reduce((total, item) => {
    const amountInCurrency = item.currency === formData.currency ? item.amount : 
      (item.currency === 'USD' && formData.currency === 'DOP') ? item.amount * 59 : 
      (item.currency === 'DOP' && formData.currency === 'USD') ? item.amount / 59 : item.amount;
    return total + amountInCurrency;
  }, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg text-finanz-success">OTROS INGRESOS</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="other-income-description">Nombre del ingreso adicional</Label>
            <Input
              id="other-income-description"
              value={newOtherIncome.description}
              onChange={(e) => setNewOtherIncome(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Ej. Freelance, Bonos..."
              disabled={isSubmitting}
              autoComplete="off"
            />
          </div>
          <div>
            <Label htmlFor="other-income-amount">Monto</Label>
            <NumericInput
              id="other-income-amount"
              value={newOtherIncome.amount}
              onChange={(value) => setNewOtherIncome(prev => ({ ...prev, amount: value || 0 }))}
              currency={newOtherIncome.currency}
              showCurrency
              disabled={isSubmitting}
              autoComplete="off"
            />
          </div>
          <div className="flex items-end">
            <Button 
              type="button" 
              onClick={addOtherIncome}
              className="bg-finanz-success hover:bg-finanz-success/90"
              disabled={isSubmitting}
            >
              <Plus className="w-4 h-4 mr-2" />
              Agregar
            </Button>
          </div>
        </div>

        {formData.otherIncomeItems && formData.otherIncomeItems.length > 0 ? (
          <div className="space-y-2">
            {formData.otherIncomeItems.map(item => (
              <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg">
                <div>
                  <span className="font-medium">{item.description}</span>
                  <span className="ml-2 text-finanz-success">
                    {formatCurrency(item.amount, item.currency)}
                  </span>
                </div>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={() => removeOtherIncome(item.id)}
                  disabled={isSubmitting}
                >
                  Eliminar
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-4">
            <p>No hay ingresos adicionales registrados</p>
            <p className="text-sm">Agrega nuevos ingresos usando el campo superior</p>
          </div>
        )}

        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <span className="font-medium">Total Otros Ingresos</span>
            <span className="text-lg font-semibold text-finanz-success">
              {formatCurrency(otherIncomeTotal, formData.currency)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
