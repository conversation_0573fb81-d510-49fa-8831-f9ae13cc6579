
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { NumericInput } from '@/components/ui/numeric-input';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Target } from 'lucide-react';
import { FinancialGoal } from '@/types';
import { formatCurrency } from '@/components/ui/numeric-input';

interface AddContributionModalProps {
  goal: FinancialGoal;
  isOpen: boolean;
  onClose: () => void;
  onAddContribution: (goalId: string, amount: number, note?: string) => void;
}

export function AddContributionModal({ goal, isOpen, onClose, onAddContribution }: AddContributionModalProps) {
  const [amount, setAmount] = useState<number | null>(null);
  const [note, setNote] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (amount && amount > 0) {
      onAddContribution(goal.id, amount, note || undefined);
      setAmount(null);
      setNote('');
      onClose();
    }
  };

  const remainingAmount = goal.amount - goal.currentAmount;
  const progressPercentage = (goal.currentAmount / goal.amount) * 100;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-finanz-primary" />
            Agregar Avance
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Goal Info */}
          <div className="bg-finanz-background rounded-lg p-4 space-y-2">
            <h4 className="font-semibold text-finanz-text-primary">{goal.name}</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-finanz-text-secondary">Actual:</span>
                <div className="font-semibold text-finanz-primary">
                  {formatCurrency(goal.currentAmount, goal.currency)}
                </div>
              </div>
              <div>
                <span className="text-finanz-text-secondary">Meta:</span>
                <div className="font-semibold">
                  {formatCurrency(goal.amount, goal.currency)}
                </div>
              </div>
              <div>
                <span className="text-finanz-text-secondary">Restante:</span>
                <div className="font-semibold text-finanz-warning">
                  {formatCurrency(remainingAmount, goal.currency)}
                </div>
              </div>
              <div>
                <span className="text-finanz-text-secondary">Progreso:</span>
                <div className="font-semibold text-finanz-success">
                  {progressPercentage.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Monto de la Contribución *</Label>
              <NumericInput
                id="amount"
                value={amount}
                onChange={setAmount}
                currency={goal.currency}
                showCurrency={true}
                placeholder="0.00"
                className="w-full"
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="note">Nota (opcional)</Label>
              <Textarea
                id="note"
                name="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                placeholder="Ej: Ahorro del mes de enero, bono extra, etc."
                rows={3}
                autoComplete="off"
              />
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button 
                type="submit" 
                disabled={!amount || amount <= 0}
                className="gap-2"
              >
                <Plus className="w-4 h-4" />
                Agregar Contribución
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
