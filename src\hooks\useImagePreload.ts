import { useEffect } from 'react';

// Hook para preload de imágenes críticas
export const useImagePreload = (src: string, priority = false) => {
  useEffect(() => {
    if (!priority) return;

    const img = new Image();
    img.src = src;
    
    // Preload también versiones responsive
    const sizes = [640, 768, 1024];
    sizes.forEach(size => {
      const responsiveImg = new Image();
      responsiveImg.src = `${src}?w=${size}&q=75`;
    });
  }, [src, priority]);
};
