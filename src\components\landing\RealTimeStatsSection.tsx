
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Server, 
  Star, 
  Clock,
  Zap,
  Shield,
  CheckCircle,
  BarChart3,
  Globe,
  Activity
} from 'lucide-react';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface PublicStatistics {
  total_registered_users: number;
  uptime_percentage: number;
  rating: number;
  support_hours: string;
  app_features: number;
  satisfaction_score: number;
  data_security_level: string;
  api_response_time: number;
  daily_active_sessions: number;
  app_version: string;
  server_locations: number;
  security_updates: number;
}

interface RealTimeStatsSectionProps {
  statistics: PublicStatistics | null;
  loading: boolean;
}

export function RealTimeStatsSection({ statistics, loading }: RealTimeStatsSectionProps) {
  const { isMobile, isTablet } = useBreakpoint();
  
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const appStats = [
    {
      title: "Funcionalidades",
      value: loading ? null : statistics?.app_features || 15,
      icon: BarChart3,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: "Herramientas disponibles"
    },
    {
      title: "Sesiones Diarias",
      value: loading ? null : formatNumber(statistics?.daily_active_sessions || 280),
      icon: Activity,
      color: "text-green-600",
      bgColor: "bg-green-50",
      description: "Usuarios activos"
    },
    {
      title: "Satisfacción",
      value: loading ? null : `${statistics?.satisfaction_score || 97}%`,
      icon: Star,
      color: "text-amber-600",
      bgColor: "bg-amber-50",
      description: "Nivel de satisfacción"
    },
    {
      title: "Actualizaciones",
      value: loading ? null : statistics?.security_updates || 24,
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: "Updates este año"
    }
  ];

  return (
    <section className={`
      bg-gradient-to-br from-gray-50 to-white relative overflow-hidden
      ${isMobile ? 'py-12' : isTablet ? 'py-14' : 'py-16'}
    `}>
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-1/4 w-64 h-64 bg-gradient-to-br from-blue-100/40 to-purple-100/40 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-1/4 w-64 h-64 bg-gradient-to-br from-emerald-100/40 to-blue-100/40 rounded-full blur-3xl"></div>
      </div>

      <div className={`
        container mx-auto relative z-10
        ${isMobile ? 'px-4' : 'px-4'}
      `}>
        <div className="text-center mb-8 md:mb-12">
          <div className={`
            inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full border border-blue-200/50 mb-3
            ${isMobile ? 'px-3 py-1 mb-2' : 'px-5 py-2 mb-4'}
          `}>
            <Server className="w-4 h-4 text-blue-600" />
            <span className={`
              font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent
              ${isMobile ? 'text-xs' : 'text-sm'}
            `}>
              Estadísticas en Tiempo Real
            </span>
          </div>

          <h2 className={`
            font-bold text-gray-900 mb-3
            ${isMobile ? 'text-2xl mb-2' : isTablet ? 'text-3xl mb-3' : 'text-3xl md:text-4xl mb-4'}
          `}>
            Rendimiento{' '}
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent">
              Excepcional
            </span>
          </h2>
          <p className={`
            text-gray-600 max-w-2xl mx-auto
            ${isMobile ? 'text-sm' : isTablet ? 'text-base' : 'text-lg'}
          `}>
            Datos actualizados cada minuto sobre el rendimiento y uso de nuestra plataforma
          </p>
        </div>

        <div className={`
          grid gap-3 mb-8
          ${isMobile ? 'grid-cols-2 gap-3 mb-6' : isTablet ? 'grid-cols-2 gap-4 mb-8' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-10'}
        `}>
          {appStats.map((stat) => (
            <Card key={stat.title} className="group border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white/80 backdrop-blur-sm">
              <CardHeader className={`
                ${isMobile ? 'pb-1' : 'pb-2'}
              `}>
                <div className="flex items-center justify-between">
                  <div className={`
                    rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300
                    ${stat.bgColor}
                    ${isMobile ? 'w-8 h-8' : 'w-10 h-10'}
                  `}>
                    <stat.icon className={`
                      ${stat.color}
                      ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                    `} />
                  </div>
                </div>
                <CardTitle className={`
                  font-medium text-gray-600
                  ${isMobile ? 'text-xs' : 'text-sm'}
                `}>
                  {stat.title}
                </CardTitle>
              </CardHeader>
              <CardContent className={`
                ${isMobile ? 'pt-0' : 'pt-0'}
              `}>
                <div className={`
                  font-bold text-gray-900 mb-1
                  ${isMobile ? 'text-lg' : 'text-xl'}
                `}>
                  {loading ? (
                    <Skeleton className={`
                      bg-gray-200
                      ${isMobile ? 'h-5 w-16' : 'h-6 w-20'}
                    `} />
                  ) : (
                    stat.value
                  )}
                </div>
                <p className={`
                  text-gray-500
                  ${isMobile ? 'text-xs' : 'text-xs'}
                `}>
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Summary card - Más compacto */}
        <div className="max-w-3xl mx-auto">
          <Card className="bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 border-0 text-white shadow-2xl">
            <CardContent className={`
              ${isMobile ? 'p-4' : 'p-6'}
            `}>
              <div className="text-center">
                <h3 className={`
                  font-bold mb-3
                  ${isMobile ? 'text-lg mb-3' : 'text-xl mb-4'}
                `}>
                  Plataforma de Confianza
                </h3>
                <div className={`
                  grid gap-3 mb-3
                  ${isMobile ? 'grid-cols-2 gap-3 mb-3' : 'grid-cols-2 md:grid-cols-3 gap-4 mb-4'}
                `}>
                  <div className="text-center">
                    <div className="flex justify-center mb-2">
                      <Globe className={`
                        ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                      `} />
                    </div>
                    <div className={`
                      font-black
                      ${isMobile ? 'text-base' : 'text-lg'}
                    `}>
                      {loading ? (
                        <Skeleton className={`
                          mx-auto bg-white/20
                          ${isMobile ? 'h-4 w-4' : 'h-5 w-6'}
                        `} />
                      ) : (
                        statistics?.server_locations || 3
                      )}
                    </div>
                    <div className={`
                      opacity-90
                      ${isMobile ? 'text-xs' : 'text-xs'}
                    `}>
                      Ubicaciones
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="flex justify-center mb-2">
                      <Zap className={`
                        ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                      `} />
                    </div>
                    <div className={`
                      font-black
                      ${isMobile ? 'text-base' : 'text-lg'}
                    `}>
                      {loading ? (
                        <Skeleton className={`
                          mx-auto bg-white/20
                          ${isMobile ? 'h-4 w-8' : 'h-5 w-10'}
                        `} />
                      ) : (
                        `${statistics?.api_response_time || 35}ms`
                      )}
                    </div>
                    <div className={`
                      opacity-90
                      ${isMobile ? 'text-xs' : 'text-xs'}
                    `}>
                      Respuesta
                    </div>
                  </div>
                  <div className={`
                    text-center
                    ${isMobile ? 'col-span-2' : ''}
                  `}>
                    <div className="flex justify-center mb-2">
                      <CheckCircle className={`
                        ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}
                      `} />
                    </div>
                    <div className={`
                      font-black
                      ${isMobile ? 'text-base' : 'text-lg'}
                    `}>
                      {loading ? (
                        <Skeleton className={`
                          mx-auto bg-white/20
                          ${isMobile ? 'h-4 w-10' : 'h-5 w-12'}
                        `} />
                      ) : (
                        `${statistics?.uptime_percentage || 99.9}%`
                      )}
                    </div>
                    <div className={`
                      opacity-90
                      ${isMobile ? 'text-xs' : 'text-xs'}
                    `}>
                      Disponibilidad
                    </div>
                  </div>
                </div>
                <div className={`
                  border-t border-white/20 pt-3
                  ${isMobile ? 'pt-3' : 'pt-4'}
                `}>
                  <div className={`
                    flex justify-center items-center opacity-90
                    ${isMobile ? 'flex-col space-y-2 text-xs' : 'flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-6 text-xs'}
                  `}>
                    <div className="flex items-center space-x-1">
                      <Shield className="w-3 h-3" />
                      <span>Seguridad {statistics?.data_security_level || 'Bancaria'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>Soporte {statistics?.support_hours || '24/7'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Server className="w-3 h-3" />
                      <span>Versión {statistics?.app_version || '2.1.5'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
