
export interface FinancialAlert {
  category: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
}

export interface AlertParameters {
  uncategorizedExpensePercentage: number;
  foodExpenseTrend: number;
  highInterestDebtAmount: number;
  totalMonthlyPayments: number;
  debtToIncomeRatio: number;
  savingsRate: number;
}

export const generateFinancialAlerts = (params: AlertParameters): FinancialAlert[] => {
  const alerts: FinancialAlert[] = [];

  if (params.uncategorizedExpensePercentage > 20) {
    alerts.push({
      category: 'Control de Gastos',
      message: `La categoría "Otros" representa el ${params.uncategorizedExpensePercentage}% del presupuesto.`,
      severity: 'medium',
      recommendation: 'Se recomienda categorizar estos gastos para mejor control.'
    });
  }

  if (params.foodExpenseTrend > 5) {
    alerts.push({
      category: 'Tendencia de Alimentación',
      message: `Incremento del ${params.foodExpenseTrend}% en gastos de alimentación en los últimos 3 meses.`,
      severity: 'low',
      recommendation: 'Monitorear para evitar desviaciones del presupuesto.'
    });
  }

  if (params.highInterestDebtAmount > 0) {
    const potentialSavings = params.highInterestDebtAmount * 0.12; // Asumiendo 12% de diferencia en tasas
    alerts.push({
      category: 'Optimización de Deudas',
      message: 'Oportunidad de consolidación de deudas de alto interés detectada.',
      severity: 'high',
      recommendation: `Consolidar deudas podría generar un ahorro de RD$${potentialSavings.toLocaleString()} anuales.`
    });
  }

  if (params.debtToIncomeRatio > 40) {
    alerts.push({
      category: 'Nivel de Endeudamiento',
      message: `Su ratio deuda-ingreso del ${params.debtToIncomeRatio}% excede el límite recomendado.`,
      severity: 'critical',
      recommendation: 'Priorice la reducción de deudas antes de nuevos compromisos financieros.'
    });
  }

  if (params.savingsRate < 10) {
    alerts.push({
      category: 'Capacidad de Ahorro',
      message: `Su tasa de ahorro actual del ${params.savingsRate}% está por debajo del mínimo recomendado.`,
      severity: 'high',
      recommendation: 'Establezca un plan de ahorro automático para mejorar su seguridad financiera.'
    });
  }

  return alerts.sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    return severityOrder[b.severity] - severityOrder[a.severity];
  });
};
