
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Income as IncomeType } from '@/types';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface SummarySectionProps {
  formData: Partial<IncomeType>;
  calculations: any;
}

export function SummarySection({ formData, calculations }: SummarySectionProps) {
  const { isMobile } = useBreakpoint();
  
  return (
    <Card>
      <CardHeader className={isMobile ? 'pb-2' : ''}>
        <CardTitle className={`
          text-finanz-primary
          ${isMobile ? 'text-base' : 'text-lg'}
        `}>
          RESUMEN FINAL
        </CardTitle>
      </CardHeader>
      <CardContent className={`
        ${isMobile ? 'space-y-2 pt-0' : 'space-y-3'}
      `}>
        <div className="flex justify-between items-center">
          <span className={`
            font-medium
            ${isMobile ? 'text-sm' : 'text-base'}
          `}>
            Ingreso Bruto
          </span>
          <span className={`
            font-bold text-finanz-primary
            ${isMobile ? 'text-base' : 'text-lg'}
          `}>
            {formatCurrency(calculations?.grossIncome || 0, formData.currency)}
          </span>
        </div>
        <div className={`
          flex justify-between items-center border-t pt-3
          ${isMobile ? 'text-lg pt-2' : 'text-xl'}
        `}>
          <span className="font-bold">Ingreso Neto</span>
          <span className={`
            font-bold text-finanz-success
            ${isMobile ? 'text-lg' : 'text-2xl'}
          `}>
            {formatCurrency(calculations?.netIncome || 0, formData.currency)}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
