
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logger } from '@/utils/logger';

// Tasa de cambio de fallback en caso de que la API falle
const FALLBACK_EXCHANGE_RATE = 59.5;

const fetchExchangeRate = async (): Promise<number> => {
  logger.info('Fetching exchange rate from Supabase function...');
  const { data, error } = await supabase.functions.invoke('get-exchange-rate');

  if (error) {
    logger.error('Error fetching exchange rate:', error);
    throw new Error(error.message);
  }

  if (data && typeof data.rate === 'number') {
    logger.info('Exchange rate fetched successfully:', data.rate);
    return data.rate;
  }
  
  logger.warn('Invalid data received from exchange rate function, using fallback.');
  throw new Error('Invalid data format from API');
};

export const useExchangeRate = () => {
  const { data: rate, ...queryInfo } = useQuery<number, Error>({
    queryKey: ['exchange-rate-usd-dop'],
    queryFn: fetchExchangeRate,
    staleTime: 1000 * 60 * 60 * 4, // Cache por 4 horas
    gcTime: 1000 * 60 * 60 * 5, // Garbage collect despues de 5 horas
    refetchOnWindowFocus: false, // No recargar en cada foco de ventana
    retry: 2, // Intentar 2 veces más en caso de error
    select: (data) => data ?? FALLBACK_EXCHANGE_RATE, // Devolver fallback si data es undefined
  });

  return {
    rate: rate ?? FALLBACK_EXCHANGE_RATE,
    ...queryInfo,
  };
};
