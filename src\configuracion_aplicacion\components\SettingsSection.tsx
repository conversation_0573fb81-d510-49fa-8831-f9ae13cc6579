import { logger } from "@/utils/logger";
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useSettings } from '../hooks/useSettings';
import { toast } from 'sonner';
import { ProfileSettingsTab } from './ProfileSettingsTab';
import { NotificationSettingsTab } from './NotificationSettingsTab';
import { SecuritySettingsTab } from './SecuritySettingsTab';
import { PasswordChangeSection } from './PasswordChangeSection';
import { AppearanceSettingsTab } from './AppearanceSettingsTab';
import { DataManagementTab } from './DataManagementTab';

export const SettingsSection: React.FC = () => {
  const {
    profileForm,
    notificationForm,
    securityForm,
    appearanceForm,
    saveProfile,
    saveNotifications,
    saveSecurity,
    saveAppearance,
    applyThemeImmediately,
    deleteAllData,
    defaultCurrency,
    setDefaultCurrency,
    isLoading,
  } = useSettings();

  const [activeTab, setActiveTab] = useState('profile');
  const [passwordLoading, setPasswordLoading] = useState(false);

  const handleSaveSecuritySettings = async () => {
    try {
      const data = securityForm.getValues();
      logger.debug('Security form data:', data);
      
      // Solo enviar los campos de configuración de seguridad (sin contraseñas)
      const securityData = {
        twoFactorAuth: data.twoFactorAuth,
        biometricAuth: data.biometricAuth,
        sessionTimeout: data.sessionTimeout,
        autoLogout: data.autoLogout,
        currentPassword: '',
        newPassword: '',
      };
      
      logger.debug('Saving security data:', securityData);
      await saveSecurity(securityData);
      toast.success('Configuración de seguridad guardada exitosamente');
    } catch (error: unknown) {
      console.error('Error saving security settings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error al guardar la configuración de seguridad';
      toast.error(errorMessage);
    }
  };

  const handleChangePassword = async () => {
    try {
      setPasswordLoading(true);
      const data = securityForm.getValues();
      
      logger.debug('Password change data:', { 
        hasCurrentPassword: !!data.currentPassword, 
        hasNewPassword: !!data.newPassword 
      });
      
      if (!data.currentPassword || !data.newPassword) {
        toast.error('Ambas contraseñas son requeridas');
        return;
      }

      if (data.newPassword.length < 6) {
        toast.error('La nueva contraseña debe tener al menos 6 caracteres');
        return;
      }

      // Para el cambio de contraseña, usar la configuración actual pero con las contraseñas
      const currentSecurityData = securityForm.getValues();
      const passwordData = {
        twoFactorAuth: currentSecurityData.twoFactorAuth,
        biometricAuth: currentSecurityData.biometricAuth,
        sessionTimeout: currentSecurityData.sessionTimeout,
        autoLogout: currentSecurityData.autoLogout,
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      };

      logger.debug('Changing password with data:', passwordData);
      await saveSecurity(passwordData);
      
      // Limpiar campos de contraseña después del éxito
      securityForm.setValue('currentPassword', '');
      securityForm.setValue('newPassword', '');
      
      toast.success('Contraseña cambiada exitosamente');
    } catch (error: unknown) {
      console.error('Error changing password:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error al cambiar la contraseña';
      toast.error(errorMessage);
    } finally {
      setPasswordLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">Perfil</TabsTrigger>
          <TabsTrigger value="notifications">Notificaciones</TabsTrigger>
          <TabsTrigger value="security">Seguridad</TabsTrigger>
          <TabsTrigger value="appearance">Apariencia</TabsTrigger>
          <TabsTrigger value="data">Datos</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <ProfileSettingsTab
            profileForm={profileForm}
            saveProfile={saveProfile}
            defaultCurrency={defaultCurrency}
            setDefaultCurrency={setDefaultCurrency}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettingsTab
            notificationForm={notificationForm}
            saveNotifications={saveNotifications}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="security">
          <div className="space-y-6">
            <SecuritySettingsTab
              securityForm={securityForm}
              handleSaveSecuritySettings={handleSaveSecuritySettings}
              isLoading={isLoading}
            />
            
            <PasswordChangeSection
              securityForm={securityForm}
              handleChangePassword={handleChangePassword}
              passwordLoading={passwordLoading}
            />
          </div>
        </TabsContent>

        <TabsContent value="appearance">
          <AppearanceSettingsTab
            appearanceForm={appearanceForm}
            saveAppearance={saveAppearance}
            applyThemeImmediately={applyThemeImmediately}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="data">
          <DataManagementTab
            deleteAllData={deleteAllData}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
