
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  TrendingUp, 
  Shield, 
  PiggyBank, 
  Calculator, 
  BarChart3, 
  Smartphone,
  Target,
  Sparkles
} from 'lucide-react';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export function FeaturesSection() {
  const { isMobile, isTablet } = useBreakpoint();
  
  const features = [
    {
      icon: TrendingUp,
      title: "Gestión de Ingresos",
      description: "Registra y analiza todos tus ingresos con cálculos automáticos de variables y deducciones.",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      icon: Calculator,
      title: "Control de Gastos",
      description: "Categoriza y monitorea tus gastos para mantener un presupuesto equilibrado.",
      color: "from-emerald-500 to-emerald-600",
      bgColor: "bg-emerald-50",
      iconColor: "text-emerald-600"
    },
    {
      icon: Shield,
      title: "Manejo de Deudas",
      description: "Gestiona tarjetas de crédito, préstamos y deudas personales en un solo lugar.",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      icon: BarChart3,
      title: "Reportes y Análisis",
      description: "Obtén insights detallados sobre tu salud financiera con gráficos interactivos.",
      color: "from-amber-500 to-amber-600",
      bgColor: "bg-amber-50",
      iconColor: "text-amber-600"
    },
    {
      icon: PiggyBank,
      title: "Metas Financieras",
      description: "Define y sigue el progreso de tus objetivos de ahorro e inversión.",
      color: "from-rose-500 to-rose-600",
      bgColor: "bg-rose-50",
      iconColor: "text-rose-600"
    },
    {
      icon: Smartphone,
      title: "Acceso Móvil",
      description: "Gestiona tus finanzas desde cualquier dispositivo con nuestra app responsive.",
      color: "from-indigo-500 to-indigo-600",
      bgColor: "bg-indigo-50",
      iconColor: "text-indigo-600"
    }
  ];

  return (
    <section className={`
      container mx-auto bg-white
      ${isMobile ? 'px-4 py-12' : isTablet ? 'px-4 py-14' : 'px-4 py-16'}
    `}>
      <div className="text-center mb-8 md:mb-12">
        <div className={`
          inline-flex items-center space-x-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full border border-purple-200/50 mb-3
          ${isMobile ? 'px-3 py-1 mb-2' : 'px-5 py-2 mb-4'}
        `}>
          <Target className="w-4 h-4 text-purple-600" />
          <span className={`
            font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent
            ${isMobile ? 'text-xs' : 'text-sm'}
          `}>
            Características
          </span>
        </div>
        <h2 className={`
          font-bold text-gray-900 mb-3
          ${isMobile ? 'text-2xl mb-2' : isTablet ? 'text-3xl mb-3' : 'text-3xl md:text-4xl mb-4'}
        `}>
          Todo lo que necesitas en{' '}
          <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-rose-600 bg-clip-text text-transparent">
            un solo lugar
          </span>
        </h2>
        <p className={`
          text-gray-600 max-w-2xl mx-auto font-medium
          ${isMobile ? 'text-sm' : isTablet ? 'text-base' : 'text-lg'}
        `}>
          Herramientas profesionales diseñadas para simplificar tu gestión financiera personal
        </p>
      </div>
      
      <div className={`
        grid gap-4
        ${isMobile ? 'grid-cols-1 gap-4' : isTablet ? 'grid-cols-2 gap-5' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'}
      `}>
        {features.map((feature, index) => (
          <Card key={index} className={`
            group border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-white hover:-translate-y-2 rounded-xl overflow-hidden
            ${isMobile ? 'hover:-translate-y-1' : ''}
          `}>
            <CardHeader className={`
              relative
              ${isMobile ? 'pb-2' : 'pb-3'}
            `}>
              <div className={`
                rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300 shadow-sm
                ${feature.bgColor}
                ${isMobile ? 'w-10 h-10 mb-2' : 'w-12 h-12'}
              `}>
                <feature.icon className={`
                  ${feature.iconColor}
                  ${isMobile ? 'w-5 h-5' : 'w-6 h-6'}
                `} />
              </div>
              <CardTitle className={`
                font-bold text-gray-900 group-hover:text-gray-800
                ${isMobile ? 'text-base' : isTablet ? 'text-lg' : 'text-lg'}
              `}>
                {feature.title}
              </CardTitle>
              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Sparkles className="w-4 h-4 text-gray-400" />
              </div>
            </CardHeader>
            <CardContent className={isMobile ? 'pt-0' : ''}>
              <CardDescription className={`
                text-gray-600 leading-relaxed
                ${isMobile ? 'text-xs' : 'text-sm'}
              `}>
                {feature.description}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
