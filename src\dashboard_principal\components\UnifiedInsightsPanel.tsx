
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Bell, Lightbulb, AlertTriangle, TrendingUp, Target } from 'lucide-react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export const UnifiedInsightsPanel: React.FC = () => {
  const [activeView, setActiveView] = useState('alerts');
  const { isMobile } = useBreakpoint();
  
  const { 
    getNetBalance, 
    getTotalDebt, 
    getSavingsRate,
    expenses,
    incomes
  } = useFinanceData();

  // Generar alertas críticas
  const generateAlerts = () => {
    const alerts = [];
    const netBalance = getNetBalance();
    const savingsRate = getSavingsRate();

    if (netBalance < 0) {
      alerts.push({
        id: 'negative-balance',
        type: 'danger',
        title: 'Balance Negativo',
        description: 'Tu balance actual es negativo',
        priority: 'high'
      });
    }

    if (savingsRate < 10) {
      alerts.push({
        id: 'low-savings',
        type: 'warning', 
        title: 'Tasa de Ahorro Baja',
        description: `Tu tasa de ahorro es del ${savingsRate.toFixed(1)}%`,
        priority: 'medium'
      });
    }

    return alerts;
  };

  // Generar insights accionables
  const generateInsights = () => {
    const insights = [];
    const currentMonth = new Date().toISOString().slice(0, 7);
    const currentIncome = incomes.find(i => i.month === currentMonth);
    
    if (currentIncome) {
      insights.push({
        id: 'income-opportunity',
        type: 'opportunity',
        title: 'Oportunidad de Optimización',
        description: 'Analiza tus ingresos para identificar áreas de mejora',
        value: `${currentIncome.netIncome.toLocaleString()} RD$`
      });
    }

    return insights;
  };

  const alerts = generateAlerts();
  const insights = generateInsights();

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'danger': return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300';
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300';
      default: return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow duration-300">
      <CardHeader className={isMobile ? 'pb-3' : ''}>
        <CardTitle className={`
          flex items-center gap-2
          ${isMobile ? 'text-base' : 'text-lg'}
        `}>
          <Bell className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
          Panel de Insights
        </CardTitle>
      </CardHeader>
      <CardContent className={isMobile ? 'pt-0' : ''}>
        <Tabs value={activeView} onValueChange={setActiveView}>
          <TabsList className={`
            grid w-full 
            ${isMobile ? 'grid-cols-2 h-auto p-1' : 'grid-cols-2'}
          `}>
            <TabsTrigger 
              value="alerts" 
              className={`
                flex items-center transition-all duration-200
                ${isMobile ? 'flex-col gap-1 py-2 text-xs' : 'gap-2 text-sm'}
              `}
            >
              <AlertTriangle className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
              <span>Alertas ({alerts.length})</span>
            </TabsTrigger>
            <TabsTrigger 
              value="insights" 
              className={`
                flex items-center transition-all duration-200
                ${isMobile ? 'flex-col gap-1 py-2 text-xs' : 'gap-2 text-sm'}
              `}
            >
              <Lightbulb className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
              <span>Insights ({insights.length})</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="alerts" className={`space-y-3 ${isMobile ? 'mt-3' : 'mt-4'}`}>
            {alerts.length === 0 ? (
              <div className={`text-center ${isMobile ? 'py-4' : 'py-6'}`}>
                <Target className={`
                  mx-auto text-green-500 mb-2
                  ${isMobile ? 'w-8 h-8' : 'w-12 h-12'}
                `} />
                <p className={`
                  text-gray-600 dark:text-gray-300
                  ${isMobile ? 'text-sm' : 'text-base'}
                `}>
                  ¡Excelente! No tienes alertas críticas.
                </p>
              </div>
            ) : (
              alerts.map((alert) => (
                <div 
                  key={alert.id} 
                  className={`
                    rounded-lg border transition-all duration-200 hover:shadow-md
                    ${getAlertColor(alert.type)}
                    ${isMobile ? 'p-3' : 'p-4'}
                  `}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className={`
                        flex items-center gap-2 mb-1
                        ${isMobile ? 'flex-col items-start' : ''}
                      `}>
                        <h4 className={`
                          font-medium
                          ${isMobile ? 'text-sm' : 'text-sm'}
                        `}>
                          {alert.title}
                        </h4>
                        <Badge 
                          variant="outline" 
                          className={`
                            ${isMobile ? 'text-xs mt-1' : 'text-xs'}
                          `}
                        >
                          {alert.priority}
                        </Badge>
                      </div>
                      <p className={`
                        opacity-80
                        ${isMobile ? 'text-xs' : 'text-sm'}
                      `}>
                        {alert.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </TabsContent>

          <TabsContent value="insights" className={`space-y-3 ${isMobile ? 'mt-3' : 'mt-4'}`}>
            {insights.length === 0 ? (
              <div className={`text-center ${isMobile ? 'py-4' : 'py-6'}`}>
                <Lightbulb className={`
                  mx-auto text-blue-500 mb-2
                  ${isMobile ? 'w-8 h-8' : 'w-12 h-12'}
                `} />
                <p className={`
                  text-gray-600 dark:text-gray-300
                  ${isMobile ? 'text-sm' : 'text-base'}
                `}>
                  No hay insights disponibles en este momento.
                </p>
              </div>
            ) : (
              insights.map((insight) => (
                <div 
                  key={insight.id} 
                  className={`
                    rounded-lg border bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800 
                    transition-all duration-200 hover:shadow-md
                    ${isMobile ? 'p-3' : 'p-4'}
                  `}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className={`
                        font-medium text-blue-800 dark:text-blue-300
                        ${isMobile ? 'text-sm' : 'text-sm'}
                      `}>
                        {insight.title}
                      </h4>
                      <p className={`
                        text-blue-700 dark:text-blue-400 mt-1
                        ${isMobile ? 'text-xs' : 'text-sm'}
                      `}>
                        {insight.description}
                      </p>
                      {insight.value && (
                        <p className={`
                          font-semibold text-blue-600 dark:text-blue-300 mt-2
                          ${isMobile ? 'text-xs' : 'text-sm'}
                        `}>
                          {insight.value}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
