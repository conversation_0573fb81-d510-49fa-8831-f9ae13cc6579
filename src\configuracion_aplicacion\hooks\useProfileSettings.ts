import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { profileSchema, type ProfileFormData } from '../schemas/settingsSchemas';

export const useProfileSettings = () => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      profession: '',
    },
  });

  const loadProfileData = useCallback(() => {
    if (user && profile) {
      profileForm.reset({
        name: profile.full_name || '',
        email: user.email || '',
        phone: profile.phone || '',
        profession: profile.profession || '',
      });
    }
  }, [user, profile, profileForm]);

  useEffect(() => {
    loadProfileData();
  }, [loadProfileData]);

  const saveProfile = async (data: ProfileFormData) => {
    try {
      setIsLoading(true);
      
      if (!user) {
        throw new Error('Usuario no autenticado');
      }
      
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: data.name,
          phone: data.phone,
          profession: data.profession,
        })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating profile in Supabase:', error);
        throw error;
      }
      
      localStorage.setItem('finanz_profile_settings', JSON.stringify(data));
      
      toast({
        title: 'Perfil actualizado',
        description: 'Los cambios han sido guardados exitosamente',
      });
    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: 'Error',
        description: 'No se pudo guardar el perfil. Intenta nuevamente.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    profileForm,
    saveProfile,
    isLoading,
  };
};
