import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';

vi.mock('@/utils/pagePreloader', () => ({
  preloadIndexPage: vi.fn(),
  preloadAuthPage: vi.fn(),
  preloadDashboardPage: vi.fn(),
  preloadExpensesPage: vi.fn(),
  preloadIncomePage: vi.fn(),
  preloadLoansPage: vi.fn(),
  preloadGoalsPage: vi.fn(),
  preloadConfigurationPage: vi.fn(),
  preloadSubscriptionsPage: vi.fn(),
  preloadReimbursementsPage: vi.fn(),
  preloadPaymentsPage: vi.fn(),
  preloadFinancialAdvicePage: vi.fn(),
  preloadDiagnosticPage: vi.fn(),
}));

import { usePagePreload } from '@/hooks/usePagePreload';
import * as preloadFns from '@/utils/pagePreloader';

describe('usePagePreload', () => {
  it('preloads all pages once', () => {
    const { rerender } = renderHook(() => usePagePreload());

    Object.values(preloadFns).forEach((fn) => {
      expect(fn).toHaveBeenCalledTimes(1);
    });

    rerender();

    Object.values(preloadFns).forEach((fn) => {
      expect(fn).toHaveBeenCalledTimes(1);
    });
  });
});
