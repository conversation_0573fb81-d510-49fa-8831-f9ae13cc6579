
import { logger } from "@/utils/logger";

import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { SecuritySettings } from './types';
import { ROUTES } from '@/constants/routes';

export class SessionMonitor {
  private sessionTimer: NodeJS.Timeout | null = null;
  private lockTimer: NodeJS.Timeout | null = null;
  private lastActivity: number = Date.now();

  constructor(private settings: SecuritySettings | null) {}

  startSessionMonitoring(): void {
    logger.debug('SessionMonitor: Starting session monitoring');
    
    // Only start if auto logout is enabled and timeout is reasonable
    if (!this.settings?.autoLogout || this.settings.sessionTimeout === '0') {
      logger.debug('SessionMonitor: Auto logout disabled, skipping');
      return;
    }

    const timeoutMinutes = parseInt(this.settings.sessionTimeout);
    // Don't start monitoring for very short timeouts (less than 10 minutes)
    if (timeoutMinutes < 10) {
      logger.debug('SessionMonitor: Timeout too short, skipping monitoring');
      return;
    }
    
    // Configurar eventos para detectar actividad
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const updateActivity = () => {
      this.lastActivity = Date.now();
      localStorage.setItem('finanz_last_activity', this.lastActivity.toString());
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // Verificar inactividad cada 2 minutos en lugar de cada minuto
    this.sessionTimer = setInterval(() => {
      this.checkSessionTimeout();
    }, 120000);
  }

  private checkSessionTimeout(): void {
    if (!this.settings?.autoLogout) return;

    const timeoutMinutes = parseInt(this.settings.sessionTimeout);
    if (timeoutMinutes === 0 || timeoutMinutes < 10) return; // Don't timeout if less than 10 minutes

    const timeoutMs = timeoutMinutes * 60 * 1000;
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    
    logger.debug('SessionMonitor: Checking session timeout. Time since last activity:', timeSinceLastActivity / 1000 / 60, 'minutes');
    
    if (timeSinceLastActivity >= timeoutMs) {
      logger.debug('SessionMonitor: Session timeout reached, logging out');
      this.handleSessionTimeout();
    } else if (timeSinceLastActivity >= timeoutMs - 10 * 60 * 1000) {
      // Advertir 10 minutos antes en lugar de 5
      const minutesLeft = Math.ceil((timeoutMs - timeSinceLastActivity) / 60000);
      toast.warning(`Tu sesión expirará en ${minutesLeft} minutos por inactividad`, {
        duration: 5000,
      });
    }
  }

  setupWindowVisibilityHandling(): void {
    logger.debug('SessionMonitor: Setting up window visibility handling');
    
    const handleVisibilityChange = () => {
      if (document.hidden) {
        logger.debug('SessionMonitor: Window hidden, starting lock timer');
        // Increase lock timer to 5 minutes instead of 2
        this.lockTimer = setTimeout(() => {
          this.handleScreenLock();
        }, 5 * 60 * 1000);
      } else {
        logger.debug('SessionMonitor: Window visible, clearing lock timer');
        if (this.lockTimer) {
          clearTimeout(this.lockTimer);
          this.lockTimer = null;
        }
        this.resetActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  private handleScreenLock(): void {
    logger.debug('SessionMonitor: Handling screen lock');
    window.dispatchEvent(new CustomEvent('security:lockScreen'));
    toast.warning('Pantalla bloqueada por seguridad');
  }

  private async handleSessionTimeout(): Promise<void> {
    try {
      logger.debug('SessionMonitor: Handling session timeout');
      await supabase.auth.signOut();
      toast.error('Sesión cerrada por inactividad');
      
      localStorage.removeItem('finanz_last_activity');
      window.location.href = ROUTES.AUTH;
    } catch (error) {
      console.error('SessionMonitor: Error during session timeout logout:', error);
    }
  }

  stopSessionTimer(): void {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  resetActivity(): void {
    this.lastActivity = Date.now();
    localStorage.setItem('finanz_last_activity', this.lastActivity.toString());
  }

  getTimeUntilTimeout(): number {
    if (!this.settings?.autoLogout || this.settings.sessionTimeout === '0') {
      return -1;
    }

    const timeoutMs = parseInt(this.settings.sessionTimeout) * 60 * 1000;
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    return Math.max(0, timeoutMs - timeSinceLastActivity);
  }

  isSessionExpired(): boolean {
    if (!this.settings?.autoLogout) return false;
    
    const timeoutMs = parseInt(this.settings.sessionTimeout) * 60 * 1000;
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    
    return timeSinceLastActivity >= timeoutMs;
  }

  shouldLockScreen(): boolean {
    if (!this.settings?.autoLogout || this.settings.securityLevel === 'basic') return false;
    
    const timeoutMs = parseInt(this.settings.sessionTimeout) * 60 * 1000;
    const lockThreshold = timeoutMs - (10 * 60 * 1000); // 10 minutes before instead of 5
    const timeSinceLastActivity = Date.now() - this.lastActivity;
    
    return timeSinceLastActivity >= lockThreshold;
  }
}
