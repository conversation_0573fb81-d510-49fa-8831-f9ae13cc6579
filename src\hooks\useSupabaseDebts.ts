import { logger } from "@/utils/logger";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { CreditCard, Loan, PersonalDebt, PersonalDebtPayment } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { QUERY_KEYS } from '@/constants/queryKeys';

const CREDIT_CARDS_KEY = QUERY_KEYS.CREDIT_CARDS;
const LOANS_KEY = QUERY_KEYS.LOANS;
const PERSONAL_DEBTS_KEY = QUERY_KEYS.PERSONAL_DEBTS;
const PERSONAL_DEBT_PAYMENTS_KEY = QUERY_KEYS.PERSONAL_DEBT_PAYMENTS;

// DB Interfaces
interface DBCreditCard {
  id: string;
  user_id: string;
  name: string;
  currency: "DOP" | "USD";
  credit_limit: number;
  current_balance: number;
  minimum_payment?: number | null;
  interest_rate?: number | null;
  payment_date?: string | null;
  payment_due_date?: string | null;
  cutoff_date?: string | null;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface DBLoan {
  id: string;
  user_id: string;
  name: string;
  type: string;
  currency: "DOP" | "USD";
  total_amount: number;
  monthly_payment: number;
  interest_rate?: number | null;
  loan_term?: number | null;
  start_date?: string | null;
  due_date?: string | null;
  payment_date?: string | null;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface DBPersonalDebt {
  id: string;
  user_id: string;
  name: string;
  currency: "DOP" | "USD";
  amount: number;
  remaining_balance?: number | null;
  payment_date?: string | null;
  monthly_budget?: number | null;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface DBPersonalDebtPayment {
  id: string;
  user_id: string;
  personal_debt_id: string;
  payment_date: string;
  total_amount: number;
  principal_amount?: number | null;
  interest_amount?: number | null;
  remaining_balance?: number | null;
  notes?: string | null;
  created_at: string;
  updated_at: string;
}

// Transform functions for credit cards
const transformCreditCard = (dbCard: DBCreditCard): CreditCard => ({
  id: dbCard.id,
  name: dbCard.name,
  currency: dbCard.currency,
  creditLimit: dbCard.credit_limit,
  currentBalance: dbCard.current_balance,
  minimumPayment: dbCard.minimum_payment,
  interestRate: dbCard.interest_rate,
  paymentDate: dbCard.payment_date,
  paymentDueDate: dbCard.payment_due_date,
  cutoffDate: dbCard.cutoff_date,
  isActive: dbCard.is_active,
  createdAt: dbCard.created_at,
  updatedAt: dbCard.updated_at
});

// Transform functions for loans
const transformLoan = (dbLoan: DBLoan): Loan => ({
  id: dbLoan.id,
  name: dbLoan.name,
  type: dbLoan.type,
  currency: dbLoan.currency,
  totalAmount: dbLoan.total_amount,
  monthlyPayment: dbLoan.monthly_payment,
  interestRate: dbLoan.interest_rate,
  loanTerm: dbLoan.loan_term,
  startDate: dbLoan.start_date,
  dueDate: dbLoan.due_date,
  paymentDate: dbLoan.payment_date,
  isActive: dbLoan.is_active,
  createdAt: dbLoan.created_at,
  updatedAt: dbLoan.updated_at
});

// Transform functions for personal debts
const transformPersonalDebt = (dbDebt: DBPersonalDebt): PersonalDebt => ({
  id: dbDebt.id,
  name: dbDebt.name,
  currency: dbDebt.currency,
  amount: dbDebt.amount,
  remainingBalance: dbDebt.remaining_balance || dbDebt.amount,
  paymentDate: dbDebt.payment_date,
  monthlyBudget: dbDebt.monthly_budget,
  isActive: dbDebt.is_active,
  createdAt: dbDebt.created_at,
  updatedAt: dbDebt.updated_at
});

// Transform functions for personal debt payments
const transformPersonalDebtPayment = (dbPayment: DBPersonalDebtPayment): PersonalDebtPayment => ({
  id: dbPayment.id,
  personalDebtId: dbPayment.personal_debt_id,
  paymentDate: dbPayment.payment_date,
  totalAmount: dbPayment.total_amount,
  principalAmount: dbPayment.principal_amount,
  interestAmount: dbPayment.interest_amount,
  remainingBalance: dbPayment.remaining_balance,
  notes: dbPayment.notes,
  createdAt: dbPayment.created_at,
  updatedAt: dbPayment.updated_at
});

export const useSupabaseDebts = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Fetch credit cards
  const { data: creditCards = [], isLoading: isLoadingCreditCards } = useQuery({
    queryKey: [CREDIT_CARDS_KEY, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      logger.debug('Fetching credit cards from Supabase for user:', user.id);
      const { data, error } = await supabase
        .from('credit_cards')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching credit cards:', error);
        throw error;
      }
      
      logger.debug('Fetched credit cards:', data);
      return data.map(transformCreditCard);
    },
    enabled: !!user?.id && enabled,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
  });

  // Fetch loans
  const { data: loans = [], isLoading: isLoadingLoans } = useQuery({
    queryKey: [LOANS_KEY, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      logger.debug('Fetching loans from Supabase for user:', user.id);
      const { data, error } = await supabase
        .from('loans')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching loans:', error);
        throw error;
      }
      
      logger.debug('Fetched loans:', data);
      return data.map(transformLoan);
    },
    enabled: !!user?.id && enabled,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
  });

  // Fetch personal debts
  const { data: personalDebts = [], isLoading: isLoadingPersonalDebts } = useQuery({
    queryKey: [PERSONAL_DEBTS_KEY, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      logger.debug('Fetching personal debts from Supabase for user:', user.id);
      const { data, error } = await supabase
        .from('personal_debts')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching personal debts:', error);
        throw error;
      }
      
      logger.debug('Fetched personal debts:', data);
      return data.map(transformPersonalDebt);
    },
    enabled: !!user?.id && enabled,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
  });

  // Fetch personal debt payments
  const { data: personalDebtPayments = [], isLoading: isLoadingPersonalDebtPayments } = useQuery({
    queryKey: [PERSONAL_DEBT_PAYMENTS_KEY, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      logger.debug('Fetching personal debt payments from Supabase for user:', user.id);
      const { data, error } = await supabase
        .from('personal_debt_payments')
        .select('*')
        .eq('user_id', user.id)
        .order('payment_date', { ascending: false });
      
      if (error) {
        console.error('Error fetching personal debt payments:', error);
        throw error;
      }
      
      logger.debug('Fetched personal debt payments:', data);
      return data.map(transformPersonalDebtPayment);
    },
    enabled: !!user?.id && enabled,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
  });

  // Mutations for credit cards
  const addCreditCardMutation = useMutation({
    mutationFn: async (creditCard: Omit<CreditCard, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const dbCreditCard = {
        user_id: user.id,
        name: creditCard.name,
        currency: creditCard.currency,
        credit_limit: creditCard.creditLimit,
        current_balance: creditCard.currentBalance,
        minimum_payment: creditCard.minimumPayment,
        interest_rate: creditCard.interestRate,
        payment_date: creditCard.paymentDate,
        payment_due_date: creditCard.paymentDueDate,
        cutoff_date: creditCard.cutoffDate,
        is_active: creditCard.isActive
      };
      
      const { data, error } = await supabase
        .from('credit_cards')
        .insert([dbCreditCard])
        .select()
        .single();
      
      if (error) throw error;
      return transformCreditCard(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CREDIT_CARDS_KEY, user?.id] });
    },
  });

  const updateCreditCardMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<CreditCard> }) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbUpdates: Partial<DBCreditCard> = transformCreditCardUpdatesToDb(updates);
      if (Object.keys(dbUpdates).length === 0) {
        // Avoid making an update call if there are no changes.
        // Fetch and return the existing card or handle as needed.
        const { data: existingData, error: fetchError } = await supabase
          .from('credit_cards')
          .select('*')
          .eq('id', id)
          .eq('user_id', user.id)
          .single();
        if (fetchError) throw fetchError;
        if (!existingData) throw new Error("Card not found");
        return transformCreditCard(existingData);
      }
  
      const { data, error } = await supabase
        .from('credit_cards')
        .update(dbUpdates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
  
      if (error) throw error;
      return transformCreditCard(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CREDIT_CARDS_KEY, user?.id] });
    },
  });

  const deleteCreditCardMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const { error } = await supabase
        .from('credit_cards')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
  
      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CREDIT_CARDS_KEY, user?.id] });
    },
  });

  // Mutations for loans
  const addLoanMutation = useMutation({
    mutationFn: async (loan: Omit<Loan, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbLoan = {
        user_id: user.id,
        name: loan.name,
        type: loan.type,
        currency: loan.currency,
        total_amount: loan.totalAmount,
        monthly_payment: loan.monthlyPayment,
        interest_rate: loan.interestRate,
        loan_term: loan.loanTerm,
        start_date: loan.startDate,
        due_date: loan.dueDate,
        payment_date: loan.paymentDate,
        is_active: loan.isActive
      };
  
      const { data, error } = await supabase
        .from('loans')
        .insert([dbLoan])
        .select()
        .single();
  
      if (error) throw error;
      return transformLoan(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [LOANS_KEY, user?.id] });
    },
  });

  const updateLoanMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Loan> }) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbUpdates: Partial<DBLoan> = transformLoanUpdatesToDb(updates);
      if (Object.keys(dbUpdates).length === 0) {
        const { data: existingData, error: fetchError } = await supabase
          .from('loans')
          .select('*')
          .eq('id', id)
          .eq('user_id', user.id)
          .single();
        if (fetchError) throw fetchError;
        if (!existingData) throw new Error("Loan not found");
        return transformLoan(existingData);
      }

      const { data, error } = await supabase
        .from('loans')
        .update(dbUpdates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
  
      if (error) throw error;
      return transformLoan(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [LOANS_KEY, user?.id] });
    },
  });

  const deleteLoanMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const { error } = await supabase
        .from('loans')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
  
      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [LOANS_KEY, user?.id] });
    },
  });

  // Mutations for personal debts
  const addPersonalDebtMutation = useMutation({
    mutationFn: async (debt: Omit<PersonalDebt, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbDebt = {
        user_id: user.id,
        name: debt.name,
        currency: debt.currency,
        amount: debt.amount,
        // ensure column names match the database schema
        remaining_balance: debt.remainingBalance || debt.amount,
        payment_date: debt.paymentDate,
        monthly_budget: debt.monthlyBudget,
        is_active: debt.isActive
      };
  
      const { data, error } = await supabase
        .from('personal_debts')
        .insert([dbDebt])
        .select()
        .single();
  
      if (error) throw error;
      return transformPersonalDebt(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PERSONAL_DEBTS_KEY, user?.id] });
    },
  });

  const updatePersonalDebtMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<PersonalDebt> }) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbUpdates: Partial<DBPersonalDebt> = transformPersonalDebtUpdatesToDb(updates);
      if (Object.keys(dbUpdates).length === 0) {
        const { data: existingData, error: fetchError } = await supabase
          .from('personal_debts')
          .select('*')
          .eq('id', id)
          .eq('user_id', user.id)
          .single();
        if (fetchError) throw fetchError;
        if (!existingData) throw new Error("Personal debt not found");
        return transformPersonalDebt(existingData);
      }

      const { data, error } = await supabase
        .from('personal_debts')
        .update(dbUpdates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
  
      if (error) throw error;
      return transformPersonalDebt(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PERSONAL_DEBTS_KEY, user?.id] });
    },
  });

  const deletePersonalDebtMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const { error } = await supabase
        .from('personal_debts')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
  
      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PERSONAL_DEBTS_KEY, user?.id] });
    },
  });

  // Mutations for personal debt payments
  const addPersonalDebtPaymentMutation = useMutation({
    mutationFn: async (payment: Omit<PersonalDebtPayment, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbPayment = {
        user_id: user.id,
        personal_debt_id: payment.personalDebtId,
        payment_date: payment.paymentDate,
        total_amount: payment.totalAmount,
        principal_amount: payment.principalAmount,
        interest_amount: payment.interestAmount,
        remaining_balance: payment.remainingBalance,
        notes: payment.notes
      };
  
      const { data, error } = await supabase
        .from('personal_debt_payments')
        .insert([dbPayment])
        .select()
        .single();
  
      if (error) throw error;
      return transformPersonalDebtPayment(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PERSONAL_DEBT_PAYMENTS_KEY, user?.id] });
    },
  });

  const updatePersonalDebtPaymentMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<PersonalDebtPayment> }) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const dbUpdates: Partial<DBPersonalDebtPayment> = transformPersonalDebtPaymentUpdatesToDb(updates);
      if (Object.keys(dbUpdates).length === 0) {
        const { data: existingData, error: fetchError } = await supabase
          .from('personal_debt_payments')
          .select('*')
          .eq('id', id)
          .eq('user_id', user.id)
          .single();
        if (fetchError) throw fetchError;
        if (!existingData) throw new Error("Personal debt payment not found");
        return transformPersonalDebtPayment(existingData);
      }

      const { data, error } = await supabase
        .from('personal_debt_payments')
        .update(dbUpdates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();
  
      if (error) throw error;
      return transformPersonalDebtPayment(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PERSONAL_DEBT_PAYMENTS_KEY, user?.id] });
    },
  });

  const deletePersonalDebtPaymentMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');
  
      const { error } = await supabase
        .from('personal_debt_payments')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);
  
      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PERSONAL_DEBT_PAYMENTS_KEY, user?.id] });
    },
  });

  return {
    creditCards,
    isLoadingCreditCards,
    addCreditCard: addCreditCardMutation.mutateAsync,
    updateCreditCard: (id: string, updates: Partial<CreditCard>) =>
      updateCreditCardMutation.mutateAsync({ id, updates }),
    deleteCreditCard: (id: string) =>
      deleteCreditCardMutation.mutateAsync(id),

    loans,
    isLoadingLoans,
    addLoan: addLoanMutation.mutateAsync,
    updateLoan: (id: string, updates: Partial<Loan>) =>
      updateLoanMutation.mutateAsync({ id, updates }),
    deleteLoan: (id: string) => deleteLoanMutation.mutateAsync(id),

    personalDebts,
    isLoadingPersonalDebts,
    addPersonalDebt: addPersonalDebtMutation.mutateAsync,
    updatePersonalDebt: (id: string, updates: Partial<PersonalDebt>) =>
      updatePersonalDebtMutation.mutateAsync({ id, updates }),
    deletePersonalDebt: (id: string) =>
      deletePersonalDebtMutation.mutateAsync(id),

    personalDebtPayments,
    isLoadingPersonalDebtPayments,
    addPersonalDebtPayment: addPersonalDebtPaymentMutation.mutateAsync,
    updatePersonalDebtPayment: (
      id: string,
      updates: Partial<PersonalDebtPayment>
    ) => updatePersonalDebtPaymentMutation.mutateAsync({ id, updates }),
    deletePersonalDebtPayment: (id: string) =>
      deletePersonalDebtPaymentMutation.mutateAsync(id),
  };
};

// Transform update objects for CreditCard
const transformCreditCardUpdatesToDb = (updates: Partial<CreditCard>): Partial<DBCreditCard> => {
  const dbUpdates: Partial<DBCreditCard> = {};
  if (updates.name !== undefined) dbUpdates.name = updates.name;
  if (updates.currency !== undefined) dbUpdates.currency = updates.currency;
  if (updates.creditLimit !== undefined) dbUpdates.credit_limit = updates.creditLimit;
  if (updates.currentBalance !== undefined) dbUpdates.current_balance = updates.currentBalance;
  if (updates.minimumPayment !== undefined) dbUpdates.minimum_payment = updates.minimumPayment;
  if (updates.interestRate !== undefined) dbUpdates.interest_rate = updates.interestRate;
  if (updates.paymentDate !== undefined) dbUpdates.payment_date = updates.paymentDate;
  if (updates.paymentDueDate !== undefined) dbUpdates.payment_due_date = updates.paymentDueDate;
  if (updates.cutoffDate !== undefined) dbUpdates.cutoff_date = updates.cutoffDate;
  if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;
  return dbUpdates;
};

// Transform update objects for Loan
const transformLoanUpdatesToDb = (updates: Partial<Loan>): Partial<DBLoan> => {
  const dbUpdates: Partial<DBLoan> = {};
  if (updates.name !== undefined) dbUpdates.name = updates.name;
  if (updates.type !== undefined) dbUpdates.type = updates.type;
  if (updates.currency !== undefined) dbUpdates.currency = updates.currency;
  if (updates.totalAmount !== undefined) dbUpdates.total_amount = updates.totalAmount;
  if (updates.monthlyPayment !== undefined) dbUpdates.monthly_payment = updates.monthlyPayment;
  if (updates.interestRate !== undefined) dbUpdates.interest_rate = updates.interestRate;
  if (updates.loanTerm !== undefined) dbUpdates.loan_term = updates.loanTerm;
  if (updates.startDate !== undefined) dbUpdates.start_date = updates.startDate;
  if (updates.dueDate !== undefined) dbUpdates.due_date = updates.dueDate;
  if (updates.paymentDate !== undefined) dbUpdates.payment_date = updates.paymentDate;
  if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;
  return dbUpdates;
};

// Transform update objects for PersonalDebt
const transformPersonalDebtUpdatesToDb = (updates: Partial<PersonalDebt>): Partial<DBPersonalDebt> => {
  const dbUpdates: Partial<DBPersonalDebt> = {};
  if (updates.name !== undefined) dbUpdates.name = updates.name;
  if (updates.currency !== undefined) dbUpdates.currency = updates.currency;
  if (updates.amount !== undefined) dbUpdates.amount = updates.amount;
  if (updates.remainingBalance !== undefined) dbUpdates.remaining_balance = updates.remainingBalance;
  if (updates.paymentDate !== undefined) dbUpdates.payment_date = updates.paymentDate;
  if (updates.monthlyBudget !== undefined) dbUpdates.monthly_budget = updates.monthlyBudget;
  if (updates.isActive !== undefined) dbUpdates.is_active = updates.isActive;
  return dbUpdates;
};

// Transform update objects for PersonalDebtPayment
const transformPersonalDebtPaymentUpdatesToDb = (updates: Partial<PersonalDebtPayment>): Partial<DBPersonalDebtPayment> => {
  const dbUpdates: Partial<DBPersonalDebtPayment> = {};
  if (updates.personalDebtId !== undefined) dbUpdates.personal_debt_id = updates.personalDebtId;
  if (updates.paymentDate !== undefined) dbUpdates.payment_date = updates.paymentDate;
  if (updates.totalAmount !== undefined) dbUpdates.total_amount = updates.totalAmount;
  if (updates.principalAmount !== undefined) dbUpdates.principal_amount = updates.principalAmount;
  if (updates.interestAmount !== undefined) dbUpdates.interest_amount = updates.interestAmount;
  if (updates.remainingBalance !== undefined) dbUpdates.remaining_balance = updates.remainingBalance;
  if (updates.notes !== undefined) dbUpdates.notes = updates.notes;
  return dbUpdates;
};
