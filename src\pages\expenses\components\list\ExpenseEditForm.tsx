
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { DatePicker } from '@/components/ui/date-picker';
import { Switch } from '@/components/ui/switch';
import { X, Check } from 'lucide-react';
import { NumericInput } from '@/components/ui/numeric-input';
import { Expense, defaultCategories } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';

interface ExpenseEditFormProps {
  expense: Expense;
  onSave: (id: string, updates: Partial<Expense>) => void;
  onCancel: () => void;
}

export function ExpenseEditForm({ expense, onSave, onCancel }: ExpenseEditFormProps) {
  const { toast } = useToast();
  const isDarkMode = useIsDarkMode();
  const [editFormData, setEditFormData] = useState<Partial<Expense>>({
    date: expense.date,
    currency: expense.currency,
    amount: expense.amount,
    type: expense.type,
    categoryId: expense.categoryId,
    categoryName: expense.categoryName,
    description: expense.description,
    paymentMethod: expense.paymentMethod,
    status: expense.status,
    paymentDate: expense.paymentDate,
    isRecurring: expense.isRecurring
  });

  // Función para convertir string a Date
  const parseDate = (dateString: string): Date | undefined => {
    if (!dateString) return undefined;
    return new Date(dateString);
  };

  // Función para convertir Date a string
  const formatDate = (date: Date | undefined): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  const handleCategoryChange = (categoryId: string) => {
    const category = defaultCategories.find(cat => cat.id === categoryId);
    setEditFormData(prev => ({ 
      ...prev, 
      categoryId,
      categoryName: category?.name || ''
      // Descripción se mantiene sin cambios - usuario tiene control total
    }));
  };

  const handleSave = async () => {
    if (!editFormData.date || !editFormData.amount || !editFormData.categoryId) {
      toast({
        title: "Error",
        description: "Por favor complete todos los campos requeridos",
        variant: "destructive"
      });
      return;
    }

    const category = defaultCategories.find(cat => cat.id === editFormData.categoryId);
    
    const updates: Partial<Expense> = {
      ...editFormData,
      categoryName: category?.name || editFormData.categoryName,
    };

    try {
      await onSave(expense.id, updates);
      
      toast({
        title: "Éxito",
        description: "Gasto actualizado correctamente",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo actualizar el gasto",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="expense-date">Fecha *</Label>
          {isDarkMode ? (
            <DatePicker
              id="expense-date"
              date={parseDate(editFormData.date || '')}
              onSelect={(date) => setEditFormData(prev => ({ ...prev, date: formatDate(date) }))}
              placeholder="Selecciona una fecha"
              name="date"
              autoComplete="off"
            />
          ) : (
            <Input
              id="expense-date"
              type="date"
              value={editFormData.date}
              onChange={(e) => setEditFormData(prev => ({ ...prev, date: e.target.value }))}
              name="date"
              autoComplete="off"
            />
          )}
        </div>
        <div>
          <Label htmlFor={editFormData.isRecurring ? "payment-day-select" : "payment-date"}>{editFormData.isRecurring ? 'Día de Pago' : 'Fecha de Pago'}</Label>
          {editFormData.isRecurring ? (
            <>
              <Select
                value={editFormData.paymentDate ? String(Number(editFormData.paymentDate.split('-')[2] || 1)) : ''}
                onValueChange={(value) =>
                  setEditFormData(prev => ({ ...prev, paymentDate: value.padStart(2, '0') }))
                }
              >
                <SelectTrigger id="payment-day-select" name="payment_date" autoComplete="off">
                  <SelectValue placeholder="Selecciona el día de pago" />
                </SelectTrigger>
                <SelectContent>
                  {[...Array(31)].map((_, i) => (
                    <SelectItem key={i + 1} value={String(i + 1).padStart(2, '0')}>
                      {i + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-finanz-text-secondary mt-1">
                El gasto se generará automáticamente el día seleccionado de cada mes.
              </p>
            </>
          ) : (
            isDarkMode ? (
              <DatePicker
                id="payment-date"
                date={parseDate(editFormData.paymentDate || '')}
                onSelect={(date) =>
                  setEditFormData(prev => ({ ...prev, paymentDate: formatDate(date) }))
                }
                placeholder="Selecciona fecha de pago"
                name="payment_date"
                autoComplete="off"
              />
            ) : (
              <Input
                id="payment-date"
                type="date"
                value={editFormData.paymentDate}
                onChange={(e) =>
                  setEditFormData(prev => ({ ...prev, paymentDate: e.target.value }))
                }
                name="payment_date"
                autoComplete="off"
              />
            )
          )}
        </div>
        <div>
          <Label htmlFor="expense-amount">Monto *</Label>
          <NumericInput
            id="expense-amount"
            value={editFormData.amount || 0}
            onChange={(value) => setEditFormData(prev => ({ ...prev, amount: value }))}
            currency={editFormData.currency}
            showCurrency={true}
            name="amount"
            autoComplete="off"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expense-currency">Moneda</Label>
          <Select
            value={editFormData.currency}
            onValueChange={(value: 'DOP' | 'USD') => 
              setEditFormData(prev => ({ ...prev, currency: value }))
            }
          >
            <SelectTrigger id="expense-currency" name="currency" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DOP">Pesos Dominicanos (RD$)</SelectItem>
              <SelectItem value="USD">Dólares (USD$)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="expense-category">Categoría *</Label>
          <Select
            value={editFormData.categoryId}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger id="expense-category" name="category_id" autoComplete="off">
              <SelectValue placeholder="Seleccionar categoría" />
            </SelectTrigger>
            <SelectContent>
              {defaultCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expense-type">Tipo</Label>
          <Select
            value={editFormData.type}
            onValueChange={(value: 'Fijo' | 'Variable') => 
              setEditFormData(prev => ({ ...prev, type: value }))
            }
          >
            <SelectTrigger id="expense-type" name="type" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Fijo">Fijo</SelectItem>
              <SelectItem value="Variable">Variable</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="expense-status">Estado</Label>
          <Select
            value={editFormData.status}
            onValueChange={(value: 'pending' | 'paid') =>
              setEditFormData(prev => ({ ...prev, status: value }))
            }
          >
            <SelectTrigger id="expense-status" name="status" autoComplete="off">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="paid">Pagado</SelectItem>
              <SelectItem value="pending">Pendiente</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2 mt-4 md:mt-0">
          <Switch
            id="edit-isRecurring"
            checked={editFormData.isRecurring ?? false}
            onCheckedChange={(checked) =>
              setEditFormData(prev => ({ ...prev, isRecurring: checked as boolean }))
            }
            name="is_recurring"
          />
          <Label htmlFor="edit-isRecurring">Recurrente</Label>
        </div>
      </div>

      <div>
        <Label htmlFor="payment-method">Método de Pago</Label>
        <Select
          value={editFormData.paymentMethod}
          onValueChange={(value: 'cash' | 'debit-card' | 'credit-card' | 'transfer') => 
            setEditFormData(prev => ({ ...prev, paymentMethod: value }))
          }
        >
          <SelectTrigger id="payment-method" name="payment_method" autoComplete="off">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="cash">Efectivo</SelectItem>
            <SelectItem value="debit-card">Tarjeta de Débito</SelectItem>
            <SelectItem value="credit-card">Tarjeta de Crédito</SelectItem>
            <SelectItem value="transfer">Transferencia</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="expense-description">Descripción</Label>
        <Textarea
          id="expense-description"
          value={editFormData.description || ''}
          onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Ingresa una descripción personalizada para este gasto (opcional)"
          className="min-h-[80px]"
          name="description"
          autoComplete="off"
        />
        <p className="text-xs text-finanz-text-secondary mt-1">
          📝 Puedes dejar este campo en blanco si no deseas agregar una descripción
        </p>
      </div>

      <div className="flex space-x-2">
        <Button onClick={handleSave} size="sm" className="bg-finanz-success hover:bg-finanz-success/90">
          <Check className="w-4 h-4 mr-2" />
          Guardar
        </Button>
        <Button onClick={onCancel} variant="outline" size="sm">
          <X className="w-4 h-4 mr-2" />
          Cancelar
        </Button>
      </div>
    </div>
  );
}
