
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Lightbulb, TrendingUp, Shield, Target, ChevronRight, Star } from 'lucide-react';
import { useFinanceData } from '@/hooks/useFinanceData';

interface Recommendation {
  id: string;
  category: 'savings' | 'debt' | 'investment' | 'emergency' | 'optimization';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  difficulty: 'easy' | 'medium' | 'hard';
  timeframe: string;
  potentialSaving: number;
  steps: string[];
  priority: number;
}

export function AdvancedRecommendations() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const { 
    getNetBalance, 
    getSavingsRate, 
    // getDebtToIncomeRatio, // Not used
    getTotalDebt,
    expenses,
    creditCards
  } = useFinanceData();

  const generateRecommendations = (): Recommendation[] => {
    const recommendations: Recommendation[] = [];
    const netBalance = getNetBalance();
    const savingsRate = getSavingsRate();
    // const debtRatio = getDebtToIncomeRatio(); // Not used
    const totalDebt = getTotalDebt();

    // Recomendación de fondo de emergencia
    if (netBalance < 50000) { // Menos de 3 meses de gastos promedio
      recommendations.push({
        id: 'emergency-fund',
        category: 'emergency',
        title: 'Crear Fondo de Emergencia',
        description: 'Establece un fondo de emergencia equivalente a 3-6 meses de gastos.',
        impact: 'high',
        difficulty: 'medium',
        timeframe: '6-12 meses',
        potentialSaving: 0,
        steps: [
          'Calcula tus gastos mensuales promedio',
          'Establece una meta de 3-6 meses de gastos',
          'Ahorra automáticamente el 10% de tus ingresos',
          'Mantén este dinero en una cuenta separada'
        ],
        priority: 9
      });
    }

    // Optimización de deudas
    if (totalDebt > 100000) {
      const highInterestCards = creditCards.filter(card => card.interestRate > 20);
      if (highInterestCards.length > 0) {
        recommendations.push({
          id: 'debt-optimization',
          category: 'debt',
          title: 'Consolidar Deudas de Alto Interés',
          description: 'Refinancia o consolida tus deudas con tasas de interés altas.',
          impact: 'high',
          difficulty: 'medium',
          timeframe: '1-3 meses',
          potentialSaving: totalDebt * 0.05, // 5% de ahorro estimado
          steps: [
            'Identifica todas las deudas con interés > 20%',
            'Busca opciones de consolidación',
            'Compara tasas de interés',
            'Aplica el método avalancha para pagos'
          ],
          priority: 8
        });
      }
    }

    // Mejorar tasa de ahorro
    if (savingsRate < 20) {
      recommendations.push({
        id: 'improve-savings',
        category: 'savings',
        title: 'Aumentar Tasa de Ahorro',
        description: 'Incrementa tu tasa de ahorro al 20% de tus ingresos.',
        impact: 'high',
        difficulty: 'easy',
        timeframe: '2-4 meses',
        potentialSaving: netBalance * 0.2,
        steps: [
          'Analiza tus gastos actuales',
          'Identifica gastos innecesarios',
          'Configura transferencias automáticas',
          'Revisa y ajusta mensualmente'
        ],
        priority: 7
      });
    }

    // Optimización de gastos
    const currentMonth = new Date().toISOString().slice(0, 7);
    const monthlyExpenses = expenses
      .filter(expense => expense.month === currentMonth)
      .reduce((acc, expense) => {
        acc[expense.categoryId] = (acc[expense.categoryId] || 0) + expense.amount;
        return acc;
      }, {} as Record<string, number>);

    const highestExpenseCategory = Object.entries(monthlyExpenses)
      .sort(([,a], [,b]) => b - a)[0];

    if (highestExpenseCategory && highestExpenseCategory[1] > netBalance * 0.3) {
      recommendations.push({
        id: 'expense-optimization',
        category: 'optimization',
        title: 'Optimizar Gastos Principales',
        description: `Reduce gastos en tu categoría más alta: ${highestExpenseCategory[1].toLocaleString('es-DO')} RD$.`,
        impact: 'medium',
        difficulty: 'easy',
        timeframe: '1-2 meses',
        potentialSaving: highestExpenseCategory[1] * 0.15,
        steps: [
          'Revisa todos los gastos de esta categoría',
          'Busca alternativas más económicas',
          'Negocia mejores precios con proveedores',
          'Elimina gastos innecesarios'
        ],
        priority: 6
      });
    }

    // Inversión para el futuro
    if (savingsRate > 15 && totalDebt < netBalance * 2) {
      recommendations.push({
        id: 'start-investing',
        category: 'investment',
        title: 'Comenzar a Invertir',
        description: 'Considera invertir parte de tus ahorros para hacer crecer tu dinero.',
        impact: 'high',
        difficulty: 'medium',
        timeframe: '3-6 meses',
        potentialSaving: netBalance * 0.08, // 8% retorno anual estimado
        steps: [
          'Edúcate sobre opciones de inversión',
          'Define tu perfil de riesgo',
          'Comienza con fondos diversificados',
          'Invierte solo dinero que no necesites'
        ],
        priority: 5
      });
    }

    return recommendations.sort((a, b) => b.priority - a.priority);
  };

  const recommendations = generateRecommendations();
  const categories = [
    { id: 'all', label: 'Todas', icon: Lightbulb },
    { id: 'emergency', label: 'Emergencia', icon: Shield },
    { id: 'savings', label: 'Ahorros', icon: Target },
    { id: 'debt', label: 'Deudas', icon: TrendingUp },
    { id: 'investment', label: 'Inversión', icon: Star },
    { id: 'optimization', label: 'Optimización', icon: TrendingUp }
  ];

  const filteredRecommendations = selectedCategory === 'all' 
    ? recommendations 
    : recommendations.filter(rec => rec.category === selectedCategory);

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'hard': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-finanz-primary" />
          Recomendaciones Inteligentes
        </CardTitle>
        
        {/* Filtros por categoría */}
        <div className="flex flex-wrap gap-2 mt-4">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="text-xs"
            >
              <category.icon className="w-3 h-3 mr-1" />
              {category.label}
            </Button>
          ))}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {filteredRecommendations.length === 0 ? (
          <div className="text-center py-6">
            <Lightbulb className="w-12 h-12 mx-auto text-gray-400 mb-2" />
            <p className="text-finanz-text-secondary">No hay recomendaciones para esta categoría.</p>
          </div>
        ) : (
          filteredRecommendations.slice(0, 3).map((recommendation) => (
            <div key={recommendation.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium">{recommendation.title}</h4>
                    <Badge className={`text-xs ${getImpactColor(recommendation.impact)}`}>
                      {recommendation.impact} impact
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-finanz-text-secondary mb-3">
                    {recommendation.description}
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="text-finanz-text-secondary">Dificultad: </span>
                      <span className={getDifficultyColor(recommendation.difficulty)}>
                        {recommendation.difficulty}
                      </span>
                    </div>
                    <div>
                      <span className="text-finanz-text-secondary">Tiempo: </span>
                      <span>{recommendation.timeframe}</span>
                    </div>
                  </div>
                  
                  {recommendation.potentialSaving > 0 && (
                    <div className="mt-2">
                      <span className="text-xs text-finanz-text-secondary">Ahorro potencial: </span>
                      <span className="text-sm font-medium text-green-600">
                        {recommendation.potentialSaving.toLocaleString('es-DO')} RD$
                      </span>
                    </div>
                  )}
                </div>
                
                <Button variant="ghost" size="sm">
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
              
              {/* Pasos de implementación */}
              <div className="border-t pt-3">
                <h5 className="text-sm font-medium mb-2">Pasos para implementar:</h5>
                <div className="space-y-1">
                  {recommendation.steps.slice(0, 2).map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-xs">
                      <div className="w-4 h-4 rounded-full bg-finanz-primary/20 text-finanz-primary flex items-center justify-center text-xs">
                        {index + 1}
                      </div>
                      <span className="text-finanz-text-secondary">{step}</span>
                    </div>
                  ))}
                  {recommendation.steps.length > 2 && (
                    <span className="text-xs text-finanz-text-secondary ml-6">
                      +{recommendation.steps.length - 2} pasos más...
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        
        {filteredRecommendations.length > 3 && (
          <div className="text-center pt-2">
            <Button variant="ghost" size="sm">
              Ver todas las recomendaciones ({filteredRecommendations.length - 3} más)
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
