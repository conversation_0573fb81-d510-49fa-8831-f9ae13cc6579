
import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { XCircle, AlertTriangle } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
  passwordStrength: {
    score: number;
    label: string;
    color: string;
    feedback: string[];
  };
  isPasswordCompromised: boolean;
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  passwordStrength,
  isPasswordCompromised
}) => {
  if (!password) return null;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">Fortaleza:</span>
        <span className={`text-sm font-medium ${passwordStrength.color}`}>
          {passwordStrength.label}
        </span>
      </div>
      <Progress
        value={(passwordStrength.score / 10) * 100}
        className="h-2"
        indicatorClassName={passwordStrength.color.replace('text-', 'bg-')}
      />
      
      {passwordStrength.feedback.length > 0 && (
        <div className="text-xs text-gray-600 space-y-1">
          {passwordStrength.feedback.map((tip, index) => (
            <div key={index} className="flex items-center space-x-1">
              <XCircle className="w-3 h-3 text-orange-500" />
              <span>{tip}</span>
            </div>
          ))}
        </div>
      )}
      
      {isPasswordCompromised && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800 text-sm">
            Esta contraseña ha sido comprometida en filtraciones de datos. Por favor elige otra.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
