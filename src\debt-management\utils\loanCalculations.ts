
// Función para calcular el pago mensual usando la fórmula de amortización
export const calculateMonthlyPayment = (principal: number, annualRate: number, termInMonths: number): number => {
  if (principal <= 0 || termInMonths <= 0) return 0;
  if (annualRate === 0) return principal / termInMonths;
  
  const monthlyRate = annualRate / 100 / 12; // Convertir porcentaje anual a decimal mensual
  const numerator = principal * monthlyRate * Math.pow(1 + monthlyRate, termInMonths);
  const denominator = Math.pow(1 + monthlyRate, termInMonths) - 1;
  
  return numerator / denominator;
};

// Función para calcular la fecha de vencimiento
export const calculateDueDate = (startDate: string, termInMonths: number): string => {
  if (!startDate || termInMonths <= 0) return startDate;
  
  const start = new Date(startDate);
  const dueDate = new Date(start);
  dueDate.setMonth(dueDate.getMonth() + termInMonths);
  
  return dueDate.toISOString().split('T')[0];
};
