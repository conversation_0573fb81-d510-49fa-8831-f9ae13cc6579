import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { visualizer } from "rollup-plugin-visualizer";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Habilitar HTTP/2 en desarrollo
    https: false, // Cambiar a true si tienes certificados locales
  },
  plugins: [
    react(),
    mode === 'production' &&
    visualizer({
      filename: 'dist/bundle-report.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        // Optimización más granular de chunks para reducir el trabajo del hilo principal
        manualChunks: (id) => {
          // Separar node_modules por categorías más específicas
          if (id.includes('node_modules')) {
            // React core - carga prioritaria
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-core';
            }

            // Router - carga bajo demanda
            if (id.includes('react-router')) {
              return 'router';
            }

            // UI Components - dividir en chunks más pequeños
            if (id.includes('@radix-ui')) {
              return 'ui-radix';
            }

            // Supabase - carga bajo demanda
            if (id.includes('@supabase') || id.includes('supabase')) {
              return 'supabase';
            }

            // Charts - carga bajo demanda (muy pesado)
            if (id.includes('recharts') || id.includes('d3-')) {
              return 'charts';
            }

            // Forms - carga bajo demanda
            if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('zod')) {
              return 'forms';
            }

            // PDF generation - carga bajo demanda (muy pesado)
            if (id.includes('jspdf') || id.includes('html2canvas')) {
              return 'pdf-tools';
            }

            // Query management
            if (id.includes('@tanstack/react-query')) {
              return 'query';
            }

            // Utilities - pequeño, puede ir con vendor
            if (id.includes('date-fns') || id.includes('clsx') || id.includes('tailwind-merge')) {
              return 'utils';
            }

            // Motion/Animation - carga bajo demanda
            if (id.includes('framer-motion')) {
              return 'animations';
            }

            // Icons - separar para carga eficiente
            if (id.includes('lucide-react')) {
              return 'icons';
            }

            // Resto de vendor libraries
            return 'vendor';
          }
        },
        // Optimizar nombres de archivos para mejor caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `js/[name]-[hash].js`;
        },
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(css)$/.test(assetInfo.name)) {
            return `css/[name]-[hash].${ext}`;
          }
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `images/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        },
      },
    },
    // Reducir el límite de advertencia de chunk
    chunkSizeWarningLimit: 500,
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        // Optimizaciones adicionales
        pure_funcs: mode === 'production' ? ['console.log', 'console.info', 'console.debug'] : [],
        passes: 2, // Múltiples pasadas de optimización
      },
      mangle: {
        safari10: true, // Compatibilidad con Safari 10
      },
      format: {
        comments: false, // Eliminar comentarios
      },
    },
    // Configuración de target para mejor compatibilidad y rendimiento
    target: ['es2020', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
    // Optimizar CSS
    cssCodeSplit: true,
    cssMinify: true,
    // Deshabilitar compresión en build para evitar conflictos con Vercel
    reportCompressedSize: false,
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@supabase/supabase-js',
      '@tanstack/react-query',
      'react-hook-form',
      '@hookform/resolvers',
      'zod',
      'date-fns',
      'clsx',
      'tailwind-merge',
    ],
    // Excluir dependencias pesadas de la pre-optimización
    exclude: [
      'recharts',
      'jspdf',
      'html2canvas',
      'framer-motion',
    ],
  },
  // Configuración de performance
  esbuild: {
    // Eliminar console.log en producción a nivel de esbuild también
    drop: mode === 'production' ? ['console', 'debugger'] : [],
  },
}));
