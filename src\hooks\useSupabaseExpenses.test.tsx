import React from 'react'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { describe, it, expect, vi, beforeEach, beforeAll } from 'vitest'

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'user1' } })
}))

interface MockDBExpense {
  id: string;
  date: string;
  payment_date: string | null;
  is_recurring: boolean;
  // Add other fields if they are transformed or used by useSupabaseExpenses hook
}

// variables used in mocked supabase client
let fetchedData: MockDBExpense[] = []
let select: vi.Mock
let eq: vi.Mock
let not: vi.Mock
let order: vi.Mock
let del: vi.Mock
let inFn: vi.Mock
let from: vi.Mock

vi.mock('@/integrations/supabase/client', () => {
  order = vi.fn(() => Promise.resolve({ data: fetchedData, error: null }));
  not = vi.fn(() => ({ order }));
  eq = vi.fn(() => ({ not, order }));
  select = vi.fn(() => ({ eq, not, order }));
  inFn = vi.fn(() => Promise.resolve({ error: null }));
  del = vi.fn(() => ({ in: inFn }));
  from = vi.fn(() => ({ select, delete: del }));

  return { supabase: { from } };
})

// Cargar el hook después de que los mocks anteriores ya están definidos
beforeAll(async () => {
  const module = await import('@/hooks/useSupabaseExpenses');
  useSupabaseExpenses = module.useSupabaseExpenses;
});

beforeEach(() => {
  fetchedData = [
    { id: 'exp1', date: '2024-01-01', payment_date: '2024-01-02', is_recurring: true },
    { id: 'exp2', date: '2024-01-05', payment_date: null, is_recurring: false }
  ]
  select.mockClear()
  eq.mockClear()
  not.mockClear()
  order.mockClear()
  del.mockClear()
  inFn.mockClear()
  from.mockClear()
})

describe('useSupabaseExpenses', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={new QueryClient()}>{children}</QueryClientProvider>
  )

  it('filters and cleans invalid expenses', async () => {
    const { result } = renderHook(() => useSupabaseExpenses(), { wrapper })

    await waitFor(() => expect(result.current.isLoading).toBe(false))

    expect(result.current.expenses).toHaveLength(1)
    expect(result.current.expenses[0].id).toBe('exp1')
    expect(result.current.expenses[0].isRecurring).toBe(true)
    expect(del).toHaveBeenCalled()
    expect(inFn).toHaveBeenCalledWith('id', ['exp2'])
    expect(not).toHaveBeenCalledWith('payment_date', 'is', null)
  })
})

// Declaración para cargar el hook dinámicamente después de que Vitest aplique los mocks
let useSupabaseExpenses: typeof import('@/hooks/useSupabaseExpenses').useSupabaseExpenses;
