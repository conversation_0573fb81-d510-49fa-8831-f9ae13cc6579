
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Calendar, TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react';

interface PeriodMetrics {
  totalPayments: number;
  completedPayments: number;
  pendingPayments: number;
  overduePayments: number;
  completionRate: number;
  totalAmount: number;
  isProjection: boolean;
}

interface PeriodMetricsCardProps {
  metrics: PeriodMetrics;
  periodName: string;
  isCurrentMonth: boolean;
}

export const PeriodMetricsCard: React.FC<PeriodMetricsCardProps> = ({
  metrics,
  periodName,
  isCurrentMonth
}) => {
  const getCompletionRateColor = (rate: number) => {
    if (rate >= 80) return 'text-finanz-success';
    if (rate >= 60) return 'text-finanz-warning';
    return 'text-finanz-danger';
  };

  const getCompletionRateIcon = (rate: number) => {
    if (rate >= 80) return <CheckCircle className="w-4 h-4 text-finanz-success" />;
    if (rate >= 60) return <TrendingUp className="w-4 h-4 text-finanz-warning" />;
    return <AlertTriangle className="w-4 h-4 text-finanz-danger" />;
  };

  const getProgressColor = (rate: number) => {
    if (rate >= 80) return 'bg-finanz-success';
    if (rate >= 60) return 'bg-finanz-warning';
    return 'bg-finanz-danger';
  };

  return (
    <Card className={`${metrics.isProjection ? 'border-finanz-indigo/30 bg-finanz-indigo/5' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Calendar className="w-5 h-5" />
            {periodName}
          </CardTitle>
          <div className="flex gap-2">
            {isCurrentMonth && (
              <Badge variant="default" className="bg-finanz-primary">
                Actual
              </Badge>
            )}
            {metrics.isProjection && (
              <Badge variant="outline" className="border-finanz-indigo text-finanz-indigo">
                Proyección
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Resumen principal */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-finanz-text-primary">
              {metrics.totalPayments}
            </div>
            <div className="text-xs text-finanz-text-secondary">
              Total Pagos
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-finanz-success">
              {metrics.completedPayments}
            </div>
            <div className="text-xs text-finanz-text-secondary">
              Completados
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-finanz-warning">
              {metrics.pendingPayments}
            </div>
            <div className="text-xs text-finanz-text-secondary">
              Pendientes
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-finanz-danger">
              {metrics.overduePayments}
            </div>
            <div className="text-xs text-finanz-text-secondary">
              Vencidos
            </div>
          </div>
        </div>

        {/* Tasa de completitud */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getCompletionRateIcon(metrics.completionRate)}
              <span className="text-sm font-medium">Tasa de Completitud</span>
            </div>
            <span className={`text-lg font-bold ${getCompletionRateColor(metrics.completionRate)}`}>
              {metrics.completionRate.toFixed(1)}%
            </span>
          </div>
          
          <Progress 
            value={metrics.completionRate} 
            className="h-2"
          />
          
          <div className="text-xs text-finanz-text-secondary text-center">
            {metrics.completionRate >= 80 
              ? '¡Excelente cumplimiento!' 
              : metrics.completionRate >= 60 
                ? 'Buen cumplimiento, se puede mejorar' 
                : 'Necesita atención inmediata'
            }
          </div>
        </div>

        {/* Monto total */}
        <div className="bg-finanz-background rounded-lg p-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-finanz-text-secondary">
              Monto Total del Período:
            </span>
            <span className="text-lg font-bold text-finanz-text-primary">
              {formatCurrency(metrics.totalAmount)}
            </span>
          </div>
          {metrics.isProjection && (
            <div className="text-xs text-finanz-indigo mt-1">
              * Basado en proyecciones inteligentes
            </div>
          )}
        </div>

        {/* Alertas rápidas */}
        {metrics.overduePayments > 0 && (
          <div className="bg-finanz-danger/10 border border-finanz-danger/20 rounded-lg p-3">
            <div className="flex items-center gap-2 text-finanz-danger">
              <AlertTriangle className="w-4 h-4" />
              <span className="font-medium text-sm">
                ¡Atención! Tienes {metrics.overduePayments} pago(s) vencido(s)
              </span>
            </div>
          </div>
        )}

        {!metrics.isProjection && metrics.completionRate === 100 && metrics.totalPayments > 0 && (
          <div className="bg-finanz-success/10 border border-finanz-success/20 rounded-lg p-3">
            <div className="flex items-center gap-2 text-finanz-success">
              <CheckCircle className="w-4 h-4" />
              <span className="font-medium text-sm">
                ¡Perfecto! Todos los pagos completados este período
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
