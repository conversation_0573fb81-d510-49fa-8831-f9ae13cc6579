/* eslint-disable react-refresh/only-export-components */

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { initializeSecurityHeaders } from '@/utils/security/contentSecurity';
import { auditLog } from '@/utils/securityUtils';

interface SecurityContextType {
  isSecurityInitialized: boolean;
}

const SecurityContext = createContext<SecurityContextType>({
  isSecurityInitialized: false
});

export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within SecurityProvider');
  }
  return context;
};

interface SecurityProviderProps {
  children: ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const [isSecurityInitialized, setIsSecurityInitialized] = React.useState(false);

  useEffect(() => {
    const initializeSecurity = () => {
      try {
        // Initialize security headers and CSP
        initializeSecurityHeaders();
        
        setIsSecurityInitialized(true);
        auditLog('security_initialization_complete', {});

        return () => {
          // no cleanup needed currently
        };
      } catch (error) {
        console.error('Security initialization failed:', error);
        auditLog('security_initialization_failed', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        return () => {};
      }
    };

    const cleanup = initializeSecurity();
    return cleanup;
  }, []);

  return (
    <SecurityContext.Provider value={{ isSecurityInitialized }}>
      {children}
    </SecurityContext.Provider>
  );
};
