import { renderHook, act } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { useIsMobile } from '@/hooks/use-mobile'

describe('useIsMobile', () => {
  it('returns true when window width is below breakpoint', () => {
    window.innerWidth = 500
    const { result } = renderHook(() => useIsMobile())
    expect(result.current).toBe(true)
  })

  it('updates when matchMedia change event fires', () => {
    window.innerWidth = 800
    const listeners: Record<string, (e: MediaQueryListEvent) => void> = {}
    ;(window.matchMedia as any).mockImplementation((query: string) => ({
      matches: false,
      media: query,
      addEventListener: (event: string, cb: (e: any) => void) => {
        listeners[event] = cb
      },
      removeEventListener: vi.fn(),
    }))

    const { result } = renderHook(() => useIsMobile())
    expect(result.current).toBe(false)

    act(() => {
      window.innerWidth = 500
      listeners['change']?.({ matches: true } as MediaQueryListEvent)
    })

    expect(result.current).toBe(true)
  })
})
