
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Monitor, Moon, Sun } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import type { AppearanceFormData } from '../../schemas/settingsSchemas';

interface ThemeSelectorProps {
  appearanceForm: UseFormReturn<AppearanceFormData>;
  applyThemeImmediately: (theme: 'light' | 'dark') => void;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  appearanceForm,
  applyThemeImmediately,
}) => {
  const handleThemeChange = (theme: 'light' | 'dark') => {
    applyThemeImmediately(theme);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Monitor className="w-5 h-5" />
          Tema y Apariencia
        </CardTitle>
        <CardDescription>
          Personaliza cómo se ve la aplicación
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={appearanceForm.control}
          name="theme"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tema</FormLabel>
              <Select 
                onValueChange={(value: 'light' | 'dark') => {
                  field.onChange(value);
                  handleThemeChange(value);
                }} 
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un tema" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="light">
                    <div className="flex items-center gap-2">
                      <Sun className="w-4 h-4" />
                      Claro
                    </div>
                  </SelectItem>
                  <SelectItem value="dark">
                    <div className="flex items-center gap-2">
                      <Moon className="w-4 h-4" />
                      Oscuro
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
};
