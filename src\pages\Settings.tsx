
import { Settings as SettingsIcon } from 'lucide-react';
import { SettingsSection } from '@/configuracion_aplicacion/components/SettingsSection';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export default function SettingsPage() {
  const { defaultCurrency, isLoading } = useFinanceData();
  const { isMobile } = useBreakpoint();

  if (isLoading && !defaultCurrency) {
    return (
      <div className={`space-y-4 md:space-y-6 animate-pulse ${isMobile ? 'p-3' : 'p-6'}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className={`bg-gray-200 rounded mb-2 ${isMobile ? 'h-6 w-32' : 'h-8 w-64'}`}></div>
            <div className={`bg-gray-200 rounded ${isMobile ? 'h-3 w-24' : 'h-4 w-48'}`}></div>
          </div>
          <div className={`bg-gray-200 rounded ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`}></div>
        </div>
        <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-4'}`}>
          {[1, 2, 3, 4].map(i => (
            <div key={i} className={`bg-gray-200 rounded-lg ${isMobile ? 'h-20' : 'h-24'}`}></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 md:space-y-6 ${isMobile ? 'p-3' : 'p-6'}`}>
      <div className={`flex items-center justify-between ${isMobile ? 'flex-col space-y-2' : ''}`}>
        <div className={`${isMobile ? 'text-center' : ''}`}>
          <h1 className={`font-bold text-foreground ${isMobile ? 'text-lg' : 'text-3xl'}`}>
            Configuración
          </h1>
          <p className={`text-muted-foreground ${isMobile ? 'text-xs' : ''}`}>
            {isMobile ? 'Personaliza tu app' : 'Personaliza tu experiencia en FinanzApp'}
          </p>
        </div>
        <SettingsIcon className={`text-muted-foreground ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`} />
      </div>

      <SettingsSection />
    </div>
  );
}
