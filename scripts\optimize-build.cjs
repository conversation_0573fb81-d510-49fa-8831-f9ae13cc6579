/**
 * Script de optimización post-build para mejorar el rendimiento
 * Ejecuta después del build de Vite para optimizaciones adicionales
 */

const fs = require('fs').promises;
const path = require('path');

const DIST_DIR = path.join(__dirname, '../dist');

/**
 * Optimizar archivos HTML
 */
async function optimizeHTML() {
  console.log('🔧 Optimizando archivos HTML...');
  
  try {
    const htmlFiles = await findFiles(DIST_DIR, '.html');
    
    for (const htmlFile of htmlFiles) {
      let content = await fs.readFile(htmlFile, 'utf-8');
      
      // Agregar preload hints para recursos críticos
      const preloadHints = `
  <link rel="preload" href="/js/react-core-" as="script" crossorigin>
  <link rel="preload" href="/css/" as="style">
  <link rel="dns-prefetch" href="https://fonts.googleapis.com">
  <link rel="dns-prefetch" href="https://fonts.gstatic.com">
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>`;
      
      // Insertar antes del cierre de </head>
      content = content.replace('</head>', `${preloadHints}\n</head>`);
      
      // Agregar atributos de performance a scripts
      content = content.replace(
        /<script([^>]*src="[^"]*"[^>]*)>/g,
        '<script$1 defer>'
      );
      
      await fs.writeFile(htmlFile, content);
    }
    
    console.log('✅ HTML optimizado');
  } catch (error) {
    console.error('❌ Error optimizando HTML:', error);
  }
}

/**
 * Generar manifest.json optimizado
 */
async function generateOptimizedManifest() {
  console.log('🔧 Generando manifest optimizado...');
  
  try {
    const manifestPath = path.join(DIST_DIR, 'manifest.json');
    
    const manifest = {
      name: 'Finanz App',
      short_name: 'Finanz',
      description: 'Aplicación de gestión financiera personal',
      start_url: '/',
      display: 'standalone',
      background_color: '#ffffff',
      theme_color: '#22c55e',
      orientation: 'portrait-primary',
      categories: ['finance', 'productivity'],
      lang: 'es',
      icons: [
        {
          src: '/favicon.svg',
          sizes: 'any',
          type: 'image/svg+xml',
          purpose: 'any maskable'
        }
      ]
    };
    
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
    console.log('✅ Manifest generado');
  } catch (error) {
    console.error('❌ Error generando manifest:', error);
  }
}

/**
 * Analizar el bundle y generar reporte
 */
async function analyzeBundleSize() {
  console.log('🔧 Analizando tamaño del bundle...');
  
  try {
    const jsFiles = await findFiles(DIST_DIR, '.js');
    const cssFiles = await findFiles(DIST_DIR, '.css');
    
    let totalJSSize = 0;
    let totalCSSSize = 0;
    
    for (const file of jsFiles) {
      const stats = await fs.stat(file);
      totalJSSize += stats.size;
    }
    
    for (const file of cssFiles) {
      const stats = await fs.stat(file);
      totalCSSSize += stats.size;
    }
    
    const report = {
      timestamp: new Date().toISOString(),
      totalJSSize: formatBytes(totalJSSize),
      totalCSSSize: formatBytes(totalCSSSize),
      totalSize: formatBytes(totalJSSize + totalCSSSize),
      jsFiles: jsFiles.length,
      cssFiles: cssFiles.length,
    };
    
    console.log('📊 Reporte del bundle:');
    console.log(`   JavaScript: ${report.totalJSSize} (${report.jsFiles} archivos)`);
    console.log(`   CSS: ${report.totalCSSSize} (${report.cssFiles} archivos)`);
    console.log(`   Total: ${report.totalSize}`);
    
    // Guardar reporte
    await fs.writeFile(
      path.join(DIST_DIR, 'bundle-report.json'),
      JSON.stringify(report, null, 2)
    );
    
  } catch (error) {
    console.error('❌ Error analizando bundle:', error);
  }
}

/**
 * Utilidades
 */
async function findFiles(dir, extension) {
  const files = [];
  
  async function scan(currentDir) {
    const entries = await fs.readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        await scan(fullPath);
      } else if (entry.name.endsWith(extension)) {
        files.push(fullPath);
      }
    }
  }
  
  await scan(dir);
  return files;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Función principal
 */
async function main() {
  console.log('🚀 Iniciando optimización post-build...\n');
  
  try {
    await optimizeHTML();
    await generateOptimizedManifest();
    await analyzeBundleSize();
    
    console.log('\n✅ Optimización completada exitosamente!');
  } catch (error) {
    console.error('\n❌ Error durante la optimización:', error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}
