import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';
import {
  CreditCard,
  Building2, // Changed from Building
  Repeat,
  User,
  ShoppingCart, // Changed from Receipt
  Check,
  Clock,
  AlertTriangle,
  Calendar, // Kept for general use
  Eye, // For projection view button
  Undo // For unmark button
} from 'lucide-react';
import { PaymentItem as PaymentItemType } from '../types/paymentTypes'; // Ensure this path is correct
import { format, differenceInCalendarDays, isToday } from 'date-fns';
import { es } from 'date-fns/locale';

// Helper function from original PaymentItem.tsx for type label (can be expanded)
const getPaymentTypeLabel = (type: string) => {
  switch (type) {
    case 'credit-card': return 'Tarjeta de Crédito';
    case 'loan': return 'Préstamo';
    case 'subscription': return 'Suscripción';
    case 'personal-debt': return 'Deuda Personal';
    case 'expense': return 'Gasto';
    default: return 'Pago';
  }
};

const getPaymentTypeIcon = (type: string) => {
  switch (type) {
    case 'credit-card': return <CreditCard className="w-4 h-4" />;
    case 'loan': return <Building2 className="w-4 h-4" />;
    case 'subscription': return <Repeat className="w-4 h-4" />;
    case 'personal-debt': return <User className="w-4 h-4" />;
    case 'expense': return <ShoppingCart className="w-4 h-4" />;
    default: return <Calendar className="w-4 h-4" />; // Default icon
  }
};

const getStatusBadgeStyle = (status: string) => {
  switch (status) {
    case 'paid': return 'bg-finanz-success text-white';
    case 'overdue': return 'bg-finanz-danger text-white';
    case 'pending': return 'bg-finanz-warning text-white';
    default: return 'bg-gray-500 text-white';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'paid': return <Check className="w-3 h-3" />;
    case 'overdue': return <AlertTriangle className="w-3 h-3" />;
    case 'pending': return <Clock className="w-3 h-3" />;
    default: return <Clock className="w-3 h-3" />;
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'paid': return 'Pagado';
    case 'overdue': return 'Vencido';
    case 'pending': return 'Pendiente';
    default: return 'Desconocido';
  }
};

interface PaymentItemProps {
  payment: PaymentItemType;
  onMarkAsPaid?: (payment: PaymentItemType) => void;
  onUnmarkAsPaid?: (payment: PaymentItemType) => void;
  isProjection?: boolean;
}

const PaymentItemComponent: React.FC<PaymentItemProps> = ({
  payment,
  onMarkAsPaid,
  onUnmarkAsPaid,
  isProjection = false
}) => {
  const dueDate = new Date(payment.dueDate);
  const paidDate = payment.paidDate ? new Date(payment.paidDate) : null;

  // Status derived from payment object, but we can enhance date display
  const actualStatus = payment.status; // Use status from payment object as source of truth
  const isPaid = actualStatus === 'paid';
  const isOverdue = actualStatus === 'overdue'; // Relies on payment.status

  // Date-fns calculations for display enhancements
  const today = new Date();
  const daysDifference = differenceInCalendarDays(dueDate, today);

  let dueDateDisplay: string;
  let dueDateClass = "text-sm text-finanz-text-secondary";

  if (isPaid && paidDate) {
    dueDateDisplay = `Pagado el ${format(paidDate, 'dd MMM', { locale: es })}`;
    dueDateClass = "text-sm text-finanz-success";
  } else if (isToday(dueDate) && actualStatus === 'pending') {
    dueDateDisplay = 'Vence hoy';
    dueDateClass = "text-sm text-finanz-warning font-medium";
  } else if (actualStatus === 'overdue') {
    // Use a more general "Vencido" message if status is already overdue.
    // Or, if payment.status doesn't automatically become 'overdue', calculate from date:
    // const isActuallyOverdue = isPast(dueDate) && !isToday(dueDate) && actualStatus === 'pending';
    // if (isActuallyOverdue) { ... }
    dueDateDisplay = `Venció el ${format(dueDate, 'dd MMM', { locale: es })}`;
    if (daysDifference < 0) {
        dueDateDisplay = `Vencido hace ${Math.abs(daysDifference)} días`;
    }
    dueDateClass = "text-sm text-finanz-danger font-medium";
  } else if (daysDifference > 0 && daysDifference <= 7 && actualStatus === 'pending') {
    dueDateDisplay = `Vence en ${daysDifference} día${daysDifference > 1 ? 's' : ''}`;
    dueDateClass = "text-sm text-finanz-warning font-medium";
  } else {
    dueDateDisplay = `Vence el ${format(dueDate, 'dd MMM yyyy', { locale: es })}`;
  }


  return (
    <Card className={`transition-all hover:shadow-md ${
      actualStatus === 'overdue' ? 'border-l-4 border-l-finanz-danger' :
      actualStatus === 'paid' ? 'border-l-4 border-l-finanz-success' :
      'border-l-4 border-l-finanz-warning'
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-3"> {/* items-start for better alignment with multiline info */}
          {/* Información principal */}
          <div className="flex items-start gap-3 flex-1"> {/* items-start here too */}
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-finanz-background flex-shrink-0 mt-1">
              {getPaymentTypeIcon(payment.type)}
            </div>
            
            <div className="flex-1 min-w-0"> {/* min-w-0 for truncation */}
              <div className="flex items-center gap-2 mb-0.5">
                <h4 className="font-medium text-finanz-text-primary truncate">
                  {payment.name}
                </h4>
                {isProjection && (
                  <Badge variant="outline" className="text-xs py-0.5">
                    Proyección
                  </Badge>
                )}
                {payment.type === 'personal-debt' && !isProjection && (
                  <Badge variant="outline" className="text-xs py-0.5 border-finanz-blue/50 text-finanz-blue">
                    Abono Sugerido
                  </Badge>
                )}
              </div>
              
              <p className={dueDateClass}>
                {dueDateDisplay}
              </p>
              <p className="text-xs text-finanz-text-secondary mt-0.5">
                {getPaymentTypeLabel(payment.type)}
              </p>
              {payment.notes && (
                <p className="text-xs text-finanz-text-secondary mt-1 truncate" title={payment.notes}>
                  Nota: {payment.notes}
                </p>
              )}
            </div>
          </div>

          {/* Monto y estado */}
          <div className="flex flex-col items-end gap-2 flex-shrink-0">
             <div className="text-right">
                <div className="text-lg font-semibold text-finanz-text-primary">
                    {formatCurrency(payment.amount, payment.currency)}
                </div>
                <Badge className={`text-xs px-2 py-0.5 ${getStatusBadgeStyle(actualStatus)}`}>
                    <span className="flex items-center gap-1">
                    {getStatusIcon(actualStatus)}
                    {getStatusText(actualStatus)}
                    </span>
                </Badge>
            </div>


            {/* Acciones */}
            <div className="flex flex-col gap-1 items-end">
              {!isProjection && actualStatus === 'pending' && onMarkAsPaid && (
                <Button
                  size="xs" // Smaller button
                  onClick={() => onMarkAsPaid(payment)}
                  className="text-xs"
                >
                  Marcar Pagado
                </Button>
              )}

              {!isProjection && actualStatus === 'overdue' && onMarkAsPaid && (
                <Button
                  size="xs"
                  variant="destructive"
                  onClick={() => onMarkAsPaid(payment)}
                  className="text-xs"
                >
                  Pagar Ahora
                </Button>
              )}

              {!isProjection && actualStatus === 'paid' && onUnmarkAsPaid && (
                <Button
                  size="xs"
                  variant="outline"
                  onClick={() => onUnmarkAsPaid(payment)}
                  className="text-xs"
                >
                  <Undo className="w-3 h-3 mr-1" /> Desmarcar
                </Button>
              )}

              {isProjection && (
                <Button
                  size="xs"
                  variant="outline"
                  className="text-xs"
                  disabled
                >
                  <Eye className="w-3 h-3 mr-1" />
                  Ver Proyección
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const PaymentItem = React.memo(PaymentItemComponent);
