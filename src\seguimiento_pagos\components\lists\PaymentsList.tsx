import React from 'react';
import { PaymentItem as PaymentItemType } from '../../types/paymentTypes'; // Adjusted import for broader compatibility
import { PaymentItem } from '../PaymentItem';
import { PaymentItemEnhanced } from '../PaymentItemEnhanced'; // Import for EnhancedPaymentsSection

interface PaymentsListProps {
  paymentsList: PaymentItemType[];
  onMarkAsPaid?: (payment: PaymentItemType) => void;
  onUnmarkAsPaid?: (payment: PaymentItemType) => void;
  isProjection?: boolean; // Added for EnhancedPaymentsSection
  useEnhancedItem?: boolean; // Flag to switch between PaymentItem and PaymentItemEnhanced
}

export function PaymentsList({
  paymentsList,
  onMarkAsPaid,
  onUnmarkAsPaid,
  isProjection = false,
  useEnhancedItem = false,
}: PaymentsListProps) {
  return (
    <div className="space-y-3">
      {paymentsList.map((payment) =>
        useEnhancedItem ? (
          <PaymentItemEnhanced
            key={payment.id}
            payment={payment}
            onMarkAsPaid={!isProjection ? onMarkAsPaid : undefined}
            onUnmarkAsPaid={!isProjection ? onUnmarkAsPaid : undefined}
            isProjection={isProjection}
          />
        ) : (
          <PaymentItem
            key={payment.id}
            payment={payment}
            onMarkAsPaid={onMarkAsPaid}
            onUnmarkAsPaid={onUnmarkAsPaid}
          />
        )
      )}
    </div>
  );
}
