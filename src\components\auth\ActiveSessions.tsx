
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Monitor, Smartphone, Tablet, MapPin, Calendar, Shield, LogOut } from 'lucide-react';
import { toast } from 'sonner';

interface Session {
  id: string;
  device: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browser: string;
  location: string;
  lastActive: Date;
  current: boolean;
  ipAddress: string;
}

interface ActiveSessionsProps {
  onTerminateSession: (sessionId: string) => Promise<void>;
  onTerminateAllSessions: () => Promise<void>;
}

export const ActiveSessions: React.FC<ActiveSessionsProps> = ({
  onTerminateSession,
  onTerminateAllSessions
}) => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Simular datos de sesión
  useEffect(() => {
    const mockSessions: Session[] = [
      {
        id: '1',
        device: 'MacBook Pro',
        deviceType: 'desktop',
        browser: 'Chrome 120.0',
        location: 'Santo Domingo, RD',
        lastActive: new Date(),
        current: true,
        ipAddress: '*************'
      },
      {
        id: '2',
        device: 'iPhone 15',
        deviceType: 'mobile',
        browser: 'Safari Mobile',
        location: 'Santiago, RD',
        lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
        current: false,
        ipAddress: '*************'
      },
      {
        id: '3',
        device: 'iPad Air',
        deviceType: 'tablet',
        browser: 'Safari Mobile',
        location: 'La Vega, RD',
        lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 día atrás
        current: false,
        ipAddress: '*************'
      }
    ];
    setSessions(mockSessions);
  }, []);

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="w-4 h-4" />;
      case 'tablet':
        return <Tablet className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  const formatLastActive = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Ahora mismo';
    if (minutes < 60) return `Hace ${minutes} min`;
    if (hours < 24) return `Hace ${hours} h`;
    return `Hace ${days} días`;
  };

  const handleTerminateSession = async (sessionId: string) => {
    setIsLoading(true);
    try {
      await onTerminateSession(sessionId);
      setSessions(sessions.filter(s => s.id !== sessionId));
      toast.success('Sesión cerrada exitosamente');
    } catch {
      toast.error('Error al cerrar sesión');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTerminateAllSessions = async () => {
    setIsLoading(true);
    try {
      await onTerminateAllSessions();
      setSessions(sessions.filter(s => s.current));
      toast.success('Todas las sesiones remotas han sido cerradas');
    } catch {
      toast.error('Error al cerrar sesiones');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Sesiones Activas
            </CardTitle>
            <CardDescription>
              Gestiona tus dispositivos conectados y sesiones activas
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleTerminateAllSessions}
            disabled={isLoading || sessions.filter(s => !s.current).length === 0}
          >
            <LogOut className="w-4 h-4 mr-2" />
            Cerrar todas
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {sessions.map((session, index) => (
          <div key={session.id}>
            <div className="flex items-start justify-between p-4 rounded-lg border">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  {getDeviceIcon(session.deviceType)}
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{session.device}</h4>
                    {session.current && (
                      <Badge variant="secondary" className="text-xs">
                        Sesión actual
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{session.browser}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      {session.location}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatLastActive(session.lastActive)}
                    </div>
                  </div>
                  <p className="text-xs text-gray-400">IP: {session.ipAddress}</p>
                </div>
              </div>
              {!session.current && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleTerminateSession(session.id)}
                  disabled={isLoading}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              )}
            </div>
            {index < sessions.length - 1 && <Separator />}
          </div>
        ))}

        {sessions.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Shield className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No hay sesiones activas</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
