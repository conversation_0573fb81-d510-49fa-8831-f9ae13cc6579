
import React from 'react';

interface TrendIconProps {
  trend: 'up' | 'down' | 'stable';
  size?: number;
  className?: string;
}

export const TrendIcon: React.FC<TrendIconProps> = ({ trend, size = 16, className = '' }) => {
  const iconStyle: React.CSSProperties = {
    width: `${size}px`,
    height: `${size}px`,
    display: 'inline-block'
  };

  const getColor = () => {
    switch (trend) {
      case 'up': return '#DC2626'; // Rojo para incremento (malo en gastos)
      case 'down': return '#059669'; // Verde para decremento (bueno en gastos)
      case 'stable': return '#6B7280'; // Gris para estable
      default: return '#6B7280';
    }
  };

  const svgColor = getColor();

  if (trend === 'up') {
    return (
      <svg style={iconStyle} className={className} viewBox="0 0 24 24" fill="none">
        <path 
          d="M7 14L12 9L17 14" 
          stroke={svgColor} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    );
  }

  if (trend === 'down') {
    return (
      <svg style={iconStyle} className={className} viewBox="0 0 24 24" fill="none">
        <path 
          d="M7 10L12 15L17 10" 
          stroke={svgColor} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
      </svg>
    );
  }

  // stable
  return (
    <svg style={iconStyle} className={className} viewBox="0 0 24 24" fill="none">
      <path 
        d="M5 12H19" 
        stroke={svgColor} 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  );
};
