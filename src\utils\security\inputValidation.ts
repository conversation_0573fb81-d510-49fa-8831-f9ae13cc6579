
import { z } from 'zod';
import { rateLimiter } from './rateLimiter';

// Enhanced email validation schema with additional security checks
export const emailSchema = z.string()
  .email('Por favor ingresa un email válido')
  .min(6, 'El email es demasiado corto')
  .max(254, 'El email es demasiado largo')
  .refine((email) => {
    // Additional security validations
    const suspiciousPatterns = [
      /admin@/i,
      /root@/i,
      /[<>]/,
      /javascript:/i,
      /data:/i,
      /\s/,
      /\0/,
      /\\/,
      /'|"|\$|\{|\}|\(|\)/,
    ];
    return !suspiciousPatterns.some(pattern => pattern.test(email));
  }, 'Email contiene caracteres no permitidos')
  .refine((email) => {
    // Check against high-risk TLDs
    const highRiskTLDs = ['.xyz', '.top', '.tk', '.ml', '.ga', '.cf', '.gq'];
    return !highRiskTLDs.some(tld => email.toLowerCase().endsWith(tld));
  }, 'Por favor usa un correo con un dominio estándar');

// Enhanced password validation schema with stronger security requirements
export const passwordSchema = z.string()
  .min(8, 'La contraseña debe tener al menos 8 caracteres') // Cambiado de 12 a 8
  .max(128, 'La contraseña es demasiado larga')
  .regex(/[A-Z]/, 'La contraseña debe incluir al menos una letra mayúscula')
  .regex(/[a-z]/, 'La contraseña debe incluir al menos una letra minúscula')
  .regex(/[0-9]/, 'La contraseña debe incluir al menos un número')
  .regex(/[!@#$%^&*(),.?":{}|<>]/, 'La contraseña debe incluir al menos un carácter especial')
  .refine((password) => {
    // Check for common weak patterns
    const weakPatterns = [
      /password/i,
      /12345/,
      /qwerty/i,
      /asdfg/i,
      /admin/i,
      /user/i,
    ];
    return !weakPatterns.some(pattern => pattern.test(password));
  }, 'La contraseña contiene secuencias o palabras comunes');

// Sanitize and normalize email addresses for consistent handling
export const sanitizeEmail = (email: string): string => {
  if (!email || typeof email !== 'string') return '';
  
  // Convert to lowercase and trim
  return email
    .toLowerCase()
    .trim()
    // Remove dots from gmail addresses (gmail ignores dots)
    .replace(/^([^@]*)\.([^@]*)(@gmail\.com)$/i, '$1$2$3')
    // Remove plus addressing
    .replace(/^([^@]*)(\+[^@]*)(@.*)$/i, '$1$3');
};

// Rate limiting implementation - tracks attempts and enforces cooldown periods
const rateLimits = new Map<string, { attempts: number, timestamp: number }>();

export const checkRateLimit = (key: string, maxAttempts: number, windowMs: number): boolean => {
  const now = Date.now();
  const limit = rateLimits.get(key);
  
  // If no previous attempts or window expired, reset counter
  if (!limit || now - limit.timestamp > windowMs) {
    rateLimits.set(key, { attempts: 1, timestamp: now });
    return true;
  }
  
  // Check if max attempts reached
  if (limit.attempts >= maxAttempts) {
    return false;
  }
  
  // Increment attempts counter
  rateLimits.set(key, { attempts: limit.attempts + 1, timestamp: limit.timestamp });
  return true;
};

export const clearRateLimit = (key: string): void => {
  rateLimits.delete(key);
};

// Enhanced number validation for financial data
export const validateFinancialAmount = (value: string | number): boolean => {
  if (typeof value === 'number') {
    return value >= 0 && value <= 1000000000 && isFinite(value);
  }
  
  const numValue = parseFloat(value);
  if (isNaN(numValue) || !isFinite(numValue)) return false;
  
  // Maximum 1 billion with 2 decimal places
  return numValue >= 0 && numValue <= 1000000000 && /^\d+(\.\d{1,2})?$/.test(value);
};

// Validate dates for financial transactions with security checks
export const validateSecureDate = (date: string): boolean => {
  if (!date || typeof date !== 'string') return false;
  
  // Check format (YYYY-MM-DD)
  if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) return false;
  
  const parsedDate = new Date(date);
  if (isNaN(parsedDate.getTime())) return false;
  
  // Prevent future dates more than 1 year ahead (for scheduled transactions)
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
  
  // Prevent very old dates (more than 100 years ago)
  const oldestAllowed = new Date();
  oldestAllowed.setFullYear(oldestAllowed.getFullYear() - 100);
  
  return parsedDate <= oneYearFromNow && parsedDate >= oldestAllowed;
};

// Enhanced device fingerprinting for security monitoring
export const generateDeviceFingerprint = async (): Promise<string> => {
  try {
    const components = [
      navigator.userAgent,
      navigator.language,
      navigator.platform,
      `${screen.width}x${screen.height}x${screen.colorDepth}`,
      new Date().getTimezoneOffset(),
      navigator.hardwareConcurrency,
      // Remove deviceMemory as it's not widely supported
      navigator.cookieEnabled,
      "canvas:" + await getCanvasFingerprint(),
    ].filter(Boolean);

    const fingerprint = components.join('|');
    
    // Generate secure hash
    const msgBuffer = new TextEncoder().encode(fingerprint);
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    console.warn('Device fingerprinting failed:', error);
    return 'fingerprint-unavailable';
  }
};

// Canvas fingerprinting helper
const getCanvasFingerprint = async (): Promise<string> => {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return '';
    
    canvas.width = 200;
    canvas.height = 60;
    
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillStyle = '#123456';
    ctx.fillText('FinanzApp Security', 2, 2);
    
    return canvas.toDataURL().slice(-50);
  } catch (error) {
    return '';
  }
};
