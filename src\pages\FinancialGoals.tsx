
import { PiggyBank } from 'lucide-react';
import { FinancialGoalsSection } from '@/planificacion_metas/components/FinancialGoalsSection';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export default function FinancialGoalsPage() {
  const { financialGoals, isLoading } = useFinanceData(['goals']);
  const { isMobile } = useBreakpoint();

  if (isLoading && financialGoals.length === 0) {
    return (
      <div className={`space-y-4 md:space-y-6 animate-pulse ${isMobile ? 'p-3' : 'p-6'}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className={`bg-gray-200 rounded mb-2 ${isMobile ? 'h-6 w-32' : 'h-8 w-64'}`}></div>
            <div className={`bg-gray-200 rounded ${isMobile ? 'h-3 w-24' : 'h-4 w-48'}`}></div>
          </div>
          <div className={`bg-gray-200 rounded ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`}></div>
        </div>
        <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-4'}`}>
          {[1, 2, 3, 4].map(i => (
            <div key={i} className={`bg-gray-200 rounded-lg ${isMobile ? 'h-20' : 'h-24'}`}></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 md:space-y-6 ${isMobile ? 'p-3' : 'p-6'}`}>
      <div className={`flex items-center justify-between ${isMobile ? 'flex-col space-y-2' : ''}`}>
        <div className={`${isMobile ? 'text-center' : ''}`}>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-3xl'}`}>
            {isMobile ? 'Metas' : 'Metas Financieras'}
          </h1>
          <p className={`text-finanz-text-secondary ${isMobile ? 'text-xs' : ''}`}>
            {isMobile ? 'Define y sigue tus metas' : 'Define y sigue el progreso de tus metas financieras'}
          </p>
        </div>
        <PiggyBank className={`text-finanz-amber ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`} />
      </div>

      <FinancialGoalsSection />
    </div>
  );
}
