import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useSecureSignupForm } from './hooks/useSecureSignupForm';
import { SecurityAlert } from './components/SecurityAlert';
import { SignupFormFields } from './components/SignupFormFields';
import { PasswordStrengthIndicator } from './components/PasswordStrengthIndicator';
import { PasswordMatchIndicator } from './components/PasswordMatchIndicator';

interface SecureSignupFormProps {
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  onStepChange?: (step: number) => void;
}

export const SecureSignupForm: React.FC<SecureSignupFormProps> = ({ 
  isLoading, 
  setIsLoading,
  onStepChange 
}) => {
  const {
    signupData,
    passwordStrength,
    isPasswordCompromised,
    validationErrors,
    handleInputChange,
    handleSecureSignUp
  } = useSecureSignupForm(isLoading, setIsLoading);

  // Simular progreso basado en campos completados
  useEffect(() => {
    let step = 1;
    
    if (signupData.fullName && signupData.email) {
      step = 2;
    }
    if (signupData.password && signupData.confirmPassword) {
      step = 3;
    }
    if (passwordStrength.score >= 4 && !isPasswordCompromised) {
      step = 4;
    }
    
    onStepChange?.(step);
  }, [signupData, passwordStrength, isPasswordCompromised, onStepChange]);

  return (
    <form onSubmit={handleSecureSignUp} className="space-y-4">
      <SecurityAlert />

      <SignupFormFields
        signupData={signupData}
        validationErrors={validationErrors}
        isLoading={isLoading}
        onInputChange={handleInputChange}
      />

      <PasswordStrengthIndicator
        password={signupData.password}
        passwordStrength={passwordStrength}
        isPasswordCompromised={isPasswordCompromised}
      />

      <PasswordMatchIndicator
        password={signupData.password}
        confirmPassword={signupData.confirmPassword}
      />

      <Button 
        type="submit" 
        className="w-full" 
        disabled={isLoading || passwordStrength.score < 4 || isPasswordCompromised}
      >
        {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
        Crear Cuenta Segura
      </Button>
      
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Mínimo 8 caracteres con mayúsculas, minúsculas, números y símbolos</p>
        <p>• Sin patrones comunes o contraseñas comprometidas</p>
        <p>• Todos los datos son cifrados y protegidos</p>
      </div>
    </form>
  );
};
