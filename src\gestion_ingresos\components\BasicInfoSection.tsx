
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Income as IncomeType } from '@/types';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface BasicInfoSectionProps {
  formData: Partial<IncomeType>;
  setFormData: React.Dispatch<React.SetStateAction<Partial<IncomeType>>>;
  isSubmitting?: boolean;
}

export function BasicInfoSection({ formData, setFormData, isSubmitting = false }: BasicInfoSectionProps) {
  const { isMobile, isTablet } = useBreakpoint();
  
  const handleMonthChange = (value: string) => {
    setFormData(prev => ({ ...prev, month: value }));
  };

  const handleCurrencyChange = (value: 'DOP' | 'USD') => {
    setFormData(prev => ({ ...prev, currency: value }));
  };

  return (
    <Card>
      <CardHeader className={isMobile ? 'pb-2' : ''}>
        <CardTitle className={`
          ${isMobile ? 'text-base' : 'text-lg'}
        `}>
          Información Básica
        </CardTitle>
      </CardHeader>
      <CardContent className={`
        ${isMobile ? 'space-y-3 pt-0' : 'space-y-4'}
      `}>
        <div className={`
          grid gap-4
          ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'}
        `}>
          <div className={`
            ${isMobile ? 'space-y-1' : 'space-y-2'}
          `}>
            <Label 
              htmlFor="month"
              className={isMobile ? 'text-xs font-medium' : ''}
            >
              Mes*
            </Label>
            <Input
              id="month"
              type="month"
              value={formData.month || ''}
              onChange={(e) => handleMonthChange(e.target.value)}
              required
              disabled={isSubmitting}
              className={isMobile ? 'h-8 text-sm' : ''}
              autoComplete="off"
            />
          </div>
          
          <div className={`
            ${isMobile ? 'space-y-1' : 'space-y-2'}
          `}>
            <Label 
              htmlFor="currency-select"
              className={isMobile ? 'text-xs font-medium' : ''}
            >
              Moneda*
            </Label>
            <Select 
              value={formData.currency || 'DOP'} 
              onValueChange={handleCurrencyChange} 
              disabled={isSubmitting}
            >
              <SelectTrigger id="currency-select" className={isMobile ? 'h-8 text-sm' : ''} autoComplete="off">
                <SelectValue placeholder="Seleccionar moneda" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="DOP">DOP (Peso Dominicano)</SelectItem>
                <SelectItem value="USD">USD (Dólar Americano)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
