import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { BrowserRouter } from 'react-router-dom'
import { TopBar } from '@/components/TopBar'

// mock dependencies
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: '1' }, loading: false })
}))
vi.mock('@/hooks/useBreakpoint', () => ({ useBreakpoint: () => ({ isMobile: true, isTablet: false }) }))
vi.mock('@/components/security/SecurityProvider', () => ({ SecurityProvider: ({ children }: any) => <>{children}</> }))

// mock SidebarTrigger and UserMenu to simplify
vi.mock('@/components/ui/sidebar', () => ({ SidebarTrigger: (props: any) => <button {...props}>menu</button> }))
vi.mock('@/components/UserMenu', () => ({ UserMenu: () => <div>UserMenu</div> }))

describe('TopBar', () => {
  it('renders brand name on mobile', () => {
    render(
      <BrowserRouter>
        <TopBar />
      </BrowserRouter>
    )
    expect(screen.getByText('FinanzApp')).toBeInTheDocument()
    expect(screen.getByText('UserMenu')).toBeInTheDocument()
  })
})
