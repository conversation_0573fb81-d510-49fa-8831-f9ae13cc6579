
import { SecureEncryption } from './encryption';
import { auditLog } from '@/utils/securityUtils';

// Enhanced secure storage with proper encryption
class SecureStorage {
  private static readonly STORAGE_KEY_PREFIX = 'finanz_secure_';
  private static readonly ENCRYPTION_PASSWORD = 'finanz_app_secure_key_v2';
  
  // Encrypt and store data securely
  async setSecure(key: string, value: any): Promise<void> {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      const encrypted = await SecureEncryption.encrypt(stringValue, this.getEncryptionKey());
      
      sessionStorage.setItem(
        `${SecureStorage.STORAGE_KEY_PREFIX}${key}`, 
        encrypted
      );
      
      auditLog('secure_storage_write', { key }, undefined, 'low');
    } catch (error) {
      console.error('Secure storage set error:', error);
      auditLog('secure_storage_error', { 
        operation: 'set', 
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, undefined, 'medium');
      throw new Error('Failed to store data securely');
    }
  }

  // Retrieve and decrypt data securely
  async getSecure(key: string): Promise<string | null> {
    try {
      const encrypted = sessionStorage.getItem(`${SecureStorage.STORAGE_KEY_PREFIX}${key}`);
      if (!encrypted) return null;
      
      const decrypted = await SecureEncryption.decrypt(encrypted, this.getEncryptionKey());
      auditLog('secure_storage_read', { key }, undefined, 'low');
      return decrypted;
    } catch (error) {
      console.error('Secure storage get error:', error);
      auditLog('secure_storage_error', { 
        operation: 'get', 
        key,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, undefined, 'medium');
      return null;
    }
  }

  // Remove secure data
  removeSecure(key: string): void {
    sessionStorage.removeItem(`${SecureStorage.STORAGE_KEY_PREFIX}${key}`);
    auditLog('secure_storage_remove', { key }, undefined, 'low');
  }

  // Clear all secure data
  clearAllSecure(): void {
    const keys = Object.keys(sessionStorage);
    let removedCount = 0;
    
    keys.forEach(key => {
      if (key.startsWith(SecureStorage.STORAGE_KEY_PREFIX)) {
        sessionStorage.removeItem(key);
        removedCount++;
      }
    });
    
    auditLog('secure_storage_clear_all', { removedCount }, undefined, 'medium');
  }

  // Enhanced token management with proper encryption
  async setSecureToken(token: string): Promise<void> {
    try {
      await this.setSecure('auth_token', token);
      await this.setSecure('token_timestamp', Date.now().toString());
      await this.setSecure('token_hash', await SecureEncryption.hash(token));
    } catch (error) {
      auditLog('token_storage_failed', { 
        error: error instanceof Error ? error.message : 'Unknown error'
      }, undefined, 'high');
      throw error;
    }
  }

  async getSecureToken(): Promise<string | null> {
    try {
      const token = await this.getSecure('auth_token');
      const timestamp = await this.getSecure('token_timestamp');
      const storedHash = await this.getSecure('token_hash');
      
      if (!token || !timestamp || !storedHash) return null;
      
      // Verify token integrity
      const currentHash = await SecureEncryption.hash(token);
      if (currentHash !== storedHash) {
        auditLog('token_integrity_violation', {}, undefined, 'high');
        this.clearTokens();
        return null;
      }
      
      // Check if token is older than 1 hour
      const tokenAge = Date.now() - parseInt(timestamp);
      if (tokenAge > 60 * 60 * 1000) {
        this.clearTokens();
        return null;
      }
      
      return token;
    } catch (error) {
      auditLog('token_retrieval_failed', { 
        error: error instanceof Error ? error.message : 'Unknown error'
      }, undefined, 'medium');
      return null;
    }
  }

  private clearTokens(): void {
    this.removeSecure('auth_token');
    this.removeSecure('token_timestamp');
    this.removeSecure('token_hash');
  }

  // Enhanced device fingerprinting
  async setDeviceFingerprint(): Promise<string> {
    const fingerprint = await this.generateDeviceFingerprint();
    await this.setSecure('device_fingerprint', fingerprint);
    await this.setSecure('device_registered', new Date().toISOString());
    return fingerprint;
  }

  async validateDeviceFingerprint(): Promise<boolean> {
    try {
      const stored = await this.getSecure('device_fingerprint');
      const current = await this.generateDeviceFingerprint();
      
      if (!stored || stored !== current) {
        auditLog('device_fingerprint_mismatch', {}, undefined, 'high');
        return false;
      }
      
      return true;
    } catch (error) {
      auditLog('device_validation_failed', { 
        error: error instanceof Error ? error.message : 'Unknown error'
      }, undefined, 'medium');
      return false;
    }
  }

  private async generateDeviceFingerprint(): Promise<string> {
    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      navigator.platform,
      navigator.hardwareConcurrency?.toString() || '0',
      navigator.maxTouchPoints?.toString() || '0'
    ];
    
    return await SecureEncryption.hash(components.join('|'));
  }

  private getEncryptionKey(): string {
    // In production, this should come from a secure source
    return SecureStorage.ENCRYPTION_PASSWORD + window.location.origin;
  }
}

export const secureStorage = new SecureStorage();
