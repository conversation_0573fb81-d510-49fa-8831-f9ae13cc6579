
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { emailSchema, passwordSchema, sanitizeEmail } from '@/utils/security/inputValidation';
import { checkRateLimit, clearRateLimit } from '@/utils/security/inputValidation';

export const useSecureAuthActions = () => {
  const [isLoading, setIsLoading] = useState(false);

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Rate limiting check
      const rateLimitKey = `login_${sanitizeEmail(email)}`;
      if (!checkRateLimit(rateLimitKey, 5, 900000)) { // 5 attempts per 15 minutes
        toast.error('Demasiados intentos de inicio de sesión. Intenta en 15 minutos.');
        return { error: new Error('Rate limit exceeded') };
      }

      // Input validation
      const emailValidation = emailSchema.safeParse(email);
      if (!emailValidation.success) {
        toast.error('Email no válido');
        return { error: new Error('Invalid email') };
      }

      if (!password || password.length < 6) {
        toast.error('Contraseña requerida');
        return { error: new Error('Password required') };
      }

      const sanitizedEmail = sanitizeEmail(email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: sanitizedEmail,
        password,
      });

      if (error) {
        // Generic error message for security
        toast.error('Credenciales incorrectas');
        return { error };
      }

      // Clear rate limit on successful login
      clearRateLimit(rateLimitKey);
      
      // Check if email is verified
      if (data.user && !data.user.email_confirmed_at) {
        toast.warning('Email no verificado. Revisa tu bandeja de entrada.');
      } else {
        toast.success('Sesión iniciada correctamente');
      }
      
      return { data, error: null };

    } catch (error) {
      console.error('Sign in error:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
      return { error: error as Error };
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName?: string) => {
    setIsLoading(true);
    
    try {
      // Rate limiting check
      const rateLimitKey = `signup_${sanitizeEmail(email)}`;
      if (!checkRateLimit(rateLimitKey, 3, 3600000)) { // 3 attempts per hour
        toast.error('Demasiados intentos de registro. Intenta en 1 hora.');
        return { error: new Error('Rate limit exceeded') };
      }

      // Input validation
      const emailValidation = emailSchema.safeParse(email);
      if (!emailValidation.success) {
        toast.error('Email no válido');
        return { error: new Error('Invalid email') };
      }

      const passwordValidation = passwordSchema.safeParse(password);
      if (!passwordValidation.success) {
        toast.error(passwordValidation.error.errors[0].message);
        return { error: new Error('Invalid password') };
      }

      const sanitizedEmail = sanitizeEmail(email);
      const redirectUrl = `${window.location.origin}/`;
      
      const { data, error } = await supabase.auth.signUp({
        email: sanitizedEmail,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: fullName || '',
          }
        }
      });

      if (error) {
        if (error.message.includes('already registered')) {
          toast.error('Este email ya está registrado');
        } else {
          toast.error('Error al crear la cuenta. Intenta nuevamente.');
        }
        return { error };
      }

      clearRateLimit(rateLimitKey);
      toast.success('Cuenta creada. Revisa tu email para confirmar.');
      return { data, error: null };

    } catch (error) {
      console.error('Sign up error:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
      return { error: error as Error };
    } finally {
      setIsLoading(false);
    }
  };

  const resendVerification = async (email: string) => {
    setIsLoading(true);
    
    try {
      const sanitizedEmail = sanitizeEmail(email);
      const redirectUrl = `${window.location.origin}/`;
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: sanitizedEmail,
        options: {
          emailRedirectTo: redirectUrl
        }
      });

      if (error) {
        toast.error('Error al reenviar email de verificación');
        return { error };
      }

      toast.success('Email de verificación reenviado');
      return { error: null };

    } catch (error) {
      console.error('Resend verification error:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
      return { error: error as Error };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    setIsLoading(true);
    
    try {
      const emailValidation = emailSchema.safeParse(email);
      if (!emailValidation.success) {
        toast.error('Email no válido');
        return { error: new Error('Invalid email') };
      }

      const sanitizedEmail = sanitizeEmail(email);
      const redirectUrl = `${window.location.origin}/auth/reset-password`;
      
      const { error } = await supabase.auth.resetPasswordForEmail(sanitizedEmail, {
        redirectTo: redirectUrl,
      });

      if (error) {
        toast.error('Error al enviar email de recuperación');
        return { error };
      }

      toast.success('Email de recuperación enviado');
      return { error: null };

    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
      return { error: error as Error };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setIsLoading(true);
    
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        toast.error('Error al cerrar sesión');
        return { error };
      }

      // Clear all secure storage
      const { secureStorage } = await import('@/utils/security/secureStorage');
      secureStorage.clearAllSecure();
      
      toast.success('Sesión cerrada correctamente');
      return { error: null };

    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Error al cerrar sesión');
      return { error: error as Error };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signIn,
    signUp,
    signOut,
    resendVerification,
    resetPassword,
    isLoading
  };
};
