
import { logger } from "@/utils/logger";

import { useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Profile } from './types';

export const useAuthState = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLocked, setIsLocked] = useState(false);

  const clearAuthState = () => {
    setUser(null);
    setSession(null);
    setProfile(null);
    setIsLocked(false);
    
    // Limpiar datos de actividad
    localStorage.removeItem('finanz_last_activity');
  };

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile:', error);
        return;
      }

      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  // Inicialización del estado de autenticación
  useEffect(() => {
    logger.debug('useAuthState: Initializing auth state...');

    let mounted = true;

    // Configurar listener de cambios de auth
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;
        
        logger.debug('useAuthState: Auth state changed:', event, !!session);
        
        // Solo limpiar estado en logout explícito o token invalid
        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' && !session) {
          if (mounted) {
            clearAuthState();
            setLoading(false);
          }
          return;
        }
        
        // Validar que la sesión sea válida y no haya expirado
        if (session && session.expires_at) {
          const now = Math.floor(Date.now() / 1000);
          const expiresAt = session.expires_at;
          
          if (now >= expiresAt) {
            logger.debug('useAuthState: Session expired, clearing state');
            if (mounted) {
              clearAuthState();
              setLoading(false);
            }
            return;
          }
        }
        
        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          
          if (session?.user) {
            // Fetch profile in background with a small delay
            setTimeout(() => {
              if (mounted) {
                fetchProfile(session.user.id);
              }
            }, 100);
          } else {
            setProfile(null);
          }
          
          // Set loading to false with a small delay to prevent flash
          setTimeout(() => {
            if (mounted) {
              setLoading(false);
            }
          }, 500);
        }
      }
    );

    // Obtener sesión inicial
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (!mounted) return;
        
        if (error) {
          console.error('useAuthState: Error getting initial session:', error);
          // No limpiar estado en errores de red
          if (error.message?.includes('Failed to fetch')) {
            logger.debug('useAuthState: Network error, keeping existing state');
          } else {
            clearAuthState();
          }
        } else {
          logger.debug('useAuthState: Initial session loaded:', !!session);
          
          // Validar que la sesión inicial no haya expirado
          if (session && session.expires_at) {
            const now = Math.floor(Date.now() / 1000);
            const expiresAt = session.expires_at;
            
            if (now >= expiresAt) {
              logger.debug('useAuthState: Initial session expired, clearing state');
              clearAuthState();
              setLoading(false);
              return;
            }
          }
          
          setSession(session);
          setUser(session?.user ?? null);
          
          if (session?.user) {
            setTimeout(() => {
              if (mounted) {
                fetchProfile(session.user.id);
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('useAuthState: Error in getSession:', error);
        // No limpiar estado en errores de conexión
        if (mounted && !error.message?.includes('Failed to fetch')) {
          clearAuthState();
        }
      } finally {
        if (mounted) {
          // Set a minimum loading time to prevent flashing
          setTimeout(() => {
            if (mounted) {
              setLoading(false);
            }
          }, 1000); // Increased from 500ms
        }
      }
    };

    getInitialSession();

    return () => {
      mounted = false;
      logger.debug('useAuthState: Cleaning up auth subscription');
      subscription.unsubscribe();
    };
  }, []);

  return {
    user,
    setUser,
    session,
    setSession,
    profile,
    setProfile,
    loading,
    setLoading,
    isLocked,
    setIsLocked,
    clearAuthState,
    fetchProfile,
  };
};
