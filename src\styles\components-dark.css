
@layer base {
  /* Light mode component styles */
  
  /* Cards */
  .bg-card {
    background-color: white;
    border-color: rgb(229 231 235);
  }

  .card-header {
    color: rgb(17 24 39);
  }

  /* Buttons */
  .bg-primary {
    background-color: #1f2937 !important;
    color: white !important;
  }

  .bg-primary:hover {
    background-color: rgb(31 41 55);
  }

  button:hover {
    background-color: rgb(243 244 246);
  }

  .button-secondary {
    background-color: rgb(229 231 235);
    color: rgb(17 24 39);
  }

  .button-secondary:hover {
    background-color: rgb(209 213 219);
  }

  /* Dropdowns and Selects */
  [data-radix-select-content] {
    background-color: white;
    border-color: rgb(229 231 235);
    z-index: 50;
  }
  
  [data-radix-select-item] {
    color: rgb(17 24 39);
  }

  [data-radix-select-item]:hover,
  [data-radix-select-item]:focus {
    background-color: rgb(243 244 246);
  }

  [data-radix-select-trigger] {
    background-color: white;
    border-color: rgb(209 213 219);
    color: rgb(17 24 39);
  }

  /* Tabs */
  [data-radix-tabs-list] {
    background-color: rgb(249 250 251);
  }

  [data-radix-tabs-trigger] {
    color: rgb(75 85 99);
  }

  [data-radix-tabs-trigger][data-state=active] {
    color: rgb(17 24 39);
    background-color: white;
  }

  /* Switches */
  [data-radix-switch-root] {
    background-color: hsl(0 0% 80%);
  }

  [data-radix-switch-root][data-state=checked] {
    background-color: #1f2937;
  }

  [data-radix-switch-thumb] {
    background-color: white !important;
  }

  [data-radix-switch-root][data-state=checked] [data-radix-switch-thumb] {
    background-color: white !important;
  }

  /* Popovers and Tooltips */
  [data-radix-popover-content] {
    background-color: white;
    border-color: rgb(229 231 235);
    color: rgb(17 24 39);
  }

  [data-radix-tooltip-content] {
    background-color: rgb(31 41 55);
    color: white;
    border-color: rgb(75 85 99);
  }

  /* Modals and Dialogs */
  [data-radix-dialog-content] {
    background-color: white;
    border-color: rgb(229 231 235);
    color: rgb(17 24 39);
  }

  [data-radix-alert-dialog-content] {
    background-color: white;
    border-color: rgb(229 231 235);
    color: rgb(17 24 39);
  }

  /* Dark mode component styles */
  
  /* Cards */
  .dark .bg-card {
    background-color: rgb(17 24 39);
    border-color: rgb(55 65 81);
    color: white !important;
  }

  .dark .card-header {
    color: white;
  }

  /* Card content text visibility */
  .dark [data-radix-card] {
    background-color: rgb(17 24 39) !important;
    color: white !important;
  }

  .dark .bg-card,
  .dark [class*="bg-card"] {
    background-color: rgb(17 24 39) !important;
    color: white !important;
  }

  /* Ensure all card text is visible */
  .dark .bg-card *,
  .dark [data-radix-card] * {
    color: inherit;
  }

  .dark .bg-card h1, .dark .bg-card h2, .dark .bg-card h3, 
  .dark .bg-card h4, .dark .bg-card h5, .dark .bg-card h6 {
    color: white;
  }

  .dark .bg-card p, .dark .bg-card span, .dark .bg-card div {
    color: rgb(229 231 235);
  }

  /* Buttons */
  .dark .bg-primary {
    background-color: white !important;
    color: black !important;
  }

  .dark .bg-primary:hover {
    background-color: rgb(229 231 235);
  }

  .dark button:hover {
    background-color: rgb(31 41 55);
  }

  .dark .button-secondary {
    background-color: rgb(31 41 55);
    color: white;
  }

  .dark .button-secondary:hover {
    background-color: rgb(55 65 81);
  }

  /* Dropdowns and Selects */
  .dark [data-radix-select-content] {
    background-color: rgb(17 24 39);
    border-color: rgb(75 85 99);
    z-index: 50;
  }
  
  .dark [data-radix-select-item] {
    color: white;
  }

  .dark [data-radix-select-item]:hover,
  .dark [data-radix-select-item]:focus {
    background-color: rgb(55 65 81);
  }

  .dark [data-radix-select-trigger] {
    background-color: rgb(17 24 39);
    border-color: rgb(75 85 99);
    color: white;
  }

  /* Tabs */
  .dark [data-radix-tabs-list] {
    background-color: rgb(17 24 39);
  }

  .dark [data-radix-tabs-trigger] {
    color: rgb(209 213 219);
  }

  .dark [data-radix-tabs-trigger][data-state=active] {
    color: white;
    background-color: rgb(55 65 81);
  }

  /* Switches */
  .dark [data-radix-switch-root] {
    background-color: hsl(0 0% 40%);
  }

  .dark [data-radix-switch-root][data-state=checked] {
    background-color: white;
  }

  .dark [data-radix-switch-thumb] {
    background-color: white !important;
  }

  .dark [data-radix-switch-root][data-state=checked] [data-radix-switch-thumb] {
    background-color: black !important;
  }

  /* Popovers and Tooltips */
  .dark [data-radix-popover-content] {
    background-color: rgb(17 24 39);
    border-color: rgb(75 85 81);
    color: white;
  }

  .dark [data-radix-tooltip-content] {
    background-color: rgb(31 41 55);
    color: white;
    border-color: rgb(75 85 99);
  }

  /* Modals and Dialogs - CORREGIDO */
  .dark [data-radix-dialog-content] {
    background-color: rgb(17 24 39) !important;
    border-color: rgb(75 85 99) !important;
    color: white !important;
  }

  .dark [data-radix-alert-dialog-content] {
    background-color: rgb(17 24 39) !important;
    border-color: rgb(75 85 99) !important;
    color: white !important;
  }

  /* Iconos en modales - NUEVO */
  .dark [data-radix-dialog-content] svg,
  .dark [data-radix-alert-dialog-content] svg,
  .dark [data-radix-dialog-content] .lucide,
  .dark [data-radix-alert-dialog-content] .lucide {
    color: white !important;
    fill: currentColor !important;
  }

  /* Botón de cerrar en modales */
  .dark [data-radix-dialog-close] svg,
  .dark [data-radix-alert-dialog-close] svg {
    color: rgb(209 213 219) !important;
  }

  .dark [data-radix-dialog-close]:hover svg,
  .dark [data-radix-alert-dialog-close]:hover svg {
    color: white !important;
  }

  /* Títulos y descripciones en modales */
  .dark [data-radix-dialog-title],
  .dark [data-radix-alert-dialog-title] {
    color: white !important;
  }

  .dark [data-radix-dialog-description],
  .dark [data-radix-alert-dialog-description] {
    color: rgb(209 213 219) !important;
  }

  /* Progress bars */
  .dark [data-radix-progress-root] {
    background-color: rgb(55 65 81);
  }

  .dark [data-radix-progress-indicator] {
    background-color: white;
  }

  /* Badges and status indicators */
  .dark .badge,
  .dark [data-radix-badge] {
    color: white;
  }

  /* Specific improvements for diagnostic page elements */
  .dark .text-finanz-success {
    color: rgb(74 222 128) !important;
  }

  .dark .text-finanz-danger {
    color: rgb(248 113 113) !important;
  }

  .dark .text-finanz-warning {
    color: rgb(251 191 36) !important;
  }

  .dark .text-finanz-primary {
    color: rgb(96 165 250) !important;
  }

  .dark .text-finanz-purple {
    color: rgb(196 181 253) !important;
  }

  .dark .text-finanz-indigo {
    color: rgb(165 180 252) !important;
  }

  /* Ensure metric cards are visible */
  .dark .border-finanz-border {
    border-color: rgb(75 85 99);
  }

  .dark .bg-finanz-success\/10 {
    background-color: rgba(34, 197, 94, 0.3);
  }

  .dark .bg-finanz-danger\/10 {
    background-color: rgba(239, 68, 68, 0.3);
  }

  .dark .bg-finanz-warning\/10 {
    background-color: rgba(245, 158, 11, 0.3);
  }

  .dark .bg-finanz-purple\/10 {
    background-color: rgba(147, 51, 234, 0.3);
  }

  .dark .bg-finanz-indigo\/10 {
    background-color: rgba(99, 102, 241, 0.3);
  }

  /* Iconos específicos para modales de asesoría */
  .dark .text-green-600 {
    color: rgb(34 197 94) !important;
  }

  .dark .text-yellow-600 {
    color: rgb(234 179 8) !important;
  }

  .dark .text-red-600 {
    color: rgb(239 68 68) !important;
  }

  .dark .text-blue-600 {
    color: rgb(37 99 235) !important;
  }

  .dark .text-gray-600 {
    color: rgb(156 163 175) !important;
  }

  /* Asegurar visibilidad de todos los iconos en modales */
  .dark [data-radix-dialog-content] .w-6,
  .dark [data-radix-dialog-content] .w-5,
  .dark [data-radix-dialog-content] .w-4 {
    color: currentColor !important;
  }
}
