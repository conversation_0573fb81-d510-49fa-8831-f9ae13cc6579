
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Lock, Eye, EyeOff, Loader2 } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { SecurityFormData } from '../schemas/settingsSchemas';

interface PasswordChangeSectionProps {
  securityForm: UseFormReturn<SecurityFormData>;
  handleChangePassword: () => Promise<void>;
  passwordLoading: boolean;
}

export const PasswordChangeSection: React.FC<PasswordChangeSectionProps> = ({
  securityForm,
  handleChangePassword,
  passwordLoading,
}) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="w-5 h-5" />
          Contraseña
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...securityForm}>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={securityForm.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contraseña Actual</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input
                          {...field}
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Contraseña actual"
                          autoComplete="current-password"
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={securityForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nueva Contraseña</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="Nueva contraseña"
                        autoComplete="new-password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button 
              type="button" 
              onClick={handleChangePassword} 
              disabled={passwordLoading} 
              className="w-full md:w-auto"
            >
              {passwordLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Cambiar Contraseña
            </Button>
          </div>
        </Form>
      </CardContent>
    </Card>
  );
};
