import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExpenseDataFixService } from '@/seguimiento_pagos/services/expenseDataFixService';
import { useAuth } from '@/contexts/AuthContext';
import { useQueryClient } from '@tanstack/react-query';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

export const DataFixComponent: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleRunFixes = async () => {
    if (!user?.id) {
      setError('Usuario no autenticado');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults(null);

    try {
      const fixResults = await ExpenseDataFixService.runAllFixes(user.id);
      setResults(fixResults);
      
      // Invalidar todas las queries relacionadas para refrescar la UI
      await queryClient.invalidateQueries();
      
    } catch (err: any) {
      setError(err.message || 'Error al ejecutar las correcciones');
      console.error('Error ejecutando correcciones:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Corrección de Datos</h2>
      
      <div className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Esta herramienta corregirá:
            <ul className="list-disc list-inside mt-2">
              <li>Fechas de pago faltantes en gastos</li>
              <li>Descripciones vacías en gastos</li>
              <li>Descripciones genéricas en pagos de julio</li>
            </ul>
          </AlertDescription>
        </Alert>

        <Button
          onClick={handleRunFixes}
          disabled={isLoading || !user}
          className="w-full"
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isLoading ? 'Ejecutando correcciones...' : 'Ejecutar Correcciones'}
        </Button>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {results && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Correcciones completadas:</strong>
              <ul className="list-disc list-inside mt-2">
                <li>Fechas de pago corregidas: {results.paymentDates.updated}</li>
                <li>Descripciones añadidas a gastos: {results.descriptions.updated}</li>
                <li>Descripciones de pagos actualizadas: {results.paymentDescriptions.updated}</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </div>
    </Card>
  );
};