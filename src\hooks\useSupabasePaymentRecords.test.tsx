import React from 'react'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { describe, it, expect, vi, beforeEach, beforeAll } from 'vitest'

// Se importará dinámicamente después de configurar los mocks
let useSupabasePaymentRecords: typeof import('@/hooks/useSupabasePaymentRecords').useSupabasePaymentRecords;

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'user1' } })
}))

interface MockDBPaymentRecord {
  id: string;
  payment_type: string;
  reference_id: string;
  due_date: string;
  amount: number;
  currency: string;
  status: string;
  paid_date: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

let update: vi.Mock
let eq: vi.Mock
let select: vi.Mock
let single: vi.Mock<Promise<{ data: MockDBPaymentRecord | null; error: Error | null }>>
let from: vi.Mock

vi.mock('@/integrations/supabase/client', () => {
  update = vi.fn(() => ({ eq, select, single }));
  eq = vi.fn(() => ({ eq, select, single })); // eq can return itself for chaining or the next step
  select = vi.fn(() => ({ single }));
  single = vi.fn().mockResolvedValue({
    data: {
      id: 'rec1',
      payment_type: 'expense',
      reference_id: 'ref',
      due_date: '2024-01-01',
      amount: 100,
      currency: 'DOP',
      status: 'paid',
      paid_date: '2024-01-02',
      notes: 'note',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as MockDBPaymentRecord,
    error: null
  });
  from = vi.fn(() => ({ update }));

  return { supabase: { from } };
})

beforeAll(async () => {
  const module = await import('@/hooks/useSupabasePaymentRecords');
  useSupabasePaymentRecords = module.useSupabasePaymentRecords;
});

beforeEach(() => {
  update.mockClear()
  eq.mockClear()
  select.mockClear()
  single.mockClear()
  from.mockClear()
})

describe('useSupabasePaymentRecords.markPaymentAsPaid', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={new QueryClient()}>{children}</QueryClientProvider>
  )

  it('saves notes when provided', async () => {
    const { result } = renderHook(() => useSupabasePaymentRecords({ enabled: false }), { wrapper })

    await act(async () => {
      result.current.markPaymentAsPaid('rec1', '2024-01-02', 'test')
    })

    expect(update).toHaveBeenCalledWith({ status: 'paid', paid_date: '2024-01-02', notes: 'test' })
  })

  it('marks as paid without notes', async () => {
    const { result } = renderHook(() => useSupabasePaymentRecords({ enabled: false }), { wrapper })

    await act(async () => {
      result.current.markPaymentAsPaid('rec1', '2024-01-03')
    })

    expect(update).toHaveBeenCalledWith({ status: 'paid', paid_date: '2024-01-03' })
  })
})
