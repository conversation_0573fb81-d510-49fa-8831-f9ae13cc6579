import { TemporalPeriod } from '../hooks/useTemporalNavigation';

/**
 * Formats a date object into YYYY-MM-DD string.
 */
const formatDateToString = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

/**
 * Generates a display name for a given month and year.
 * e.g., "Julio 2024"
 */
const getMonthDisplayName = (month: number, year: number, locale: string = 'es-ES'): string => {
  const date = new Date(year, month - 1, 1); // month is 1-indexed
  return date.toLocaleString(locale, { month: 'long', year: 'numeric' });
};

/**
 * Calculates the start and end dates for a given year and 1-indexed month.
 */
export const getFormattedPeriod = (year: number, month: number): Pick<TemporalPeriod, 'startDate' | 'endDate' | 'displayName' | 'year' | 'month'> => {
  const startDate = new Date(year, month - 1, 1); // month is 1-indexed for input, 0-indexed for Date
  const endDate = new Date(year, month, 0); // Day 0 of next month gives last day of current month

  return {
    year,
    month, // Store 1-indexed month
    startDate: formatDateToString(startDate),
    endDate: formatDateToString(endDate),
    displayName: getMonthDisplayName(month, year),
  };
};


/**
 * Calculates the previous period from a given TemporalPeriod.
 * Returns null if the given period is such that a previous period cannot be determined (e.g., year 1 month 1).
 */
export const getPreviousPeriod = (currentPeriod: TemporalPeriod): TemporalPeriod | null => {
  if (!currentPeriod) return null;

  let prevYear = currentPeriod.year;
  let prevMonth = currentPeriod.month - 1; // currentPeriod.month is 1-indexed

  if (prevMonth === 0) { // If current month is January (1), previous is December of last year
    prevMonth = 12;
    prevYear -= 1;
  }

  // Basic sanity check for year (you might have a defined earliest year in your app)
  if (prevYear < 1900) { // Or whatever your app's practical minimum year is
    return null;
  }

  const prevPeriodDetails = getFormattedPeriod(prevYear, prevMonth);
  const today = new Date();
  today.setHours(0,0,0,0);
  const prevPeriodDate = new Date(prevYear, prevMonth -1, 1);

  return {
    ...prevPeriodDetails,
    isFutureMonth: prevPeriodDate > today, // This will always be false for a previous period
    isCurrentMonth: prevPeriodDate.getFullYear() === today.getFullYear() && prevPeriodDate.getMonth() === today.getMonth(), // Also likely false
    displayMode: 'month', // Assuming default display mode
  };
};

/**
 * Validates temporal consistency - ensures that an entity's creation date is not after a target period.
 * This prevents showing items as overdue in months before they were created.
 * 
 * @param createdAt - Creation date in YYYY-MM-DD format
 * @param targetYear - Target period year
 * @param targetMonth - Target period month (1-indexed)
 * @returns true if temporally consistent, false otherwise
 */
export const isTemporallyConsistent = (createdAt: string | undefined, targetYear: number, targetMonth: number): boolean => {
  if (!createdAt) return true; // If no creation date, allow (legacy data)
  
  const createdYearMonth = createdAt.slice(0, 7); // YYYY-MM
  const targetYearMonth = `${targetYear}-${String(targetMonth).padStart(2, '0')}`;
  
  return createdYearMonth <= targetYearMonth;
};

/**
 * Checks if the given period is the actual current month of the system.
 * This is critical for rollover logic - only show carried-over payments in the real current month.
 * 
 * @param period - TemporalPeriod to check
 * @returns true if the period is the actual current month
 */
export const isActualCurrentMonth = (period: TemporalPeriod): boolean => {
  const now = new Date();
  const actualCurrentYear = now.getFullYear();
  const actualCurrentMonth = now.getMonth() + 1; // Convert from 0-11 to 1-12
  
  return period.year === actualCurrentYear && period.month === actualCurrentMonth;
};

/**
 * Validates that an expense's due date is not before its creation date.
 * This helps detect data integrity issues.
 * 
 * @param createdAt - Creation date in YYYY-MM-DD format
 * @param dueDate - Due date in YYYY-MM-DD format
 * @returns true if dates are logically consistent
 */
export const isDateLogicallyConsistent = (createdAt: string | undefined, dueDate: string): boolean => {
  if (!createdAt) return true; // If no creation date, allow (legacy data)
  
  const createdAtDate = new Date(createdAt + "T00:00:00");
  const dueDateObj = new Date(dueDate + "T00:00:00");
  
  return createdAtDate <= dueDateObj;
};

/**
 * Validates temporal integrity of an expense
 * Returns true if the expense passes all temporal validations
 */
export const validateExpenseTemporalIntegrity = (expense: any): boolean => {
  // Check if expense has required date fields
  if (!expense.createdAt) {
    console.warn(`Expense ${expense.id} lacks createdAt field`);
    return false;
  }
  
  if (!expense.date && !expense.paymentDate) {
    console.warn(`Expense ${expense.id} lacks both date and paymentDate`);
    return false;
  }
  
  // Parse dates
  const createdAt = new Date(expense.createdAt);
  const dueDate = new Date(expense.paymentDate || expense.date);
  
  // Validate dates are valid
  if (isNaN(createdAt.getTime())) {
    console.warn(`Expense ${expense.id} has invalid createdAt: ${expense.createdAt}`);
    return false;
  }
  
  if (isNaN(dueDate.getTime())) {
    console.warn(`Expense ${expense.id} has invalid due date: ${expense.paymentDate || expense.date}`);
    return false;
  }
  
  // Critical validation: Creation date cannot be after due date for non-recurring expenses
  if (!expense.isRecurring && createdAt > dueDate) {
    console.warn(`Expense ${expense.id} has createdAt (${expense.createdAt}) after due date (${expense.paymentDate || expense.date})`);
    return false;
  }
  
  return true;
};

/**
 * Checks if an expense can appear in a given period
 * Returns true if the expense is valid for the period
 */
export const canExpenseAppearInPeriod = (
  expense: any,
  targetYear: number,
  targetMonth: number // 1-12
): boolean => {
  if (!validateExpenseTemporalIntegrity(expense)) {
    return false;
  }
  
  const createdAt = new Date(expense.createdAt);
  const targetPeriodEnd = new Date(targetYear, targetMonth - 1, 0, 23, 59, 59, 999);
  
  // Expense cannot appear in a period before it was created
  if (createdAt > targetPeriodEnd) {
    return false;
  }
  
  // For non-recurring expenses, check if the due date is in the target period
  if (!expense.isRecurring) {
    const dueDate = new Date(expense.paymentDate || expense.date);
    const dueDateYear = dueDate.getFullYear();
    const dueDateMonth = dueDate.getMonth() + 1; // Convert to 1-12
    
    return dueDateYear === targetYear && dueDateMonth === targetMonth;
  }
  
  // For recurring expenses, check if they should generate a payment in the target period
  const createdYear = createdAt.getFullYear();
  const createdMonth = createdAt.getMonth() + 1; // Convert to 1-12
  
  // Recurring expense can only generate payments from its creation month onwards
  if (targetYear < createdYear || (targetYear === createdYear && targetMonth < createdMonth)) {
    return false;
  }
  
  return true;
};

/**
 * Devuelve una fecha (00:00:00) sin componente horario para comparar solo por día.
 */
export const stripTime = (isoDateString: string): Date => {
  // Si no viene en formato ISO con 'T', asumimos YYYY-MM-DD
  const datePart = isoDateString.includes('T') ? isoDateString.split('T')[0] : isoDateString;
  return new Date(datePart + 'T00:00:00');
};

/** Devuelve el inicio del mes (00:00:00) */
export const startOfMonth = (year: number, month0: number): Date => new Date(year, month0, 1, 0, 0, 0, 0);

/** Devuelve el fin del mes (23:59:59.999) */
export const endOfMonth = (year: number, month0: number): Date => new Date(year, month0 + 1, 0, 23, 59, 59, 999);
