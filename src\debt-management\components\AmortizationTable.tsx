import React, { useState, useRef, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Check, X, Calculator, Download, Eye, EyeOff, FileText, FileImage } from 'lucide-react';
import { exportToPDF, exportToPNG } from '@/utils/exportHelpers';
import { Loan } from '@/types';
import { formatCurrency } from '@/components/ui/numeric-input';
import { 
  calculateAmortizationTable, 
  markPaidPayments, 
  getPaymentSummary,
  AmortizationTable as AmortizationTableType 
} from '../utils/amortizationCalculations';
import { cn } from '@/lib/utils';

interface AmortizationTableProps {
  loan: Loan;
  onClose: () => void;
}

export function AmortizationTable({ loan, onClose }: AmortizationTableProps) {
  const [showAllRows, setShowAllRows] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calcular tabla de amortización
  const table = calculateAmortizationTable(
    loan.totalAmount,
    loan.interestRate,
    loan.loanTerm,
    loan.startDate,
    loan.monthlyPayment
  );

  // Marcar pagos realizados
  const tableWithPaidStatus = markPaidPayments(table);
  const summary = getPaymentSummary(tableWithPaidStatus);

  // Mostrar solo los primeros 12 pagos por defecto
  const displayRows = showAllRows ? tableWithPaidStatus.rows : tableWithPaidStatus.rows.slice(0, 12);

  const exportToCSV = () => {
    const headers = ['Período', 'Saldo Inicial', 'Cuota', 'Interés', 'Capital', 'Saldo Final', 'Fecha Pago', 'Estado'];
    const rows = tableWithPaidStatus.rows.map(row => [
      row.period.toString(),
      row.initialBalance.toFixed(2),
      row.payment.toFixed(2),
      row.interestPayment.toFixed(2),
      row.principalPayment.toFixed(2),
      row.finalBalance.toFixed(2),
      row.paymentDate || '',
      row.isPaid ? 'Pagado' : 'Pendiente'
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `amortizacion_${loan.name.replace(/\s+/g, '_')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportPDF = () => {
    if (containerRef.current) {
      exportToPDF(
        containerRef.current,
        `amortizacion_${loan.name.replace(/\s+/g, '_')}.pdf`
      );
    }
  };

  const exportPNG = () => {
    if (containerRef.current) {
      exportToPNG(
        containerRef.current,
        `amortizacion_${loan.name.replace(/\s+/g, '_')}.png`
      );
    }
  };

  return (
    <div className="space-y-6" ref={containerRef}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="w-5 h-5" />
                Tabla de Amortización - {loan.name}
              </CardTitle>
              <p className="text-sm text-finanz-text-secondary mt-1">
                {loan.type} • {formatCurrency(loan.totalAmount, loan.currency)} • {loan.loanTerm} meses
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={exportToCSV} size="sm">
                <Download className="w-4 h-4 mr-2" />
                CSV
              </Button>
              <Button variant="outline" onClick={exportPDF} size="sm">
                <FileText className="w-4 h-4 mr-2" />
                PDF
              </Button>
              <Button variant="outline" onClick={exportPNG} size="sm">
                <FileImage className="w-4 h-4 mr-2" />
                PNG
              </Button>
              <Button variant="outline" onClick={onClose} size="sm">
                Cerrar
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base text-finanz-success">Pagos Realizados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-finanz-success">
                {summary.paid.count} / {tableWithPaidStatus.rows.length}
              </div>
              <div className="text-sm space-y-1">
                <div>Total: {formatCurrency(summary.paid.totalAmount, loan.currency)}</div>
                <div>Capital: {formatCurrency(summary.paid.principal, loan.currency)}</div>
                <div>Interés: {formatCurrency(summary.paid.interest, loan.currency)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base text-finanz-warning">Pagos Pendientes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-finanz-warning">
                {summary.pending.count}
              </div>
              <div className="text-sm space-y-1">
                <div>Total: {formatCurrency(summary.pending.totalAmount, loan.currency)}</div>
                <div>Capital: {formatCurrency(summary.pending.principal, loan.currency)}</div>
                <div>Interés: {formatCurrency(summary.pending.interest, loan.currency)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base text-finanz-primary">Resumen Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-finanz-primary">
                {formatCurrency(table.totalPayments, loan.currency)}
              </div>
              <div className="text-sm space-y-1">
                <div>Capital: {formatCurrency(table.totalPrincipal, loan.currency)}</div>
                <div>Interés: {formatCurrency(table.totalInterest, loan.currency)}</div>
                <div>Tasa: {loan.interestRate}% anual</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Amortization Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Detalle de Pagos</CardTitle>
            <Button
              variant="outline"
              onClick={() => setShowAllRows(!showAllRows)}
              size="sm"
            >
              {showAllRows ? (
                <>
                  <EyeOff className="w-4 h-4 mr-2" />
                  Mostrar menos
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4 mr-2" />
                  Mostrar todo ({tableWithPaidStatus.rows.length})
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Estado</TableHead>
                  <TableHead className="w-20">#</TableHead>
                  <TableHead>Fecha Pago</TableHead>
                  <TableHead className="text-right">Saldo Inicial</TableHead>
                  <TableHead className="text-right">Cuota</TableHead>
                  <TableHead className="text-right">Interés</TableHead>
                  <TableHead className="text-right">Capital</TableHead>
                  <TableHead className="text-right">Saldo Final</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayRows.map((row) => (
                  <TableRow 
                    key={row.period}
                    className={`${row.isPaid ? 'bg-green-50 opacity-75' : ''} hover:bg-gray-50`}
                  >
                    <TableCell>
                      {row.isPaid ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                          <Check className="w-3 h-3 mr-1" />
                          Pagado
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <X className="w-3 h-3 mr-1" />
                          Pendiente
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{row.period}</TableCell>
                    <TableCell>
                      {row.paymentDate ? new Date(row.paymentDate).toLocaleDateString('es-DO') : '-'}
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {formatCurrency(row.initialBalance, loan.currency)}
                    </TableCell>
                    <TableCell className="text-right font-mono font-medium">
                      {formatCurrency(row.payment, loan.currency)}
                    </TableCell>
                    <TableCell className="text-right font-mono text-finanz-danger">
                      {formatCurrency(row.interestPayment, loan.currency)}
                    </TableCell>
                    <TableCell className="text-right font-mono text-finanz-success">
                      {formatCurrency(row.principalPayment, loan.currency)}
                    </TableCell>
                    <TableCell className="text-right font-mono">
                      {formatCurrency(row.finalBalance, loan.currency)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {!showAllRows && tableWithPaidStatus.rows.length > 12 && (
            <div className="text-center mt-4">
              <Button variant="outline" onClick={() => setShowAllRows(true)}>
                Ver {tableWithPaidStatus.rows.length - 12} pagos restantes
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Footer Note */}
      <Card className="bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Calculator className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-2">Información sobre la Tabla de Amortización:</p>
              <ul className="space-y-1 text-xs">
                <li>• Calculada usando el sistema francés (cuota fija) estándar en República Dominicana</li>
                <li>• Los pagos se marcan automáticamente como "Pagados" según la fecha actual</li>
                <li>• Los montos pueden tener pequeñas variaciones por redondeo</li>
                <li>• Esta tabla es referencial - consulte siempre con su entidad financiera</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
