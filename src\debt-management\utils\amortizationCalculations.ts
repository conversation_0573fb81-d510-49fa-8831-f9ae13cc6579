
export interface AmortizationRow {
  period: number;
  initialBalance: number;
  payment: number;
  interestPayment: number;
  principalPayment: number;
  finalBalance: number;
  isPaid: boolean;
  paymentDate?: string;
}

export interface AmortizationTable {
  rows: AmortizationRow[];
  totalPayments: number;
  totalInterest: number;
  totalPrincipal: number;
}

// Calcula la tabla de amortización completa usando el sistema francés
export const calculateAmortizationTable = (
  principal: number,
  annualRate: number,
  termInMonths: number,
  startDate: string,
  monthlyPayment?: number
): AmortizationTable => {
  if (principal <= 0 || termInMonths <= 0) {
    return { rows: [], totalPayments: 0, totalInterest: 0, totalPrincipal: principal };
  }

  const monthlyRate = annualRate / 100 / 12;
  
  // Calcular cuota mensual si no se proporciona
  let payment = monthlyPayment;
  if (!payment) {
    if (annualRate === 0) {
      payment = principal / termInMonths;
    } else {
      const numerator = principal * monthlyRate * Math.pow(1 + monthlyRate, termInMonths);
      const denominator = Math.pow(1 + monthlyRate, termInMonths) - 1;
      payment = numerator / denominator;
    }
  }

  const rows: AmortizationRow[] = [];
  let currentBalance = principal;
  const start = new Date(startDate);

  for (let period = 1; period <= termInMonths; period++) {
    const interestPayment = currentBalance * monthlyRate;
    const principalPayment = payment - interestPayment;
    const finalBalance = Math.max(0, currentBalance - principalPayment);

    // Calcular fecha de pago
    const paymentDate = new Date(start);
    paymentDate.setMonth(paymentDate.getMonth() + period);

    rows.push({
      period,
      initialBalance: currentBalance,
      payment: Math.round(payment * 100) / 100,
      interestPayment: Math.round(interestPayment * 100) / 100,
      principalPayment: Math.round(principalPayment * 100) / 100,
      finalBalance: Math.round(finalBalance * 100) / 100,
      isPaid: false,
      paymentDate: paymentDate.toISOString().split('T')[0]
    });

    currentBalance = finalBalance;
    
    // Si el balance llega a cero, terminar
    if (currentBalance <= 0) break;
  }

  const totalPayments = rows.reduce((sum, row) => sum + row.payment, 0);
  const totalInterest = rows.reduce((sum, row) => sum + row.interestPayment, 0);

  return {
    rows,
    totalPayments: Math.round(totalPayments * 100) / 100,
    totalInterest: Math.round(totalInterest * 100) / 100,
    totalPrincipal: principal
  };
};

// Marcar pagos como realizados basado en fecha actual
export const markPaidPayments = (
  table: AmortizationTable,
  currentDate: string = new Date().toISOString().split('T')[0]
): AmortizationTable => {
  const updatedRows = table.rows.map(row => ({
    ...row,
    isPaid: row.paymentDate ? row.paymentDate <= currentDate : false
  }));

  return {
    ...table,
    rows: updatedRows
  };
};

// Obtener resumen de pagos
export const getPaymentSummary = (table: AmortizationTable) => {
  const paidPayments = table.rows.filter(row => row.isPaid);
  const pendingPayments = table.rows.filter(row => !row.isPaid);
  
  const paidAmount = paidPayments.reduce((sum, row) => sum + row.payment, 0);
  const paidInterest = paidPayments.reduce((sum, row) => sum + row.interestPayment, 0);
  const paidPrincipal = paidPayments.reduce((sum, row) => sum + row.principalPayment, 0);
  
  const pendingAmount = pendingPayments.reduce((sum, row) => sum + row.payment, 0);
  const pendingInterest = pendingPayments.reduce((sum, row) => sum + row.interestPayment, 0);
  const pendingPrincipal = pendingPayments.reduce((sum, row) => sum + row.principalPayment, 0);

  return {
    paid: {
      count: paidPayments.length,
      totalAmount: Math.round(paidAmount * 100) / 100,
      interest: Math.round(paidInterest * 100) / 100,
      principal: Math.round(paidPrincipal * 100) / 100
    },
    pending: {
      count: pendingPayments.length,
      totalAmount: Math.round(pendingAmount * 100) / 100,
      interest: Math.round(pendingInterest * 100) / 100,
      principal: Math.round(pendingPrincipal * 100) / 100
    }
  };
};
