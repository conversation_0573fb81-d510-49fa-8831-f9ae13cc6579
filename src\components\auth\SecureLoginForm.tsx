import type React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Eye, EyeOff, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { useSecureAuthActions } from '@/contexts/auth/useSecureAuthActions';

interface SecureLoginFormProps {
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  onForgotPassword: () => void;
  prefilledEmail?: string;
}

export const SecureLoginForm: React.FC<SecureLoginFormProps> = ({ 
  isLoading, 
  setIsLoading, 
  onForgotPassword,
  prefilledEmail = ''
}) => {
  const [email, setEmail] = useState(prefilledEmail);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  
  const { signIn } = useSecureAuthActions();

  // Actualizar email cuando cambie el prefilled
  useEffect(() => {
    setEmail(prefilledEmail);
  }, [prefilledEmail]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validaciones
    const errors: {[key: string]: string} = {};
    
    if (!email.trim()) {
      errors.email = 'Email requerido';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Email inválido';
    }
    
    if (!password.trim()) {
      errors.password = 'Contraseña requerida';
    }
    
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      toast.error('Por favor corrige los errores antes de continuar');
      return;
    }

    setIsLoading(true);
    
    try {
      const { error } = await signIn(email, password);
      
      if (!error) {
        toast.success('Sesión iniciada correctamente');
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Email field - solo mostrar si no hay email prellenado */}
      {!prefilledEmail && (
        <div className="space-y-2">
          <Label htmlFor="login-email">Correo Electrónico *</Label>
          <Input
            id="login-email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isLoading}
            className={validationErrors.email ? 'border-red-500' : ''}
            autoComplete="email"
          />
          {validationErrors.email && (
            <p className="text-sm text-red-600">{validationErrors.email}</p>
          )}
        </div>
      )}
      
      {/* Password field */}
      <div className="space-y-2">
        <Label htmlFor="login-password">Contraseña *</Label>
        <div className="relative">
          <Input
            id="login-password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Tu contraseña"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={isLoading}
            className={validationErrors.password ? 'border-red-500' : ''}
            autoComplete="current-password"
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3"
            onClick={() => setShowPassword(!showPassword)}
            disabled={isLoading}
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
        {validationErrors.password && (
          <p className="text-sm text-red-600">{validationErrors.password}</p>
        )}
      </div>

      {/* Forgot password link */}
      <div className="text-right">
        <Button
          type="button"
          variant="link"
          className="text-sm text-blue-600 hover:text-blue-800 p-0 h-auto"
          onClick={onForgotPassword}
        >
          ¿Olvidaste tu contraseña?
        </Button>
      </div>

      {/* Submit button */}
      <Button 
        type="submit" 
        className="w-full" 
        disabled={isLoading}
      >
        {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Shield className="w-4 h-4 mr-2" />}
        Iniciar Sesión Segura
      </Button>
      
      {/* Security info */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Conexión cifrada end-to-end</p>
        <p>• Protección contra ataques de fuerza bruta</p>
        <p>• Monitoreo de seguridad en tiempo real</p>
      </div>
    </form>
  );
};
