
import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { logger } from '@/utils/logger';
import { QUERY_KEYS } from '@/constants/queryKeys';

export const usePaymentsSynchronization = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const lastSyncRef = useRef<number>(0);

  useEffect(() => {
    if (!user?.id) return;

    // Función optimizada para invalidar queries relacionados con pagos
    const invalidatePaymentQueries = async () => {
      const now = Date.now();
      
      // Debounce para evitar múltiples sincronizaciones en corto tiempo
      if (now - lastSyncRef.current < 500) {
        logger.debug('usePaymentsSynchronization: Debouncing sync request');
        return;
      }
      
      lastSyncRef.current = now;
      
      logger.debug('usePaymentsSynchronization: Starting payment synchronization');
      
      const queryKeysToInvalidate = [
        [QUERY_KEYS.PAYMENT_RECORDS, user.id],
        [QUERY_KEYS.DEBTS, user.id],
        [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, user.id],
        [QUERY_KEYS.OTHER_DATA, user.id],
        [QUERY_KEYS.EXPENSES, user.id]
      ];

      try {
        // Invalidar todas las queries relacionadas en paralelo
        await Promise.all(
          queryKeysToInvalidate.map(queryKey =>
            queryClient.invalidateQueries({ queryKey, exact: true })
          )
        );

        // Refetch inmediato solo de datos críticos
        await queryClient.refetchQueries({ 
          queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user.id],
          exact: true 
        });
        
        logger.debug('usePaymentsSynchronization: Synchronization completed successfully');
      } catch (error) {
        logger.error('usePaymentsSynchronization: Error during synchronization', error);
      }
    };

    // Escuchar cambios en el cache de forma optimizada
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated' && event.query.queryKey.includes(user.id)) {
        const queryKey = event.query.queryKey[0] as string;
        
        // Solo sincronizar si se actualizó una entidad relacionada con pagos
        const relatedQueries = [
          QUERY_KEYS.DEBTS,
          QUERY_KEYS.EXPENSES,
          QUERY_KEYS.OTHER_DATA,
          QUERY_KEYS.PERSONAL_DEBT_PAYMENTS,
        ];
        
        if (relatedQueries.includes(queryKey)) {
          logger.debug('usePaymentsSynchronization: Detected entity update, triggering sync:', queryKey);
          
          // Usar setTimeout para debounce adicional
          setTimeout(invalidatePaymentQueries, 200);
        }
      }
    });

    return () => {
      unsubscribe();
    };
  }, [user?.id, queryClient]);
};
