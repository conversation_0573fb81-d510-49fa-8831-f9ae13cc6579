
import React from 'react';
import { Button } from '@/components/ui/button';
import { Play, CheckSquare } from 'lucide-react';

interface AdviceItem {
  id: string;
  title: string;
  description: string;
  type: 'success' | 'warning' | 'danger' | 'info';
  category: 'savings' | 'debt' | 'budget' | 'investment' | 'emergency';
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
}

interface AdviceDetailModalActionsProps {
  advice: AdviceItem;
  onClose: () => void;
  onStartActionPlan: () => void;
  isActionPlanStarted: boolean;
}

export const AdviceDetailModalActions: React.FC<AdviceDetailModalActionsProps> = ({
  advice,
  onClose,
  onStartActionPlan,
  isActionPlanStarted
}) => {
  return (
    <div className="flex gap-3 pt-4 border-t">
      <Button onClick={onClose} variant="outline" className="flex-1">
        Cerrar
      </Button>
      {advice.actionable && (
        <Button 
          className="flex-1"
          onClick={onStartActionPlan}
          disabled={isActionPlanStarted}
        >
          {isActionPlanStarted ? (
            <>
              <CheckSquare className="w-4 h-4 mr-2" />
              Plan Iniciado
            </>
          ) : (
            <>
              <Play className="w-4 h-4 mr-2" />
              Iniciar Plan de Acción
            </>
          )}
        </Button>
      )}
    </div>
  );
};
