import React, { Suspense } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { EnhancedDashboardMetrics } from '../EnhancedDashboardMetrics';
import { UnifiedInsightsPanel } from '../UnifiedInsightsPanel';
import { ProactiveAlerts } from '../ProactiveAlerts';
import { PeriodComparison } from '../PeriodComparison';
import { EnhancedReportExporter } from '../EnhancedReportExporter';
import { useDashboardData } from '../../hooks/useDashboardData';
import { TrendingUp, TrendingDown, AlertCircle } from 'lucide-react';

const SummaryQuickView = React.lazy(() => import('../SummaryQuickView'));

export const SummaryTab: React.FC = () => {
  const {
    netIncome,
    totalMonthlyPayments,
    netBalance,
    totalDebt,
    pendingReimbursements,
    savingsRate,
    debtToIncomeRatio,
    emergencyFundMonths,
    paymentToIncomeRatio,
    paymentBreakdown
  } = useDashboardData();

  return (
    <div className="space-y-6">
      {/* Header con exportador */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Dashboard Financiero</h2>
          <p className="text-sm text-gray-600">Vista completa de tu situación financiera</p>
        </div>
        <EnhancedReportExporter />
      </div>

      {/* Alertas proactivas */}
      <ProactiveAlerts />

      {/* Vista rápida con lazy loading */}
      <Suspense fallback={
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[1, 2, 3].map(i => (
                <Skeleton key={i} className="h-24" />
              ))}
            </div>
          </CardContent>
        </Card>
      }>
        <SummaryQuickView />
      </Suspense>

      {/* Comparación con períodos anteriores */}
      <PeriodComparison />

      {/* Panel de insights unificado */}
      <UnifiedInsightsPanel />

      {/* Métricas principales mejoradas */}
      <EnhancedDashboardMetrics
        netIncome={netIncome}
        totalMonthlyPayments={totalMonthlyPayments}
        netBalance={netBalance}
        totalDebt={totalDebt}
        pendingReimbursements={pendingReimbursements}
        savingsRate={savingsRate}
        debtToIncomeRatio={debtToIncomeRatio}
        emergencyFundMonths={emergencyFundMonths}
        paymentToIncomeRatio={paymentToIncomeRatio}
        paymentBreakdown={paymentBreakdown}
      />
    </div>
  );
};
