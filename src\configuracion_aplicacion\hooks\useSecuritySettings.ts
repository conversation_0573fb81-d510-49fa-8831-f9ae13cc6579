import { logger } from "@/utils/logger";

import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import { securitySchema, type SecurityFormData } from '../schemas/settingsSchemas';
import { securityService, type SecuritySettings } from '../services/securityService';

export const useSecuritySettings = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const securityForm = useForm<SecurityFormData>({
    resolver: zodResolver(securitySchema),
    defaultValues: {
      twoFactorAuth: false,
      biometricAuth: false,
      sessionTimeout: '60', // Default to 60 minutes instead of 30
      autoLogout: false, // Default to false to prevent automatic logouts
      currentPassword: '',
      newPassword: '',
    },
  });

  const loadSecuritySettings = useCallback(() => {
    try {
      const savedSecurity = localStorage.getItem('finanz_security_settings');
      
      if (savedSecurity) {
        const securityData = JSON.parse(savedSecurity);
        const completeSecurityData: SecurityFormData = {
          twoFactorAuth: securityData.twoFactorAuth ?? false,
          biometricAuth: securityData.biometricAuth ?? false,
          sessionTimeout: securityData.sessionTimeout ?? '60', // Default to 60 minutes
          autoLogout: securityData.autoLogout ?? false, // Default to false
          currentPassword: '',
          newPassword: '',
        };
        securityForm.reset(completeSecurityData);
      } else {
        securityForm.reset({
          twoFactorAuth: false,
          biometricAuth: false,
          sessionTimeout: '60', // Default to 60 minutes
          autoLogout: false, // Default to false
          currentPassword: '',
          newPassword: '',
        });
      }
    } catch (error) {
      console.error('Error loading security settings:', error);
    }
  }, [securityForm]);

  const initializeSecurityService = useCallback(async () => {
    try {
      await securityService.loadSettings();
    } catch (error) {
      console.error('Error initializing security service:', error);
    }
  }, []);

  useEffect(() => {
    loadSecuritySettings();
    initializeSecurityService();
  }, [loadSecuritySettings, initializeSecurityService]);

  const determineSecurityLevel = (data: SecurityFormData): 'basic' | 'strict' | 'maximum' => {
    if (data.twoFactorAuth && data.sessionTimeout === '15') {
      return 'maximum';
    } else if (data.sessionTimeout === '30' || data.sessionTimeout === '45') {
      return 'strict';
    } else {
      return 'basic';
    }
  };

  const showSecurityLevelInfo = (level: 'basic' | 'strict' | 'maximum'): void => {
    const messages = {
      basic: 'Nivel básico: Configuración permisiva para uso regular',
      strict: 'Nivel estricto: Re-autenticación periódica y bloqueo automático',
      maximum: 'Nivel máximo: 2FA + re-autenticación para acciones sensibles'
    };
    
    toast({
      title: `Seguridad configurada: ${level.toUpperCase()}`,
      description: messages[level],
      duration: 5000,
    });
  };

  const saveSecurity = async (data: SecurityFormData) => {
    try {
      setIsLoading(true);
      logger.debug('useSecuritySettings: Saving security data:', data);
      
      // Si hay contraseñas, manejar el cambio de contraseña
      if (data.currentPassword && data.newPassword) {
        logger.debug('useSecuritySettings: Processing password change');
        await securityService.changePassword(data.currentPassword, data.newPassword);
      }
      
      // Guardar configuraciones de seguridad (sin contraseñas)
      const securitySettings: SecuritySettings = {
        twoFactorAuth: data.twoFactorAuth,
        biometricAuth: data.biometricAuth,
        sessionTimeout: data.sessionTimeout,
        autoLogout: data.autoLogout,
        securityLevel: determineSecurityLevel(data),
        requireReauth: data.twoFactorAuth,
        lockOnMinimize: data.sessionTimeout === '15' || data.sessionTimeout === '30',
      };
      
      logger.debug('useSecuritySettings: Saving security settings:', securitySettings);
      await securityService.saveSettings(securitySettings);
      
      // Solo mostrar mensajes de éxito si no se cambió contraseña (el cambio de contraseña ya muestra su propio mensaje)
      if (!data.currentPassword && !data.newPassword) {
        toast({
          title: 'Configuración de seguridad actualizada',
          description: 'Las configuraciones han sido guardadas exitosamente',
        });
        
        showSecurityLevelInfo(securitySettings.securityLevel);
      }
      
    } catch (error: unknown) {
      const errMsg = error instanceof Error ? error.message : JSON.stringify(error);
      console.error('useSecuritySettings: Error saving security:', errMsg);
      throw error; // Re-lanzar el error para que lo maneje el componente
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setIsLoading(true);
      
      await securityService.changePassword(currentPassword, newPassword);
      
      toast({
        title: 'Contraseña actualizada',
        description: 'La contraseña ha sido cambiada exitosamente',
      });
      
      // Limpiar campos de contraseña
      securityForm.setValue('currentPassword', '');
      securityForm.setValue('newPassword', '');
      
    } catch (error: unknown) {
      const errMsg = error instanceof Error ? error.message : 'Error desconocido';
      console.error('Error changing password:', errMsg);
      toast({
        title: 'Error',
        description: errMsg || 'No se pudo cambiar la contraseña',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    securityForm,
    saveSecurity,
    changePassword,
    isLoading,
    securityService,
  };
};
