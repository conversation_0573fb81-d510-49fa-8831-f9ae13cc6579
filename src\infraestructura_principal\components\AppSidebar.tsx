
import React, { useRef } from "react";
import {
  LayoutDashboard,
  Wallet,
  Coins,
  CreditCard,
  Repeat,
  FileText,
  Goal,
  Lightbulb,
  Settings,
  HelpCircle,
  DollarSign,
  Calendar,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  useSidebar,
} from "@/components/ui/sidebar";
import { Link, useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { motion, useMotionValue, useTransform, useSpring, MotionValue } from 'framer-motion';
import {
  preloadDashboardPage,
  preloadIncomePage,
  preloadExpensesPage,
  preloadLoansPage,
  preloadSubscriptionsPage,
  preloadReimbursementsPage,
  preloadPaymentsPage,
  preloadGoalsPage,
  preloadFinancialAdvicePage,
  preloadConfigurationPage,
  preloadDiagnosticPage,
} from "@/utils/pagePreloader";

// Componente AnimatedNavItem para el efecto dock mejorado
const AnimatedNavItem = ({ 
  item, 
  mouseY, 
  isActive, 
  onMouseEnter, 
  onClick 
}: {
  item: {
    href: string;
    label: string;
    icon: any;
    color: string;
    preload: () => void;
  };
  mouseY: MotionValue<number>;
  isActive: boolean;
  onMouseEnter: () => void;
  onClick: () => void;
}) => {
  // 1. Obtén una referencia al elemento para medir su posición
  const ref = useRef<HTMLAnchorElement>(null);

  // 2. Calcula la distancia entre el mouse y el centro del ítem
  const distance = useTransform(mouseY, (val) => {
    const bounds = ref.current?.getBoundingClientRect() ?? { y: 0, height: 0 };
    return val - bounds.y - bounds.height / 2;
  });

  // 3. Transformaciones mejoradas para un efecto dock más pronunciado
  // Distancia de activación más amplia para mejor respuesta
  const iconSizeTransform = useTransform(distance, [-120, 0, 120], [20, 36, 20]);
  
  // 4. Transformación de opacidad para efecto de desvanecimiento
  const opacityTransform = useTransform(distance, [-120, -60, 0, 60, 120], [0.7, 0.9, 1, 0.9, 0.7]);
  
  // 5. Transformación de desplazamiento lateral para efecto 3D
  const translateXTransform = useTransform(distance, [-120, 0, 120], [0, 8, 0]);
  
  // 6. Aplica animaciones de muelle optimizadas para cada propiedad
  const iconSize = useSpring(iconSizeTransform, { 
    stiffness: 300, 
    damping: 20, 
    mass: 0.5 
  });
  
  const opacity = useSpring(opacityTransform, {
    stiffness: 400,
    damping: 25,
    mass: 0.3
  });
  
  const translateX = useSpring(translateXTransform, {
    stiffness: 250,
    damping: 20,
    mass: 0.4
  });

  return (
    <SidebarMenuItem key={item.href}>
      <SidebarMenuButton asChild>
        <Link
          ref={ref}
          to={item.href}
          onMouseEnter={onMouseEnter}
          onClick={onClick}
          className={`
            flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 ease-in-out group relative overflow-hidden
            ${isActive
              ? 'bg-blue-50 dark:bg-neutral-800 text-blue-700 dark:text-white shadow-sm'
              : 'text-gray-700 dark:text-neutral-200 hover:bg-gray-50 dark:hover:bg-neutral-800 hover:text-gray-900 dark:hover:text-white'
            }
          `}
        >
          {/* Indicador activo mejorado */}
          {isActive && (
            <motion.div 
              className="absolute left-0 top-0 bottom-0 w-1 bg-blue-600 rounded-r-full"
              initial={{ scaleY: 0 }}
              animate={{ scaleY: 1 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            />
          )}
          
          {/* Contenedor del ícono con efectos múltiples */}
          <motion.div
            style={{ 
              opacity,
              x: translateX
            }}
            className="relative"
          >
            {/* Efecto de brillo al hover */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-white/10 rounded-lg"
              initial={{ x: "-100%" }}
              whileHover={{ x: "100%" }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
            />
            
            {/* Ícono con animación de tamaño */}
            <motion.div 
              style={{ width: iconSize, height: iconSize }} 
              className="flex items-center justify-center relative z-10"
            >
              <item.icon className={`
                w-full h-full transition-colors duration-200
                ${isActive 
                  ? 'text-blue-600 dark:text-blue-400' 
                  : item.color
                }
              `} strokeWidth={1.5} />
            </motion.div>
          </motion.div>
          
          {/* Texto con efecto de desvanecimiento */}
          <motion.span 
            style={{ opacity }}
            className={`
              font-medium transition-colors duration-200
              ${isActive 
                ? 'text-blue-700 dark:text-white' 
                : 'text-gray-700 dark:text-neutral-200 group-hover:text-gray-900 dark:group-hover:text-white'
              }
            `}
          >
            {item.label}
          </motion.span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
};

export const AppSidebar = () => {
  const location = useLocation();
  const isMobile = useIsMobile();
  const { setOpenMobile } = useSidebar();
  
  // 1. Crea un motion value para rastrear la posición Y del mouse
  const mouseY = useMotionValue(Infinity);
  
  // Función para manejar clics en mobile
  const handleMobileNavigation = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };
  
  const navItems = [
    {
      href: "/app/dashboard",
      label: "Dashboard",
      icon: LayoutDashboard,
      color: "text-slate-600 dark:text-slate-300",
      preload: preloadDashboardPage,
    },
    {
      href: "/app/income",
      label: "Ingresos",
      icon: Wallet,
      color: "text-emerald-600 dark:text-emerald-400",
      preload: preloadIncomePage,
    },
    {
      href: "/app/expenses",
      label: "Gastos",
      icon: Coins,
      color: "text-red-600 dark:text-red-400",
      preload: preloadExpensesPage,
    },
    {
      href: "/app/loans",
      label: "Deudas",
      icon: CreditCard,
      color: "text-purple-600 dark:text-purple-400",
      preload: preloadLoansPage,
    },
    {
      href: "/app/subscriptions",
      label: "Suscripciones",
      icon: Repeat,
      color: "text-blue-600 dark:text-blue-400",
      preload: preloadSubscriptionsPage,
    },
    {
      href: "/app/reimbursements",
      label: "Reembolsos",
      icon: FileText,
      color: "text-indigo-600 dark:text-indigo-400",
      preload: preloadReimbursementsPage,
    },
    {
      href: "/app/payments",
      label: "Gestión de Pagos",
      icon: Calendar,
      color: "text-orange-600 dark:text-orange-400",
      preload: preloadPaymentsPage,
    },
    {
      href: "/app/goals",
      label: "Metas Financieras",
      icon: Goal,
      color: "text-amber-600 dark:text-amber-400",
      preload: preloadGoalsPage,
    },
    {
      href: "/app/financial-advice",
      label: "Consejos Financieros",
      icon: Lightbulb,
      color: "text-yellow-600 dark:text-yellow-400",
      preload: preloadFinancialAdvicePage,
    },
    {
      href: "/app/configuration",
      label: "Configuración",
      icon: Settings,
      color: "text-gray-600 dark:text-gray-300",
      preload: preloadConfigurationPage,
    },
    {
      href: "/app/diagnostic",
      label: "Diagnóstico",
      icon: HelpCircle,
      color: "text-slate-600 dark:text-slate-300",
      preload: preloadDiagnosticPage,
    },
  ];

  return (
    <Sidebar className="border-r border-gray-200 dark:border-neutral-700 bg-white dark:bg-black shadow-sm">
      <SidebarHeader className="p-6 border-b border-gray-100 dark:border-neutral-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
            <DollarSign className="w-6 h-6 text-white" />
          </div>
          <div className="flex flex-col">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              FinanzApp
            </h1>
            <p className="text-sm text-gray-500 dark:text-neutral-400">
              Tu gestor financiero
            </p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4 py-6">
        <SidebarGroup>
          <SidebarGroupLabel className="text-gray-500 dark:text-neutral-400 font-medium text-xs uppercase tracking-wider px-3 py-2 mb-2">
            Gestión Financiera
          </SidebarGroupLabel>
          <SidebarGroupContent>
            {/* 2. Envuelve la navegación con motion.div y añade los listeners */}
            <motion.div
              onMouseMove={(e) => mouseY.set(e.clientY)}
              onMouseLeave={() => mouseY.set(Infinity)}
            >
              <SidebarMenu className="space-y-2">
                {navItems.map((item) => {
                  const isActive = location.pathname === item.href;
                  
                  return (
                    <AnimatedNavItem
                      key={item.href}
                      item={item}
                      mouseY={mouseY}
                      isActive={isActive}
                      onMouseEnter={item.preload}
                      onClick={handleMobileNavigation}
                    />
                  );
                })}
              </SidebarMenu>
            </motion.div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-4 border-t border-gray-100 dark:border-neutral-700">
        <div className="text-center">
          <div className="inline-flex items-center space-x-2 bg-gray-50 dark:bg-neutral-800 px-3 py-2 rounded-full">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-gray-600 dark:text-neutral-300 font-medium">
              FinanzApp v2.1
            </span>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};
