import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Mail, ArrowLeft, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { emailSchema, sanitizeEmail } from '@/utils/security/inputValidation';
import { ZodError } from 'zod';

interface PasswordRecoveryFormProps {
  onBackToLogin: () => void;
}

export const PasswordRecoveryForm: React.FC<PasswordRecoveryFormProps> = ({ onBackToLogin }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [validationError, setValidationError] = useState('');

  const validateEmail = (email: string): string | null => {
    try {
      emailSchema.parse(email);
      return null;
    } catch (error) {
      if (error instanceof ZodError) {
        return error.errors?.[0]?.message || 'Email inválido';
      }
      return 'Email inválido';
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validación
    const emailError = validateEmail(email);
    if (emailError) {
      setValidationError(emailError);
      return;
    }

    setIsLoading(true);
    setValidationError('');

    try {
      const sanitizedEmail = sanitizeEmail(email);
      const redirectUrl = `${window.location.origin}/auth/reset-password`;
      
      const { error } = await supabase.auth.resetPasswordForEmail(sanitizedEmail, {
        redirectTo: redirectUrl,
      });

      if (error) {
        console.error('Password reset error:', error);
        toast.error('Error al enviar el email de recuperación. Intenta nuevamente.');
        return;
      }

      setEmailSent(true);
      toast.success('Email de recuperación enviado correctamente');
    } catch (error) {
      console.error('Password reset error:', error);
      toast.error('Error de conexión. Verifica tu internet.');
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="space-y-4">
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <p className="font-medium">Email enviado correctamente</p>
            <p className="text-sm mt-1">
              Revisa tu bandeja de entrada y haz clic en el enlace para restablecer tu contraseña.
            </p>
          </AlertDescription>
        </Alert>

        <div className="text-center space-y-4">
          <p className="text-sm text-gray-600">
            ¿No recibiste el email? Revisa tu carpeta de spam o intenta nuevamente.
          </p>
          
          <div className="space-y-2">
            <Button 
              variant="outline" 
              onClick={() => setEmailSent(false)}
              className="w-full"
            >
              Enviar nuevamente
            </Button>
            
            <Button 
              variant="ghost" 
              onClick={onBackToLogin}
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver al inicio de sesión
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handlePasswordReset} className="space-y-4">
      <Alert className="border-blue-200 bg-blue-50">
        <Mail className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <p className="font-medium">Recuperación de Contraseña</p>
          <p className="text-sm mt-1">
            Ingresa tu email y te enviaremos un enlace para restablecer tu contraseña.
          </p>
        </AlertDescription>
      </Alert>

      <div className="space-y-2">
        <Label htmlFor="recovery-email">Correo Electrónico</Label>
        <Input
          id="recovery-email"
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
            if (validationError) setValidationError('');
          }}
          required
          disabled={isLoading}
          className={validationError ? 'border-red-500' : ''}
        />
        {validationError && (
          <p className="text-sm text-red-600">{validationError}</p>
        )}
      </div>

      <div className="space-y-2">
        <Button 
          type="submit" 
          className="w-full" 
          disabled={isLoading || !email}
        >
          {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
          Enviar enlace de recuperación
        </Button>
        
        <Button 
          type="button"
          variant="ghost" 
          onClick={onBackToLogin}
          className="w-full"
          disabled={isLoading}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Volver al inicio de sesión
        </Button>
      </div>
    </form>
  );
};
