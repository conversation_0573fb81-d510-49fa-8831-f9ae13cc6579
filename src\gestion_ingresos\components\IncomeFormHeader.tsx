
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, TrendingUp } from 'lucide-react';
import { Income as IncomeType } from '@/types';

interface IncomeFormHeaderProps {
  formData: Partial<IncomeType>;
  editingIncome: IncomeType | null;
  onBack: () => void;
}

export function IncomeFormHeader({ formData, editingIncome, onBack }: IncomeFormHeaderProps) {
  const getMonthName = (monthStr: string) => {
    const date = new Date(monthStr + '-01');
    return date.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' });
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Volver
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {editingIncome ? 'Editar' : 'Nuevo'} Ingreso {formData.month ? getMonthName(formData.month) : ''}
          </h1>
        </div>
      </div>
      <TrendingUp className="w-8 h-8 text-finanz-success" />
    </div>
  );
}
