import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { notificationSchema, type NotificationFormData } from '../schemas/settingsSchemas';
import { notificationService, type NotificationSettings } from '../services/notificationService';

export const useNotificationSettings = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const notificationForm = useForm<NotificationFormData>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      paymentReminders: true,
      budgetAlerts: true,
      goalProgress: true,
      emailReports: false,
      pushNotifications: true,
      overduePayments: true,
      weeklyReports: false,
      monthlyReports: true,
    },
  });

  // Cargar notificaciones y servicio sólo cuando cambie el usuario
  const loadNotificationSettings = useCallback(() => {
    try {
      const savedNotifications = localStorage.getItem('finanz_notification_settings');
      
      if (savedNotifications) {
        const notificationData = JSON.parse(savedNotifications);
        const completeNotificationData: NotificationFormData = {
          paymentReminders: notificationData.paymentReminders ?? true,
          budgetAlerts: notificationData.budgetAlerts ?? true,
          goalProgress: notificationData.goalProgress ?? true,
          emailReports: notificationData.emailReports ?? false,
          pushNotifications: notificationData.pushNotifications ?? true,
          overduePayments: notificationData.overduePayments ?? true,
          weeklyReports: notificationData.weeklyReports ?? false,
          monthlyReports: notificationData.monthlyReports ?? true,
        };
        notificationForm.reset(completeNotificationData);
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  }, [notificationForm]);

  const initializeNotificationService = useCallback(async () => {
    if (!user) return;

    try {
      await notificationService.loadSettings(user.id);
      await notificationService.requestNotificationPermission();
      await notificationService.checkOverduePayments();
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }, [user]);

  useEffect(() => {
    loadNotificationSettings();
    initializeNotificationService();
  }, [loadNotificationSettings, initializeNotificationService]);

  const saveNotifications = async (data: NotificationFormData) => {
    try {
      setIsLoading(true);
      
      const notificationSettings: NotificationSettings = {
        paymentReminders: data.paymentReminders,
        budgetAlerts: data.budgetAlerts,
        goalProgress: data.goalProgress,
        emailReports: data.emailReports,
        pushNotifications: data.pushNotifications,
        overduePayments: data.overduePayments,
        weeklyReports: data.weeklyReports,
        monthlyReports: data.monthlyReports,
      };
      
      await notificationService.saveSettings(notificationSettings);
      
      toast({
        title: 'Notificaciones actualizadas',
        description: 'Las preferencias de notificaciones han sido guardadas',
      });
    } catch (error) {
      console.error('Error saving notifications:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron guardar las notificaciones',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    notificationForm,
    saveNotifications,
    isLoading,
    notificationService,
  };
};
