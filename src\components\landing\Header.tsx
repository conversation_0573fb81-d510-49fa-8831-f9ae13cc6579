
import React from 'react';
import { TouchButton } from '@/components/ui/touch-button';
import { DollarSign, ArrowRight, LogIn } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export function Header() {
  const { isMobile, isTablet } = useBreakpoint();

  return (
    <header className={`
      fixed top-0 left-0 right-0 z-50 
      bg-white/95 backdrop-blur-lg shadow-lg border-b border-white/20
      ${isMobile ? 'px-3 py-2' : isTablet ? 'px-4 py-3' : 'px-4 py-4'}
    `}>
      <div className={`
        container mx-auto flex items-center justify-between
        ${isMobile ? 'gap-2' : 'gap-4'}
      `}>
        {/* Logo Section */}
        <div className="flex items-center space-x-2">
          <div className="relative">
            <div className={`
              bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 
              rounded-2xl flex items-center justify-center shadow-xl
              ${isMobile ? 'w-10 h-10' : isTablet ? 'w-12 h-12' : 'w-14 h-14'}
            `}>
              <DollarSign className={`
                text-white
                ${isMobile ? 'w-5 h-5' : isTablet ? 'w-6 h-6' : 'w-8 h-8'}
              `} />
            </div>
            <div className={`
              absolute -top-1 -right-1 bg-gradient-to-r from-green-400 to-emerald-500 
              rounded-full border-2 border-white
              ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}
            `}></div>
          </div>
          <div className={isMobile ? 'hidden' : 'block'}>
            <h1 className={`
              font-black bg-gradient-to-r from-blue-600 to-purple-700 
              bg-clip-text text-transparent
              ${isMobile ? 'text-lg' : isTablet ? 'text-xl' : 'text-2xl'}
            `}>
              FinanzApp
            </h1>
            <p className={`
              text-gray-500 font-semibold tracking-wide
              ${isMobile ? 'text-xs' : 'text-xs'}
            `}>
              Gestión Financiera Profesional
            </p>
          </div>
        </div>

        {/* Navigation - Hidden on mobile */}
        {!isMobile && (
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              to="/features" 
              className="text-gray-700 hover:text-blue-600 font-semibold transition-colors duration-200"
            >
              Características
            </Link>
            <Link 
              to="/support" 
              className="text-gray-700 hover:text-blue-600 font-semibold transition-colors duration-200"
            >
              Soporte
            </Link>
          </nav>
        )}
        
        {/* Action Buttons */}
        <div className={`
          flex items-center
          ${isMobile ? 'space-x-1' : 'space-x-3'}
        `}>
          {isMobile ? (
            // Mobile layout - solo botón principal
            <Link to="/app/dashboard">
              <TouchButton 
                className="bg-gradient-to-r from-blue-600 to-purple-700 hover:from-blue-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all duration-200 font-semibold text-xs px-3 py-2"
              >
                <ArrowRight className="w-3 h-3" />
                <span className="ml-1">App</span>
              </TouchButton>
            </Link>
          ) : (
            // Desktop/Tablet layout
            <>
              <Link to="/auth">
                <TouchButton 
                  variant="outline" 
                  className={`
                    border-2 border-gray-300 text-gray-700 bg-white 
                    hover:!bg-gray-50 hover:!border-blue-500 hover:!text-blue-600 
                    transition-all duration-200 font-semibold rounded-xl
                    ${isTablet ? 'px-4 text-sm' : 'px-6'}
                  `}
                >
                  <LogIn className="w-4 h-4 mr-2" />
                  Acceder
                </TouchButton>
              </Link>
              <Link to="/app/dashboard">
                <TouchButton 
                  className={`
                    bg-gradient-to-r from-blue-600 to-purple-700 
                    hover:from-blue-700 hover:to-purple-800 shadow-lg 
                    hover:shadow-xl transition-all duration-200 font-semibold rounded-xl
                    ${isTablet ? 'px-4 text-sm' : 'px-6'}
                  `}
                >
                  Dashboard
                  <ArrowRight className="w-4 h-4 ml-2" />
                </TouchButton>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
