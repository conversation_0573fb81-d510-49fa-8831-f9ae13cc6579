
-- Drop existing policies to recreate them with optimized performance
DROP POLICY IF EXISTS "Users can view their own payment projections" ON public.payment_projections;
DROP POLICY IF EXISTS "Users can create their own payment projections" ON public.payment_projections;
DROP POLICY IF EXISTS "Users can update their own payment projections" ON public.payment_projections;
DROP POLICY IF EXISTS "Users can delete their own payment projections" ON public.payment_projections;
DROP POLICY IF EXISTS "Users can view their own audit logs" ON public.payment_audit_log;

-- <PERSON><PERSON> optimized RLS policies for payment_projections
CREATE POLICY "Users can view their own payment projections" 
  ON public.payment_projections 
  FOR SELECT 
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create their own payment projections" 
  ON public.payment_projections 
  FOR INSERT 
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own payment projections" 
  ON public.payment_projections 
  FOR UPDATE 
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own payment projections" 
  ON public.payment_projections 
  FOR DELETE 
  USING ((SELECT auth.uid()) = user_id);

-- <PERSON><PERSON> optimized RLS policy for payment_audit_log
CREATE POLICY "Users can view their own audit logs" 
  ON public.payment_audit_log 
  FOR SELECT 
  USING ((SELECT auth.uid()) = user_id);
