import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { describe, it, expect, vi } from 'vitest'
import { useFinanceData } from '@/hooks/useFinanceData'
import { Income, Expense, CreditCard, Loan, PersonalDebt, Reimbursement } from '@/types'

vi.mock('@/utils/securityUtils', () => ({
  auditLog: vi.fn()
}))

import { auditLog } from '@/utils/securityUtils'

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'user-1' } })
}))

const addIncomeMock = vi.fn().mockResolvedValue({ id: 'income-new' })

const currentMonth = new Date().toISOString().slice(0, 7)
const prevMonth = '2023-01'

const income: Income = {
  id: 'income-1',
  month: currentMonth,
  currency: 'DOP',
  fixedSalary: 1000,
  variablePercentage: 0,
  variableAmount: 0,
  quarterlyIncentive: 0,
  performancePercentage: 0,
  vehicleDepreciation: 0,
  legalDeductions: 0,
  payrollLoan: 0,
  grossIncome: 1000,
  netIncome: 1000,
  otherIncomeItems: [
    { id: 'extra', description: 'Bonus', amount: 200, currency: 'DOP' }
  ],
  variableScenarios: { base: 0, medium: 0, optimal: 0 }
}

const incomePrev: Income = {
  ...income,
  id: 'income-2',
  month: prevMonth,
  netIncome: 500,
  grossIncome: 500,
}

const expense: Expense = {
  id: 'exp-1',
  date: `${currentMonth}-05`,
  month: currentMonth,
  type: 'Fijo',
  categoryId: '1',
  categoryName: 'Rent',
  amount: 300,
  description: '',
  paymentMethod: 'cash',
  status: 'paid',
  currency: 'DOP',
  paymentDate: `${income.month}-06`,
  createdAt: '',
  updatedAt: ''
}

const expensePrev: Expense = {
  ...expense,
  id: 'exp-2',
  date: `${prevMonth}-05`,
  month: prevMonth,
  amount: 100,
  paymentDate: `${prevMonth}-06`,
}

const creditCard: CreditCard = {
  id: 'cc1',
  name: 'Card1',
  currency: 'DOP',
  creditLimit: 0,
  currentBalance: 500,
  minimumPayment: 0,
  interestRate: 0,
  paymentDate: '15',
  paymentDueDate: '20',
  cutoffDate: '10',
  isActive: true
}

const loan: Loan = {
  id: 'loan1',
  name: 'Loan1',
  type: 'car',
  currency: 'DOP',
  totalAmount: 1000,
  monthlyPayment: 0,
  interestRate: 0,
  loanTerm: 0,
  startDate: '2024-01-01',
  dueDate: '2024-12-31',
  paymentDate: '1',
  isActive: true
}

const personalDebt: PersonalDebt = {
  id: 'pd1',
  name: 'Debt1',
  currency: 'DOP',
  amount: 200,
  paymentDate: '10',
  isActive: true
}

const reimbursement: Reimbursement = {
  id: 'r1',
  date: `${currentMonth}-01`,
  categoryId: '1',
  categoryName: 'Travel',
  amount: 50,
  currency: 'DOP',
  status: 'Pendiente',
  reimbursementDate: null,
  description: '',
  createdAt: '',
  updatedAt: ''
}

const reimbursementPrev: Reimbursement = {
  ...reimbursement,
  id: 'r2',
  date: `${prevMonth}-01`,
  amount: 20,
}

vi.mock('@/hooks/useSupabaseIncomes', () => ({
  useSupabaseIncomes: () => ({
    incomes: [income, incomePrev],
    isLoading: false,
    error: null,
    addIncome: addIncomeMock,
    updateIncome: vi.fn(),
    deleteIncome: vi.fn()
  })
}))

vi.mock('@/hooks/useSupabaseExpenses', () => ({
  useSupabaseExpenses: () => ({
    expenses: [expense, expensePrev],
    isLoading: false,
    error: null,
    addExpense: vi.fn(),
    updateExpense: vi.fn(),
    deleteExpense: vi.fn()
  })
}))

vi.mock('@/hooks/useSupabaseDebts', () => ({
  useSupabaseDebts: () => ({
    creditCards: [creditCard],
    loans: [loan],
    personalDebts: [personalDebt],
    personalDebtPayments: [],
    isLoading: false,
    addCreditCard: vi.fn(),
    updateCreditCard: vi.fn(),
    deleteCreditCard: vi.fn(),
    addLoan: vi.fn(),
    updateLoan: vi.fn(),
    deleteLoan: vi.fn(),
    addPersonalDebt: vi.fn(),
    updatePersonalDebt: vi.fn(),
    deletePersonalDebt: vi.fn(),
    addPersonalDebtPayment: vi.fn(),
    updatePersonalDebtPayment: vi.fn(),
    deletePersonalDebtPayment: vi.fn()
  })
}))

vi.mock('@/hooks/useSupabaseData', () => ({
  useSupabaseData: () => ({
    subscriptions: [],
    reimbursements: [reimbursement, reimbursementPrev],
    financialGoals: [],
    defaultCurrency: 'DOP',
    isLoading: false,
    addSubscription: vi.fn(),
    updateSubscription: vi.fn(),
    deleteSubscription: vi.fn(),
    toggleSubscriptionStatus: vi.fn(),
    addReimbursement: vi.fn(),
    updateReimbursement: vi.fn(),
    deleteReimbursement: vi.fn(),
    addFinancialGoal: vi.fn(),
    updateFinancialGoal: vi.fn(),
    deleteFinancialGoal: vi.fn(),
    setDefaultCurrency: vi.fn()
  })
}))

vi.mock('@/hooks/useSupabasePaymentRecords', () => ({
  useSupabasePaymentRecords: () => ({
    paymentRecords: [],
    isLoading: false,
    error: null,
    addPaymentRecord: vi.fn(),
    updatePaymentRecord: vi.fn(),
    deletePaymentRecord: vi.fn(),
    markPaymentAsPaid: vi.fn()
  })
}))

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={new QueryClient()}>{children}</QueryClientProvider>
)


describe('useFinanceData', () => {
  it('returns calculated values from financial data', () => {
    const { result } = renderHook(() => useFinanceData(), { wrapper })

    expect(result.current.getNetIncome()).toBe(1500)
    expect(result.current.getTotalExpenses()).toBe(400)
    expect(result.current.getNetBalance()).toBe(1100)
    expect(result.current.getTotalDebt()).toBe(1700)
    expect(result.current.getPendingReimbursements()).toBe(70)
  })

  it('filters calculations by month when option provided', () => {
    const { result } = renderHook(() => useFinanceData(undefined, { month: prevMonth }), { wrapper })

    expect(result.current.getNetIncome()).toBe(500)
    expect(result.current.getTotalExpenses()).toBe(100)
    expect(result.current.getNetBalance()).toBe(400)
    expect(result.current.getPendingReimbursements()).toBe(20)
  })

  it('exposes helper functions for month calculations', () => {
    const { result } = renderHook(() => useFinanceData(), { wrapper })

    expect(result.current.getNetIncomeFor(prevMonth)).toBe(500)
    expect(result.current.getTotalExpensesFor(prevMonth)).toBe(100)
    expect(result.current.getNetBalanceFor(prevMonth)).toBe(400)
  })

  it('logs actions when adding income', async () => {
    const { result } = renderHook(() => useFinanceData(), { wrapper })

    const newIncome = { ...income, id: 'another-id' }

    await act(async () => {
      await result.current.addIncome(newIncome)
    })

    expect(auditLog).toHaveBeenCalledWith('income_add_attempt', { userId: 'user-1' })
    expect(auditLog).toHaveBeenCalledWith('income_add_success', { userId: 'user-1' })
    expect(addIncomeMock).toHaveBeenCalledWith(newIncome)
  })
})
