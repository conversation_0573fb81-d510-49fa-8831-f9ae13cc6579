
// Content Security Policy and security headers implementation
export const initializeSecurityHeaders = (): void => {
  // Nonce used for allowing inline scripts without resorting to 'unsafe-inline'
  const nonceArray = new Uint8Array(16);
  crypto.getRandomValues(nonceArray);
  const nonce = btoa(String.fromCharCode(...nonceArray));

  // CSP implementation for enhanced XSS protection
  const cspDirectives = {
    'default-src': ["'self'"],
    'script-src': ["'self'", `'nonce-${nonce}'`, 'https://accounts.google.com', 'https://apis.google.com'],
    // Inline styles are still required throughout the app
    'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
    'font-src': ["'self'", 'https://fonts.gstatic.com'],
    'img-src': ["'self'", 'data:', 'https:', 'blob:'],
    'connect-src': ["'self'", 'https://qtxqvusyyjhylfqefzli.supabase.co', 'wss://qtxqvusyyjhylfqefzli.supabase.co'],
    'frame-src': ["'self'", 'https://accounts.google.com'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'upgrade-insecure-requests': []
  };

  const cspString = Object.entries(cspDirectives)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');

  // Set CSP meta tag
  let cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]') as HTMLMetaElement;
  if (!cspMeta) {
    cspMeta = document.createElement('meta');
    cspMeta.httpEquiv = 'Content-Security-Policy';
    document.head.appendChild(cspMeta);
  }
  cspMeta.content = cspString;

  // Apply the nonce to all script tags so they comply with the CSP
  document.querySelectorAll('script').forEach(script => {
    if (!script.hasAttribute('nonce')) {
      script.setAttribute('nonce', nonce);
    }
  });

  // Additional security headers via meta tags
  // Note: X-Frame-Options and Permissions-Policy are configured via HTTP headers in vercel.json
  // These headers should NOT be set via meta tags as they cause browser errors
  const securityHeaders = [
    { name: 'X-Content-Type-Options', content: 'nosniff' },
    { name: 'Referrer-Policy', content: 'strict-origin-when-cross-origin' }
  ];

  securityHeaders.forEach(({ name, content }) => {
    let meta = document.querySelector(`meta[http-equiv="${name}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.httpEquiv = name;
      document.head.appendChild(meta);
    }
    meta.content = content;
  });
};

// XSS protection for dynamic content
export const sanitizeHTML = (html: string): string => {
  const temp = document.createElement('div');
  temp.textContent = html;
  return temp.innerHTML;
};

// Secure error handling to prevent information leakage
export const sanitizeErrorMessage = (error: unknown): string => {
  if (typeof error === 'string') {
    return sanitizeHTML(error);
  }
  
  if (error instanceof Error) {
    // Remove sensitive patterns from error messages
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /key/gi,
      /secret/gi,
      /credential/gi,
      /auth/gi
    ];
    
    let message = error.message;
    sensitivePatterns.forEach(pattern => {
      message = message.replace(pattern, '[PROTECTED]');
    });
    
    return sanitizeHTML(message);
  }
  
  return 'Ha ocurrido un error inesperado';
};

