import { logger } from "@/utils/logger";

import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { strongPasswordSchema } from '@/utils/security/passwordValidation';
import { sanitizeInput } from '@/utils/security/sanitization';
import { rateLimiter } from '@/utils/security/rateLimiter';

export class AuthMethods {
  private attemptCounts: Map<string, { count: number; lastAttempt: number }> = new Map();
  private readonly MAX_ATTEMPTS = 3;
  private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

  private isRateLimited(identifier: string): boolean {
    const attempts = this.attemptCounts.get(identifier);
    if (!attempts) return false;

    const now = Date.now();
    if (now - attempts.lastAttempt > this.LOCKOUT_DURATION) {
      this.attemptCounts.delete(identifier);
      return false;
    }

    return attempts.count >= this.MAX_ATTEMPTS;
  }

  private recordAttempt(identifier: string, success: boolean): void {
    if (success) {
      this.attemptCounts.delete(identifier);
      return;
    }

    const now = Date.now();
    const attempts = this.attemptCounts.get(identifier) || { count: 0, lastAttempt: now };
    
    attempts.count += 1;
    attempts.lastAttempt = now;
    this.attemptCounts.set(identifier, attempts);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      if (!currentPassword || !newPassword) {
        throw new Error('Ambas contraseñas son requeridas');
      }

      // Sanitize inputs
      // const sanitizedCurrentPassword = sanitizeInput(currentPassword); // Not used
      const sanitizedNewPassword = sanitizeInput(newPassword);

      // Validate new password with enhanced security requirements
      try {
        strongPasswordSchema.parse(sanitizedNewPassword);
      } catch (error: unknown) {
        if (error && typeof error === 'object' && 'errors' in error) {
          // @ts-expect-error indexing
          const firstMessage = (error as { errors?: { message?: string }[] }).errors?.[0]?.message;
          throw new Error(firstMessage || 'La nueva contraseña no cumple con los requisitos de seguridad');
        }
        throw new Error('La nueva contraseña no cumple con los requisitos de seguridad');
      }

      logger.debug('AuthMethods: Changing password with enhanced security validation');

      const { data: user } = await supabase.auth.getUser();
      if (!user.user?.email) {
        throw new Error('Usuario no autenticado');
      }

      // Check rate limiting
      const identifier = user.user.email;
      if (this.isRateLimited(identifier)) {
        throw new Error('Demasiados intentos. Inténtalo de nuevo en 15 minutos.');
      }

      // Additional client-side rate limiting
      if (!rateLimiter.checkLimit(`password_change_${identifier}`, 2, 600000)) { // 2 attempts per 10 minutes
        throw new Error('Demasiados intentos de cambio de contraseña. Espera 10 minutos.');
      }

      // Use Supabase's built-in password update which requires current session
      const { error } = await supabase.auth.updateUser({
        password: sanitizedNewPassword
      });

      if (error) {
        this.recordAttempt(identifier, false);
        
        // Enhanced error handling
        if (error.message.includes('rate limit')) {
          throw new Error('Demasiados intentos. Espera antes de intentar nuevamente.');
        } else if (error.message.includes('weak password')) {
          throw new Error('La contraseña no cumple con los requisitos de seguridad.');
        } else {
          throw new Error('Error al cambiar la contraseña. Verifica tu contraseña actual.');
        }
      }

      this.recordAttempt(identifier, true);
      rateLimiter.clearAttempts(`password_change_${identifier}`);
      
      logger.debug('AuthMethods: Password changed successfully with enhanced security');
      toast.success('Contraseña actualizada exitosamente');
      
      // Log security event
      this.logPasswordChangeEvent(identifier);
      
    } catch (error: unknown) {
      const errMsg = error instanceof Error ? error.message : JSON.stringify(error);
      console.error('AuthMethods: Error changing password:', errMsg);
      throw new Error(errMsg || 'Error al cambiar la contraseña');
    }
  }

  private logPasswordChangeEvent(userEmail: string): void {
    try {
      // Store security event in localStorage for client-side tracking
      const securityEvent = {
        type: 'password_change',
        email: userEmail,
        timestamp: new Date().toISOString(),
        ip: 'client-side', // Would be replaced with actual IP in server-side implementation
        userAgent: navigator.userAgent.slice(0, 100)
      };
      
      const existingEvents = JSON.parse(localStorage.getItem('finanz_security_events') || '[]');
      const updatedEvents = [securityEvent, ...existingEvents].slice(0, 50); // Keep last 50 events
      localStorage.setItem('finanz_security_events', JSON.stringify(updatedEvents));
      
    } catch (error) {
      console.warn('Failed to log password change event:', error);
    }
  }

  async setupTwoFactorAuth(): Promise<{ secret: string; qrCodeUrl: string }> {
    try {
      // Enhanced 2FA setup with better security messaging
      throw new Error('La configuración de autenticación de dos factores debe implementarse en el servidor por seguridad. Esta funcionalidad estará disponible próximamente con validación server-side completa.');
    } catch (error) {
      console.error('Error setting up 2FA:', error);
      throw error;
    }
  }

  async setupBiometricAuth(): Promise<void> {
    try {
      if (!('credentials' in navigator)) {
        throw new Error('WebAuthn no está soportado en este navegador');
      }

      // Enhanced biometric setup with better error handling
      const challenge = new Uint8Array(32);
      crypto.getRandomValues(challenge);

      const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
        challenge,
        rp: { 
          name: 'FinanzApp - Gestión Financiera Segura',
          id: window.location.hostname
        },
        user: {
          id: new Uint8Array(16),
          name: '<EMAIL>',
          displayName: 'Usuario FinanzApp'
        },
        pubKeyCredParams: [
          { alg: -7, type: 'public-key' }, // ES256
          { alg: -257, type: 'public-key' } // RS256
        ],
        authenticatorSelection: {
          authenticatorAttachment: 'platform',
          userVerification: 'required',
          requireResidentKey: false
        },
        timeout: 60000,
        attestation: 'direct'
      };

      const credential = await navigator.credentials.create({
        publicKey: publicKeyCredentialCreationOptions
      });

      if (credential) {
        // Store biometric configuration securely
        const biometricConfig = {
          enabled: true,
          credentialId: credential.id,
          setupDate: new Date().toISOString(),
          deviceInfo: {
            userAgent: navigator.userAgent.slice(0, 100),
            platform: navigator.platform
          }
        };
        
        localStorage.setItem('finanz_biometric_config', JSON.stringify(biometricConfig));
        toast.success('Autenticación biométrica configurada exitosamente');
        
        // Log security event
        this.logBiometricSetupEvent();
      }
    } catch (error: unknown) {
      console.error('Error setting up biometric auth:', error);
      let errorMessage = 'Error al configurar la autenticación biométrica';
      if (error instanceof DOMException) {
        switch (error.name) {
          case 'NotSupportedError':
            errorMessage = 'La autenticación biométrica no está disponible en este dispositivo';
            break;
          case 'NotAllowedError':
            errorMessage = 'Permiso denegado para la autenticación biométrica';
            break;
          case 'SecurityError':
            errorMessage = 'Error de seguridad al configurar la autenticación biométrica';
            break;
        }
      }
      toast.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  private logBiometricSetupEvent(): void {
    try {
      const securityEvent = {
        type: 'biometric_setup',
        timestamp: new Date().toISOString(),
        deviceInfo: {
          userAgent: navigator.userAgent.slice(0, 100),
          platform: navigator.platform
        }
      };
      
      const existingEvents = JSON.parse(localStorage.getItem('finanz_security_events') || '[]');
      const updatedEvents = [securityEvent, ...existingEvents].slice(0, 50);
      localStorage.setItem('finanz_security_events', JSON.stringify(updatedEvents));
      
    } catch (error) {
      console.warn('Failed to log biometric setup event:', error);
    }
  }

  async verifyTwoFactorAuth(token: string): Promise<boolean> {
    try {
      const sanitizedToken = sanitizeInput(token);
      
      if (!sanitizedToken || sanitizedToken.length !== 6 || !/^\d{6}$/.test(sanitizedToken)) {
        throw new Error('Código de verificación inválido');
      }
      
      // Enhanced 2FA verification with better security
      toast.error('La verificación 2FA debe implementarse en el servidor por seguridad');
      return false;
    } catch (error) {
      console.error('Error verifying 2FA:', error);
      return false;
    }
  }
}
