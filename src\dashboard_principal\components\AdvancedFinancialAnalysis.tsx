
import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useDashboardPayments } from '../hooks/useDashboardPayments';
import { TrendingUp } from 'lucide-react';
import { PieChart as RechartsPieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';
import { useBreakpoint } from '@/hooks/useBreakpoint';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const AdvancedFinancialAnalysis: React.FC = () => {
  // CORREGIDO: Obtener fecha actual simplificada
  const today = new Date();
  const currentMonth = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
  
  const { incomes, creditCards, loans, personalDebts } = useFinanceData(['incomes', 'debts'], { currentMonth: true });
  const { totalMonthlyPayments, paymentBreakdown } = useDashboardPayments();
  const isDark = useIsDarkMode();
  const { isMobile } = useBreakpoint();

  console.log('AdvancedFinancialAnalysis: Fecha actual:', today.toISOString());
  console.log('AdvancedFinancialAnalysis: Mes actual calculado:', currentMonth);

  // CORREGIDO: Análisis por moneda usando únicamente datos del mes actual
  const currencyAnalysis = useMemo(() => {
    const analysis = {
      DOP: { income: 0, payments: 0, debt: 0 },
      USD: { income: 0, payments: 0, debt: 0 }
    };

    // CORREGIDO: Filtrar estrictamente por el mes actual
    const currentMonthIncomes = incomes.filter(income => {
      const match = income.month === currentMonth;
      console.log('AdvancedFinancialAnalysis: Comparando income.month:', income.month, 'con currentMonth:', currentMonth, 'match:', match);
      return match;
    });
    
    console.log('AdvancedFinancialAnalysis: Ingresos filtrados del mes actual:', currentMonthIncomes.length);
    
    currentMonthIncomes.forEach(income => {
      const netIncome = income.netIncome || 0;
      console.log('AdvancedFinancialAnalysis: Agregando ingreso neto:', netIncome, 'moneda:', income.currency);
      analysis[income.currency as 'DOP' | 'USD'].income += netIncome;
    });

    console.log('AdvancedFinancialAnalysis: Análisis de ingresos por moneda:', analysis.DOP.income, analysis.USD.income);

    // Asignar pagos del mes (por ahora asumimos DOP)
    analysis.DOP.payments = totalMonthlyPayments;

    // Calcular deudas totales por moneda
    creditCards.forEach(card => {
      analysis[card.currency as 'DOP' | 'USD'].debt += card.currentBalance || 0;
    });

    loans.forEach(loan => {
      analysis[loan.currency as 'DOP' | 'USD'].debt += loan.totalAmount || 0;
    });

    personalDebts.forEach(debt => {
      const amount = debt.remainingBalance || debt.amount || 0;
      analysis[debt.currency as 'DOP' | 'USD'].debt += amount;
    });

    console.log('AdvancedFinancialAnalysis: Análisis final por moneda:', analysis);
    return analysis;
  }, [incomes, totalMonthlyPayments, creditCards, loans, personalDebts, currentMonth]);

  // Datos para gráficos
  const pieData = [
    { name: 'Ingresos DOP', value: currencyAnalysis.DOP.income, currency: 'DOP' },
    { name: 'Ingresos USD', value: currencyAnalysis.USD.income, currency: 'USD' },
    { name: 'Pagos DOP', value: currencyAnalysis.DOP.payments, currency: 'DOP' },
    { name: 'Pagos USD', value: currencyAnalysis.USD.payments, currency: 'USD' }
  ].filter(item => item.value > 0);

  const barData = [
    {
      category: 'Ingresos',
      DOP: currencyAnalysis.DOP.income,
      USD: currencyAnalysis.USD.income * 60
    },
    {
      category: 'Pagos',
      DOP: currencyAnalysis.DOP.payments,
      USD: currencyAnalysis.USD.payments * 60
    },
    {
      category: 'Deudas',
      DOP: currencyAnalysis.DOP.debt,
      USD: currencyAnalysis.USD.debt * 60
    }
  ];

  const formatCurrency = (amount: number, currency: 'DOP' | 'USD' = 'DOP') => {
    const symbol = currency === 'USD' ? '$' : 'RD$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Análisis Financiero Avanzado
        </CardTitle>
        <CardDescription>
          Vista consolidada de tu situación financiera por monedas - {today.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })} (Mes Actual)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="currency">Por Moneda</TabsTrigger>
            <TabsTrigger value="breakdown">Desglose Pagos</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold">Resumen por Moneda - {today.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })}</h4>
                
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h5 className="font-medium text-blue-800 mb-3">Pesos Dominicanos (RD$)</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Ingresos:</span>
                      <span className="font-semibold text-green-600">
                        {formatCurrency(currencyAnalysis.DOP.income, 'DOP')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pagos del Mes:</span>
                      <span className="font-semibold text-red-600">
                        {formatCurrency(currencyAnalysis.DOP.payments, 'DOP')}
                      </span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium">Balance:</span>
                      <span className={`font-bold ${
                        currencyAnalysis.DOP.income - currencyAnalysis.DOP.payments >= 0 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {formatCurrency(currencyAnalysis.DOP.income - currencyAnalysis.DOP.payments, 'DOP')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-50 rounded-lg">
                  <h5 className="font-medium text-green-800 mb-3">Dólares Americanos (USD)</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Ingresos:</span>
                      <span className="font-semibold text-green-600">
                        {formatCurrency(currencyAnalysis.USD.income, 'USD')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pagos del Mes:</span>
                      <span className="font-semibold text-red-600">
                        {formatCurrency(currencyAnalysis.USD.payments, 'USD')}
                      </span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium">Balance:</span>
                      <span className={`font-bold ${
                        currencyAnalysis.USD.income - currencyAnalysis.USD.payments >= 0 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {formatCurrency(currencyAnalysis.USD.income - currencyAnalysis.USD.payments, 'USD')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold">Distribución Visual</h4>
                <div className="chart-container-responsive">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={isMobile ? 60 : 80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: any) => formatCurrency(value)} />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="currency" className="space-y-4">
            <div className="chart-container-responsive">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={barData}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
                  <XAxis dataKey="category" stroke={isDark ? '#e5e7eb' : '#374151'} />
                  <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} />
                  <Tooltip
                    formatter={(value: any, name: string) => [
                      `${name === 'USD' ? '$' : 'RD$'}${value.toLocaleString()}`,
                      name === 'USD' ? 'USD (convertido)' : 'RD$'
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="DOP" fill="#0088FE" name="RD$" />
                  <Bar dataKey="USD" fill="#00C49F" name="USD" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="breakdown" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-yellow-50 rounded-lg">
                <h5 className="font-medium text-yellow-800 mb-2">Pagos Pendientes</h5>
                <p className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(paymentBreakdown.pending, 'DOP')}
                </p>
              </div>
              <div className="p-4 bg-red-50 rounded-lg">
                <h5 className="font-medium text-red-800 mb-2">Pagos Vencidos</h5>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(paymentBreakdown.overdue, 'DOP')}
                </p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h5 className="font-medium text-green-800 mb-2">Pagados Este Mes</h5>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(paymentBreakdown.paid, 'DOP')}
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
