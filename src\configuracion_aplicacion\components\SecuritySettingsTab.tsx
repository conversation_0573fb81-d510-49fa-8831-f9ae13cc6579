
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Shield, Loader2 } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { SecurityFormData } from '../schemas/settingsSchemas';

interface SecuritySettingsTabProps {
  securityForm: UseFormReturn<SecurityFormData>;
  handleSaveSecuritySettings: () => Promise<void>;
  isLoading: boolean;
}

export const SecuritySettingsTab: React.FC<SecuritySettingsTabProps> = ({
  securityForm,
  handleSaveSecuritySettings,
  isLoading,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          Configuración de Seguridad
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...securityForm}>
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Autenticación</h3>
              <div className="space-y-3">
                <FormField
                  control={securityForm.control}
                  name="twoFactorAuth"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Autenticación de Dos Factores</FormLabel>
                        <p className="text-sm text-muted-foreground">Agrega una capa extra de seguridad a tu cuenta</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
                
                <FormField
                  control={securityForm.control}
                  name="biometricAuth"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Autenticación Biométrica</FormLabel>
                        <p className="text-sm text-muted-foreground">Usa huella dactilar o reconocimiento facial</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Sesión</h3>
              <div className="space-y-3">
                <FormField
                  control={securityForm.control}
                  name="sessionTimeout"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tiempo de Sesión (minutos)</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="15">15 minutos</SelectItem>
                          <SelectItem value="30">30 minutos</SelectItem>
                          <SelectItem value="60">1 hora</SelectItem>
                          <SelectItem value="120">2 horas</SelectItem>
                          <SelectItem value="0">Sin límite</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={securityForm.control}
                  name="autoLogout"
                  render={({ field }) => (
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel>Cerrar Sesión Automáticamente</FormLabel>
                        <p className="text-sm text-muted-foreground">Cierra sesión automáticamente por inactividad</p>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </div>
                  )}
                />
              </div>
            </div>

            <Button 
              type="button" 
              onClick={handleSaveSecuritySettings} 
              disabled={isLoading} 
              className="w-full md:w-auto"
            >
              {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Guardar Configuración
            </Button>
          </div>
        </Form>
      </CardContent>
    </Card>
  );
};
