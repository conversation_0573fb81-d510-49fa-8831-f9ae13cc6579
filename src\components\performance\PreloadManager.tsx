import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

// Importar las funciones de preload de App.tsx
import {
  IndexPage,
  AuthPage,
  FeaturesPage,
  PrivacyPolicyPage,
  TermsOfServicePage,
  SupportPage,
  DashboardPage,
  ExpensesPage,
  IncomePage,
  LoansPage,
  GoalsPage,
  ConfigurationPage,
  SubscriptionsPage,
  ReimbursementsPage,
  PaymentsPage,
  FinancialAdvicePage,
  DiagnosticPage,
} from '@/App';

// Mapeo de rutas a componentes para preload inteligente
const ROUTE_PRELOAD_MAP: Record<string, Array<() => Promise<unknown>>> = {
  '/': [AuthPage.preload!, FeaturesPage.preload!],
  '/auth': [DashboardPage.preload!],
  '/app/dashboard': [ExpensesPage.preload!, IncomePage.preload!],
  '/app/expenses': [IncomePage.preload!, DashboardPage.preload!],
  '/app/income': [ExpensesPage.preload!, GoalsPage.preload!],
  '/app/loans': [GoalsPage.preload!, ExpensesPage.preload!],
  '/app/goals': [LoansPage.preload!, IncomePage.preload!],
  '/app/subscriptions': [ExpensesPage.preload!, PaymentsPage.preload!],
  '/app/reimbursements': [ExpensesPage.preload!, PaymentsPage.preload!],
  '/app/payments': [SubscriptionsPage.preload!, ReimbursementsPage.preload!],
  '/app/financial-advice': [DiagnosticPage.preload!, DashboardPage.preload!],
  '/app/diagnostic': [FinancialAdvicePage.preload!, DashboardPage.preload!],
  '/app/configuration': [DashboardPage.preload!],
};

// Componentes críticos que siempre deben precargarse
const CRITICAL_PRELOADS = [
  DashboardPage.preload!,
  ExpensesPage.preload!,
  IncomePage.preload!,
];

interface PreloadManagerProps {
  children?: React.ReactNode;
}

export const PreloadManager: React.FC<PreloadManagerProps> = ({ children }) => {
  const location = useLocation();

  // Preload inteligente basado en la ruta actual
  const preloadForRoute = useCallback((path: string) => {
    const preloaders = ROUTE_PRELOAD_MAP[path];
    if (preloaders) {
      // Usar requestIdleCallback para no bloquear el hilo principal
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          preloaders.forEach(preloader => {
            preloader().catch(console.warn);
          });
        });
      } else {
        // Fallback para navegadores que no soportan requestIdleCallback
        setTimeout(() => {
          preloaders.forEach(preloader => {
            preloader().catch(console.warn);
          });
        }, 100);
      }
    }
  }, []);

  // Preload crítico al montar el componente
  useEffect(() => {
    // Preload de componentes críticos con prioridad baja
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        CRITICAL_PRELOADS.forEach(preloader => {
          preloader().catch(console.warn);
        });
      }, { timeout: 2000 });
    } else {
      setTimeout(() => {
        CRITICAL_PRELOADS.forEach(preloader => {
          preloader().catch(console.warn);
        });
      }, 1000);
    }
  }, []);

  // Preload basado en cambios de ruta
  useEffect(() => {
    preloadForRoute(location.pathname);
  }, [location.pathname, preloadForRoute]);

  // Preload al hacer hover sobre enlaces (solo en desktop)
  useEffect(() => {
    const handleLinkHover = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && link.href.includes(window.location.origin)) {
        const path = new URL(link.href).pathname;
        preloadForRoute(path);
      }
    };

    // Solo en dispositivos con hover (desktop)
    if (window.matchMedia('(hover: hover)').matches) {
      document.addEventListener('mouseover', handleLinkHover);
      return () => document.removeEventListener('mouseover', handleLinkHover);
    }
  }, [preloadForRoute]);

  return <>{children}</>;
};

// Hook para preload manual
export const usePreload = () => {
  const preloadRoute = useCallback((path: string) => {
    const preloaders = ROUTE_PRELOAD_MAP[path];
    if (preloaders) {
      preloaders.forEach(preloader => {
        preloader().catch(console.warn);
      });
    }
  }, []);

  return { preloadRoute };
};

// Componente para preload de recursos críticos
export const CriticalResourcePreloader: React.FC = () => {
  useEffect(() => {
    // Preload de fuentes críticas
    const preloadFont = (href: string) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      link.href = href;
      document.head.appendChild(link);
    };

    // Preload de CSS crítico si es necesario
    const preloadCSS = (href: string) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      document.head.appendChild(link);
    };

    // Solo ejecutar en producción
    if (import.meta.env.PROD) {
      // Aquí puedes agregar URLs específicas de fuentes o CSS crítico
      // preloadFont('/fonts/inter-var.woff2');
    }
  }, []);

  return null;
};
