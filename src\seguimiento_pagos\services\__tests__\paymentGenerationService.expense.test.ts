import { describe, it, expect, vi } from 'vitest'
import { PaymentGenerationService } from '@/seguimiento_pagos/services/paymentGenerationService'
import type { TemporalPeriod } from '@/seguimiento_pagos/hooks/useTemporalNavigation'
import type { Expense } from '@/types'

vi.mock('@/utils/logger', () => ({
  logger: { debug: vi.fn(), error: vi.fn(), info: vi.fn(), warn: vi.fn() }
}))

describe('PaymentGenerationService.generateFromExpenses', () => {
  const targetPeriod: TemporalPeriod = {
    year: 2024,
    month: 5,
    startDate: '2024-05-01',
    endDate: '2024-05-31',
    displayName: 'May 2024',
    isCurrentMonth: false,
    isPastMonth: false,
    isFutureMonth: false,
    displayMode: 'month'
  }

  const baseExpense: Omit<Expense, 'id' | 'date' | 'month' | 'paymentDate' | 'isRecurring'> = {
    type: 'Fijo',
    categoryId: 'c1',
    categoryName: 'Utilities',
    amount: 100,
    description: 'expense',
    paymentMethod: 'cash',
    status: 'pending',
    currency: 'DOP',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  }

  it('creates payments for recurring expenses in the target month', () => {
    const service = new PaymentGenerationService(targetPeriod)
    const expenses: Expense[] = [
      {
        id: 'r1',
        date: '2024-01-10',
        month: '2024-01',
        paymentDate: '5',
        isRecurring: true,
        ...baseExpense
      },
      {
        id: 'r2',
        date: '2024-02-20',
        month: '2024-02',
        paymentDate: '20',
        isRecurring: true,
        ...baseExpense
      }
    ]

    const payments = service.generateFromExpenses(expenses)

    expect(payments).toHaveLength(2)
    expect(payments[0]).toMatchObject({ referenceId: 'r1', dueDate: '2024-05-05' })
    expect(payments[1]).toMatchObject({ referenceId: 'r2', dueDate: '2024-05-20' })
  })

  it('filters non-recurring expenses by month', () => {
    const service = new PaymentGenerationService(targetPeriod)
    const expenses: Expense[] = [
      {
        id: 'n1',
        date: '2024-05-10',
        month: '2024-05',
        isRecurring: false,
        ...baseExpense
      },
      {
        id: 'n2',
        date: '2024-06-01',
        month: '2024-06',
        isRecurring: false,
        ...baseExpense
      }
    ]

    const payments = service.generateFromExpenses(expenses)

    expect(payments).toHaveLength(1)
    expect(payments[0].referenceId).toBe('n1')
  })
})
