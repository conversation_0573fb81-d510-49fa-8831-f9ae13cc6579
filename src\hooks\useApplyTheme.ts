
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { applyAppTheme } from '@/main';
import { ROUTES } from '@/constants/routes';

/**
 * Listen for route changes and apply the saved theme for private pages.
 * Public routes always use the light theme.
 */
export const useApplyTheme = () => {
  const location = useLocation();

  useEffect(() => {
    const publicPaths = [
      ROUTES.ROOT,
      ROUTES.AUTH,
      ROUTES.FEATURES,
      ROUTES.SUPPORT,
    ];

    if (publicPaths.includes(location.pathname)) {
      document.documentElement.classList.remove('dark');
    } else {
      applyAppTheme();
    }
  }, [location.pathname]);
};
