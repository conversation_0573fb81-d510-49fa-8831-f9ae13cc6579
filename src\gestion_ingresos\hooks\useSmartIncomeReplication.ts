
import { useState } from 'react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { Income } from '@/types';
import { toast } from 'sonner';

export const useSmartIncomeReplication = () => {
  const { incomes, addIncome } = useFinanceData();
  const [isReplicating, setIsReplicating] = useState(false);

  const getNextAvailableMonth = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    let nextMonth = currentMonth;
    let nextYear = currentYear;
    
    const currentMonthStr = `${nextYear}-${nextMonth.toString().padStart(2, '0')}`;
    const hasCurrentMonth = incomes.some(income => income.month === currentMonthStr);
    
    if (hasCurrentMonth) {
      nextMonth++;
      if (nextMonth > 12) {
        nextMonth = 1;
        nextYear++;
      }
    }
    
    return `${nextYear}-${nextMonth.toString().padStart(2, '0')}`;
  };

  const getSmartIncomeData = (): Income | null => {
    const realIncomes = incomes
      .filter(income => !income.isProjected)
      .sort((a, b) => b.month.localeCompare(a.month));
    
    if (realIncomes.length === 0) return null;

    // Si solo hay un registro, usarlo directamente
    if (realIncomes.length === 1) {
      return realIncomes[0];
    }

    // Análisis inteligente basado en los últimos 3 meses
    const lastThreeMonths = realIncomes.slice(0, 3);
    const baseData = realIncomes[0];

    // Calcular promedios y tendencias
    const avgFixedSalary = lastThreeMonths.reduce((sum, income) => sum + income.fixedSalary, 0) / lastThreeMonths.length;
    const avgVariablePercentage = lastThreeMonths.reduce((sum, income) => sum + income.variablePercentage, 0) / lastThreeMonths.length;
    const avgPerformancePercentage = lastThreeMonths.reduce((sum, income) => sum + income.performancePercentage, 0) / lastThreeMonths.length;

    // Detectar patrones estacionales (ejemplo: bonos trimestrales)
    const currentMonth = new Date().getMonth() + 1;
    const isQuarterEnd = [3, 6, 9, 12].includes(currentMonth);
    const hasQuarterlyPattern = lastThreeMonths.some(income => income.quarterlyIncentive > 0);

    // Predicción inteligente del incentivo trimestral
    let predictedQuarterlyIncentive = 0;
    if (isQuarterEnd && hasQuarterlyPattern) {
      const quarterlyIncomes = realIncomes.filter(income => {
        const month = parseInt(income.month.split('-')[1]);
        return [3, 6, 9, 12].includes(month) && income.quarterlyIncentive > 0;
      });
      if (quarterlyIncomes.length > 0) {
        predictedQuarterlyIncentive = quarterlyIncomes.reduce((sum, income) => sum + income.quarterlyIncentive, 0) / quarterlyIncomes.length;
      }
    }

    return {
      ...baseData,
      fixedSalary: Math.round(avgFixedSalary),
      variablePercentage: Math.round(avgVariablePercentage * 10) / 10,
      performancePercentage: Math.round(avgPerformancePercentage * 10) / 10,
      quarterlyIncentive: Math.round(predictedQuarterlyIncentive),
      variableAmount: 0, // Se recalculará
      grossIncome: 0, // Se recalculará
      netIncome: 0, // Se recalculará
    };
  };

  const generateRecommendations = (lastIncomeData: Income): string[] => {
    const recommendations: string[] = [];
    const currentMonth = new Date().getMonth() + 1;
    
    // Recomendaciones basadas en patrones históricos
    if (lastIncomeData.variablePercentage > 15) {
      recommendations.push('💡 Variable alto detectado. Considere escenarios conservadores para planificación.');
    }
    
    if ([3, 6, 9, 12].includes(currentMonth)) {
      recommendations.push('📊 Mes de fin de trimestre. Revise si aplica incentivo trimestral.');
    }
    
    if (lastIncomeData.payrollLoan > 0) {
      recommendations.push('⚠️ Préstamo nómina activo. Verifique si el monto ha cambiado.');
    }
    
    if (lastIncomeData.vehicleDepreciation > 0) {
      recommendations.push('🚗 Depreciación vehicular incluida. Confirme si sigue aplicando.');
    }

    const currentDate = new Date();
    if (currentDate.getMonth() === 11) { // Diciembre
      recommendations.push('🎄 Mes de diciembre. Considere regalía navideña si aplica.');
    }

    return recommendations;
  };

  const replicateLastMonth = async (): Promise<{ income: Income | null; recommendations: string[] }> => {
    setIsReplicating(true);
    
    try {
      const smartIncomeData = getSmartIncomeData();
      
      if (!smartIncomeData) {
        toast.error('No se encontraron datos de ingresos previos para replicar');
        return { income: null, recommendations: [] };
      }

      const nextMonth = getNextAvailableMonth();
      
      // Verificar si ya existe
      const existingIncome = incomes.find(income => income.month === nextMonth);
      if (existingIncome) {
        toast.error(`Ya existe un registro para ${nextMonth}`);
        return { income: null, recommendations: [] };
      }

      // Crear datos replicados con inteligencia
      const replicatedData: Omit<Income, 'id'> = {
        month: nextMonth,
        currency: smartIncomeData.currency,
        fixedSalary: smartIncomeData.fixedSalary,
        variablePercentage: smartIncomeData.variablePercentage,
        variableAmount: 0,
        quarterlyIncentive: smartIncomeData.quarterlyIncentive,
        performancePercentage: smartIncomeData.performancePercentage,
        vehicleDepreciation: smartIncomeData.vehicleDepreciation,
        legalDeductions: smartIncomeData.legalDeductions,
        payrollLoan: smartIncomeData.payrollLoan,
        grossIncome: 0,
        netIncome: 0,
        otherIncomeItems: [...smartIncomeData.otherIncomeItems], // Clonar array
        variableScenarios: {
          base: smartIncomeData.variableScenarios?.base || 0,
          medium: smartIncomeData.variableScenarios?.medium || 0,
          optimal: smartIncomeData.variableScenarios?.optimal || 0
        },
        isProjected: true // Marcar como proyección inicial
      };

      await addIncome(replicatedData);
      
      const monthName = new Date(nextMonth + '-01').toLocaleDateString('es-ES', { 
        month: 'long', 
        year: 'numeric' 
      });
      
      const recommendations = generateRecommendations(smartIncomeData);
      
      toast.success(`Datos inteligentes replicados para ${monthName}. Revise las recomendaciones.`);
      
      return { 
        income: { ...replicatedData, id: Date.now().toString() }, 
        recommendations 
      };
      
    } catch (error) {
      console.error('Error replicating income data:', error);
      toast.error('Error al replicar los datos del mes anterior');
      return { income: null, recommendations: [] };
    } finally {
      setIsReplicating(false);
    }
  };

  const canReplicate = () => {
    const smartIncomeData = getSmartIncomeData();
    return !!smartIncomeData && !isReplicating;
  };

  const getReplicationPreview = () => {
    const smartIncomeData = getSmartIncomeData();
    if (!smartIncomeData) return null;

    const nextMonth = getNextAvailableMonth();
    const monthName = new Date(nextMonth + '-01').toLocaleDateString('es-ES', { 
      month: 'long', 
      year: 'numeric' 
    });

    return {
      month: monthName,
      fixedSalary: smartIncomeData.fixedSalary,
      variablePercentage: smartIncomeData.variablePercentage,
      quarterlyIncentive: smartIncomeData.quarterlyIncentive,
      recommendations: generateRecommendations(smartIncomeData)
    };
  };

  return {
    replicateLastMonth,
    canReplicate: canReplicate(),
    isReplicating,
    getReplicationPreview,
    smartIncomeData: getSmartIncomeData()
  };
};
