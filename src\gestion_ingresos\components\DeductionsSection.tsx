
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { NumericInput, formatCurrency } from '@/components/ui/numeric-input';
import { Income as IncomeType } from '@/types';

interface DeductionsSectionProps {
  formData: Partial<IncomeType>;
  setFormData: React.Dispatch<React.SetStateAction<Partial<IncomeType>>>;
  calculations: any;
  isSubmitting?: boolean;
}

export function DeductionsSection({ formData, setFormData, calculations, isSubmitting = false }: DeductionsSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg text-red-600">DEDUCCIONES</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-gray-700">ARS (3.04% del salario cotizable)</span>
            <div className="h-10 px-3 py-2 bg-gray-100 border rounded-md flex items-center">
              <span className="text-red-600 font-medium">
                {formatCurrency(calculations?.arsContribution || 0, formData.currency)}
              </span>
            </div>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">AFP (2.87% del salario cotizable)</span>
            <div className="h-10 px-3 py-2 bg-gray-100 border rounded-md flex items-center">
              <span className="text-red-600 font-medium">
                {formatCurrency(calculations?.afpContribution || 0, formData.currency)}
              </span>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <span className="font-medium">Total ARS + AFP</span>
            <span className="text-lg font-semibold text-red-600">
              {formatCurrency(calculations?.totalARSAFP || 0, formData.currency)}
            </span>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Calculado sobre salario cotizable: {formatCurrency(calculations?.cotizableSalary || 0, formData.currency)}
          </p>
        </div>

        <div className="border-t pt-4">
          <p className="text-base font-semibold mb-3 block">ISR (Escala Progresiva sobre base gravable)</p>
          <div className="h-10 px-3 py-2 bg-gray-100 border rounded-md flex items-center">
            <span className="text-red-600 font-medium">
              {formatCurrency(calculations?.monthlyIRS || 0, formData.currency)}
            </span>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Base gravable (Ingreso Bruto - ARS - AFP): {formatCurrency(calculations?.taxableIncome || 0, formData.currency)}
          </p>
        </div>

        <div>
          <Label htmlFor="legalDeductions">Otras Deducciones Legales</Label>
          <NumericInput
            id="legalDeductions"
            value={formData.legalDeductions || 0}
            onChange={(value) => setFormData(prev => ({ ...prev, legalDeductions: value || 0 }))}
            currency={formData.currency}
            showCurrency
            disabled={isSubmitting}
            autoComplete="off"
          />
        </div>
        <div>
          <Label htmlFor="payrollLoan">Préstamo de Nómina</Label>
          <NumericInput
            id="payrollLoan"
            value={formData.payrollLoan || 0}
            onChange={(value) => setFormData(prev => ({ ...prev, payrollLoan: value || 0 }))}
            currency={formData.currency}
            showCurrency
            disabled={isSubmitting}
            autoComplete="off"
          />
        </div>
      </CardContent>
    </Card>
  );
}
