
import { useState, useEffect, useCallback } from 'react';
import { Session } from '@supabase/supabase-js';
import { securityService } from '@/configuracion_aplicacion/services/securityService';

export const useAuthSecurity = (session: Session | null) => {
  const [isValidSession, setIsValidSession] = useState(true);
  const [shouldLockScreen, setShouldLockScreen] = useState(false);
  const [timeUntilExpiry, setTimeUntilExpiry] = useState(0);
  const [securityConfigsLoaded, setSecurityConfigsLoaded] = useState(false);
  const [deviceTrusted, setDeviceTrusted] = useState(false);

  // Device fingerprinting with multiple data points
  const generateDeviceFingerprint = useCallback(() => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }

    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL(),
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints,
      timestamp: Date.now()
    };

    return btoa(JSON.stringify(fingerprint));
  }, []);

  // Initialize security configurations
  useEffect(() => {
    const initSecurity = async () => {
      try {
        await securityService.loadSettings();
        
        // Check device trust status
        const storedFingerprint = localStorage.getItem('finanz_device_fingerprint');
        const currentFingerprint = generateDeviceFingerprint();
        
        if (storedFingerprint === currentFingerprint) {
          setDeviceTrusted(true);
        } else {
          localStorage.setItem('finanz_device_fingerprint', currentFingerprint);
          setDeviceTrusted(false);
        }
        
        setSecurityConfigsLoaded(true);
      } catch (error) {
        console.error('Failed to initialize security:', error);
        setSecurityConfigsLoaded(false);
      }
    };

    initSecurity();
  }, [generateDeviceFingerprint]);

  // Session validation and monitoring
  useEffect(() => {
    if (!session) {
      setIsValidSession(false);
      setTimeUntilExpiry(0);
      return;
    }

    const validateSession = () => {
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = session.expires_at || 0;
      const timeLeft = expiresAt - now;

      setTimeUntilExpiry(Math.max(0, timeLeft));
      setIsValidSession(timeLeft > 0);
      
      // Lock screen if session is about to expire (5 minutes)
      setShouldLockScreen(timeLeft <= 300 && timeLeft > 0);
    };

    validateSession();
    const interval = setInterval(validateSession, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [session]);

  // Activity monitoring
  const updateActivity = useCallback(() => {
    const now = Date.now();
    localStorage.setItem('finanz_last_activity', now.toString());
    securityService.resetActivity();
  }, []);

  // Monitor user activity
  useEffect(() => {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const handleActivity = () => {
      updateActivity();
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [updateActivity]);

  // Session validation with security checks
  const validateSessionWithSecurity = useCallback(async () => {
    if (!session) return;

    try {
      // Validate session hasn't been tampered with
      const sessionData = JSON.stringify(session);
      const storedHash = localStorage.getItem('finanz_session_hash');
      const currentHash = btoa(sessionData);

      if (storedHash && storedHash !== currentHash) {
        console.warn('Session integrity check failed');
        // Could trigger logout or additional verification
      }

      localStorage.setItem('finanz_session_hash', currentHash);
    } catch (error) {
      console.error('Session validation failed:', error);
    }
  }, [session]);

  const markDeviceAsTrusted = useCallback(() => {
    setDeviceTrusted(true);
    const fingerprint = generateDeviceFingerprint();
    localStorage.setItem('finanz_device_fingerprint', fingerprint);
    localStorage.setItem('finanz_device_trusted', 'true');
  }, [generateDeviceFingerprint]);

  return {
    isValidSession,
    shouldLockScreen,
    timeUntilExpiry,
    securityConfigsLoaded,
    deviceTrusted,
    updateActivity,
    validateSessionWithSecurity,
    markDeviceAsTrusted
  };
};
