
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Shield, Smartphone, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

interface TwoFactorAuthProps {
  onEnable: (method: '2fa' | 'sms', secret?: string) => Promise<void>;
  onVerify: (code: string, method: '2fa' | 'sms') => Promise<boolean>;
  isEnabled: boolean;
  isLoading: boolean;
}

export const TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({
  onEnable,
  onVerify,
  isEnabled,
  isLoading
}) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [secret, setSecret] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [step, setStep] = useState<'setup' | 'verify' | 'complete'>('setup');

  const generateBackupCodes = () => {
    const codes = Array.from({ length: 8 }, () => 
      Math.random().toString(36).substring(2, 8).toUpperCase()
    );
    setBackupCodes(codes);
    return codes;
  };

  const generateSecretKey = () => {
    const secretKey = Math.random().toString(36).substring(2, 15);
    setSecret(secretKey);
    // En una implementación real, aquí se generaría y mostraría un QR Code
    // utilizando una librería como qrcode.react, por ejemplo:
    // const appName = 'FinanzApp';
    // const userEmail = '<EMAIL>'; // Usar email real
    // const qrUrl = `otpauth://totp/${appName}:${userEmail}?secret=${secretKey}&issuer=${appName}`;
    // Aquí se pasaría qrUrl al componente que renderiza el QR.
  };

  const handleEnable2FA = async (method: '2fa' | 'sms') => {
    try {
      if (method === '2fa') {
        generateSecretKey();
      }
      generateBackupCodes();
      await onEnable(method, secret); // 'secret' podría ser relevante para 'onEnable'
      setStep('verify');
    } catch {
      toast.error('Error al configurar 2FA');
    }
  };

  const handleVerify = async () => {
    try {
      const success = await onVerify(verificationCode, 'sms'); // Asumo que 'sms' es el método por defecto o se determinará de otra forma
      if (success) {
        setStep('complete');
        toast.success('2FA configurado exitosamente');
      } else {
        toast.error('Código de verificación incorrecto');
      }
    } catch {
      toast.error('Error al verificar código');
    }
  };

  // useEffect(() => {
  //   // Si se necesitara generar algo al montar el componente:
  //   // generateSecretKey();
  // }, []);

  if (isEnabled) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <Shield className="w-6 h-6 text-green-600" />
          </div>
          <CardTitle className="text-green-700">2FA Activo</CardTitle>
          <CardDescription>
            Tu cuenta está protegida con autenticación de doble factor
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Badge variant="secondary" className="w-full justify-center py-2">
            Verificación requerida para acceder
          </Badge>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Shield className="w-6 h-6 text-blue-600" />
        </div>
        <CardTitle>Configurar 2FA</CardTitle>
        <CardDescription>
          Añade una capa extra de seguridad a tu cuenta
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {step === 'setup' && (
          <Tabs defaultValue="app" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="app">App Autenticación</TabsTrigger>
              <TabsTrigger value="sms">SMS</TabsTrigger>
            </TabsList>
            
            <TabsContent value="app" className="space-y-4">
              <div className="text-center space-y-4">
                <div className="w-32 h-32 bg-gray-100 rounded-lg mx-auto flex items-center justify-center">
                  <p className="text-xs text-gray-500 text-center px-2">
                    QR Code para<br />Google Authenticator
                  </p>
                </div>
                <p className="text-sm text-gray-600">
                  Escanea este código QR con Google Authenticator o Authy
                </p>
                <Button 
                  onClick={() => handleEnable2FA('2fa')}
                  disabled={isLoading}
                  className="w-full"
                >
                  <Smartphone className="w-4 h-4 mr-2" />
                  Configurar con App
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="sms" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Número de teléfono</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+****************"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              <Button 
                onClick={() => handleEnable2FA('sms')}
                disabled={isLoading || !phoneNumber}
                className="w-full"
              >
                Enviar código SMS
              </Button>
            </TabsContent>
          </Tabs>
        )}

        {step === 'verify' && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="verification-code">Código de verificación</Label>
              <Input
                id="verification-code"
                type="text"
                placeholder="000000"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                maxLength={6}
              />
            </div>
            <Button 
              onClick={handleVerify}
              disabled={isLoading || verificationCode.length !== 6}
              className="w-full"
            >
              Verificar código
            </Button>
          </div>
        )}

        {step === 'complete' && backupCodes.length > 0 && (
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-yellow-800">Códigos de respaldo</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowBackupCodes(!showBackupCodes)}
                >
                  {showBackupCodes ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              <p className="text-sm text-yellow-700 mb-3">
                Guarda estos códigos en un lugar seguro. Úsalos si pierdes acceso a tu dispositivo 2FA.
              </p>
              {showBackupCodes && (
                <div className="grid grid-cols-2 gap-2 text-xs font-mono">
                  {backupCodes.map((code, index) => (
                    <div key={index} className="bg-white p-2 rounded border">
                      {code}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <Button onClick={() => setStep('setup')} variant="outline" className="w-full">
              Configurar otro método
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
