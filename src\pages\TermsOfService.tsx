import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, FileText, Shield, Users, AlertTriangle, Scale } from 'lucide-react';

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver al inicio
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Términos de Servicio</h1>
          <p className="text-gray-600 text-lg">
            Última actualización: {new Date().toLocaleDateString('es-ES')}
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 space-y-8">
          <section>
            <div className="flex items-center mb-4">
              <FileText className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">1. Aceptación de términos</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>
                Al acceder y utilizar FinanzApp, aceptas estar sujeto a estos Términos de Servicio 
                y todas las leyes y regulaciones aplicables. Si no estás de acuerdo con alguno de 
                estos términos, no debes usar nuestro servicio.
              </p>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <Users className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">2. Descripción del servicio</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>
                FinanzApp es una aplicación web de gestión financiera personal que te permite:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Registrar y categorizar ingresos y gastos</li>
                <li>Gestionar deudas y préstamos</li>
                <li>Establecer y seguir metas financieras</li>
                <li>Recibir análisis y recomendaciones personalizadas</li>
                <li>Exportar y respaldar tus datos</li>
              </ul>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">3. Responsabilidades del usuario</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>Como usuario de FinanzApp, te comprometes a:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Proporcionar información precisa y actualizada</li>
                <li>Mantener la confidencialidad de tu cuenta</li>
                <li>No usar el servicio para actividades ilegales</li>
                <li>No intentar acceder a cuentas de otros usuarios</li>
                <li>Notificar cualquier uso no autorizado de tu cuenta</li>
              </ul>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <FileText className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">4. Limitación de responsabilidad</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>
                FinanzApp es una herramienta de gestión financiera personal. No somos asesores 
                financieros y no proporcionamos asesoramiento financiero profesional. Las 
                recomendaciones y análisis son solo informativos.
              </p>
              <p>
                No nos hacemos responsables de pérdidas financieras resultantes del uso de 
                nuestra aplicación.
              </p>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <FileText className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">5. Modificaciones</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>
                Nos reservamos el derecho de modificar estos términos en cualquier momento. 
                Los cambios importantes serán notificados a los usuarios con al menos 30 días 
                de anticipación.
              </p>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <FileText className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">6. Contacto</h2>
            </div>
            <div className="text-gray-700">
              <p>
                Para preguntas sobre estos términos, contacta:
              </p>
              <p className="mt-2">
                <strong>Email:</strong> <EMAIL><br />
                <strong>Dirección:</strong> República Dominicana
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
