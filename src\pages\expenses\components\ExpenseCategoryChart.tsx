
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';
import { defaultCategories } from '@/types';

interface ExpenseCategoryChartProps {
  expensesByCategory: { [key: string]: number };
  totalExpensesMonth: number;
}

export function ExpenseCategoryChart({ expensesByCategory, totalExpensesMonth }: ExpenseCategoryChartProps) {
  return (
    <Card className="border-finanz-border">
      <CardHeader>
        <CardTitle className="text-finanz-primary">Gastos por Categoría</CardTitle>
        <CardDescription>
          Distribución del mes actual
        </CardDescription>
      </CardHeader>
      <CardContent>
        {Object.keys(expensesByCategory).length > 0 ? (
          <div className="space-y-3">
            {Object.entries(expensesByCategory)
              .sort(([,a], [,b]) => b - a)
              .map(([categoryId, amount]) => {
                const category = defaultCategories.find(cat => cat.id === categoryId);
                const percentage = (amount / totalExpensesMonth) * 100;
                
                return (
                  <div key={categoryId} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{category?.name}</span>
                      <span className="text-sm font-semibold">
                        {formatCurrency(amount)}
                      </span>
                    </div>
                    <div className="w-full bg-finanz-neutral/30 rounded-full h-2">
                      <div 
                        className="bg-finanz-danger h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <div className="text-xs text-finanz-text-secondary text-right">
                      {percentage.toFixed(1)}%
                    </div>
                  </div>
                );
              })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-finanz-text-secondary mx-auto mb-4" />
            <p className="text-finanz-text-secondary">
              Aún no tienes gastos registrados este mes
            </p>
            <p className="text-sm text-finanz-text-secondary mt-2">
              ¡Registra tu primer gasto para ver las estadísticas!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
