
import React from 'react';
import { Button } from '@/components/ui/button';
import { Income as IncomeType } from '@/types';
import { Loader2 } from 'lucide-react';

interface FormActionsProps {
  onCancel: () => void;
  editingIncome: IncomeType | null;
  isValidating?: boolean;
  hasErrors?: boolean;
  isSubmitting?: boolean;
}

export function FormActions({ 
  onCancel, 
  editingIncome, 
  isValidating = false, 
  hasErrors = false,
  isSubmitting = false
}: FormActionsProps) {
  return (
    <div className="flex justify-end space-x-3 pt-6 border-t">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        disabled={isSubmitting || isValidating}
      >
        Cancelar
      </Button>
      <Button
        type="submit"
        disabled={isSubmitting || isValidating || hasErrors}
        className="bg-finanz-primary hover:bg-finanz-primary/90 min-w-[150px]" // Added min-width for consistent size
      >
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Guardando...
          </>
        ) : isValidating ? (
          'Validando...'
        ) : editingIncome ? (
          'Actualizar Ingreso'
        ) : (
          'Guardar Ingreso'
        )}
      </Button>
    </div>
  );
}
