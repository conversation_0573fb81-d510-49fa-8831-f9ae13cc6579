
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  CheckCircle,
  TrendingUp,
  BarChart3,
  Star,
  ArrowRight,
  Shield
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export function BenefitsSection() {
  const { isMobile, isTablet } = useBreakpoint();
  
  const benefits = [
    {
      text: "Sincronización automática de datos",
      icon: BarChart3
    },
    {
      text: "Exportación a PDF y Excel",
      icon: ArrowRight
    },
    {
      text: "Notificaciones de pagos pendientes",
      icon: CheckCircle
    },
    {
      text: "Análisis financiero detallado",
      icon: Star
    },
    {
      text: "Modo offline disponible",
      icon: Shield
    },
    {
      text: "Seguridad de datos garantizada",
      icon: Shield
    }
  ];

  return (
    <section className={`
      bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 relative overflow-hidden
      ${isMobile ? 'py-12' : isTablet ? 'py-14' : 'py-16'}
    `}>
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 -right-32 w-64 h-64 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 -left-32 w-64 h-64 bg-gradient-to-br from-emerald-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
      </div>
      
      <div className={`
        container mx-auto relative z-10
        ${isMobile ? 'px-4' : 'px-4'}
      `}>
        <div className={`
          grid items-center gap-8
          ${isMobile ? 'grid-cols-1 gap-8' : 'grid-cols-1 lg:grid-cols-2 gap-12'}
        `}>
          <div>
            <div className={`
              inline-flex items-center space-x-2 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-full border border-emerald-200/50 mb-3
              ${isMobile ? 'px-3 py-1 mb-2' : 'px-5 py-2 mb-4'}
            `}>
              <Star className="w-4 h-4 text-emerald-600" />
              <span className={`
                font-semibold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent
                ${isMobile ? 'text-xs' : 'text-sm'}
              `}>
                Beneficios
              </span>
            </div>
            <h2 className={`
              font-bold text-gray-900 mb-4
              ${isMobile ? 'text-2xl mb-3' : isTablet ? 'text-3xl mb-5' : 'text-3xl md:text-4xl mb-6'}
            `}>
              ¿Por qué elegir{' '}
              <span className="bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 bg-clip-text text-transparent whitespace-nowrap">
                FinanzApp?
              </span>
            </h2>
            <p className={`
              text-gray-600 leading-relaxed font-medium mb-6
              ${isMobile ? 'text-sm mb-5' : isTablet ? 'text-base mb-7' : 'text-lg mb-8'}
            `}>
              Nuestra plataforma está diseñada para simplificar la gestión financiera personal 
              con herramientas profesionales y una interfaz elegante e intuitiva.
            </p>
            <div className={`
              space-y-2
              ${isMobile ? 'space-y-2' : 'space-y-3'}
            `}>
              {benefits.map((benefit, index) => (
                <div key={index} className={`
                  flex items-center space-x-3 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/50 hover:shadow-md transition-all duration-200
                  ${isMobile ? 'p-2' : 'p-3'}
                `}>
                  <div className={`
                    bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm
                    ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}
                  `}>
                    <benefit.icon className={`
                      text-white
                      ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}
                    `} />
                  </div>
                  <span className={`
                    text-gray-700 font-medium
                    ${isMobile ? 'text-xs' : 'text-sm'}
                  `}>
                    {benefit.text}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className={`
              bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 rounded-2xl text-white shadow-2xl relative overflow-hidden
              ${isMobile ? 'p-6' : 'p-8'}
            `}>
              {/* Background pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl"></div>
              <div className={`
                absolute bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg
                ${isMobile ? '-top-2 -right-2 w-12 h-12' : '-top-3 -right-3 w-16 h-16'}
              `}>
                <TrendingUp className={`
                  text-white
                  ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}
                `} />
              </div>
              <div className="relative z-10">
                <h3 className={`
                  font-bold mb-3
                  ${isMobile ? 'text-xl mb-3' : 'text-2xl mb-4'}
                `}>
                  Comienza Gratis
                </h3>
                <p className={`
                  text-white/90 leading-relaxed mb-5
                  ${isMobile ? 'text-sm mb-4' : 'text-base mb-6'}
                `}>
                  Accede a todas las funcionalidades básicas sin costo. 
                  Disfruta de una experiencia premium desde el primer día.
                </p>
                <Link to="/auth">
                  <Button 
                    variant="secondary" 
                    className={`
                      w-full bg-white text-blue-700 hover:bg-gray-100 font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200
                      ${isMobile ? 'py-2 text-sm' : 'py-3 text-base'}
                    `}
                  >
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Crear Cuenta Gratis
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
