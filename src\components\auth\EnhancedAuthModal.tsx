
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Smartphone, Monitor } from 'lucide-react';
import { TwoFactorAuth } from './TwoFactorAuth';
import { ActiveSessions } from './ActiveSessions';
import { ConnectivityIndicator } from './ConnectivityIndicator';
import { useBiometricAuth } from '@/hooks/useBiometricAuth';
import { toast } from 'sonner';

interface EnhancedAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTab?: string;
}

export const EnhancedAuthModal: React.FC<EnhancedAuthModalProps> = ({
  isOpen,
  onClose,
  initialTab = 'security'
}) => {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    isSupported: isBiometricSupported,
    isLoading: isBiometricLoading,
    registerBiometric,
    authenticateWithBiometric
  } = useBiometricAuth();

  const handleEnable2FA = async (method: '2fa' | 'sms') => {
    setIsLoading(true);
    try {
      // Simular configuración de 2FA
      await new Promise(resolve => setTimeout(resolve, 2000));
      setIs2FAEnabled(true);
      toast.success(`2FA por ${method === '2fa' ? 'aplicación' : 'SMS'} configurado exitosamente`);
    } catch {
      toast.error('Error al configurar 2FA');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerify2FA = async (code: string): Promise<boolean> => {
    try {
      // Simular verificación
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Código de prueba: 123456
      if (code === '123456') {
        return true;
      }
      
      return false;
    } catch {
      return false;
    }
  };

  const handleTerminateSession = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Sesión cerrada exitosamente');
    } catch {
      toast.error('Error al cerrar sesión');
    }
  };

  const handleTerminateAllSessions = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Todas las sesiones remotas han sido cerradas');
    } catch {
      toast.error('Error al cerrar sesiones');
    }
  };

  const handleSetupBiometric = async () => {
    const success = await registerBiometric('<EMAIL>');
    if (success) {
      toast.success('Autenticación biométrica configurada');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Centro de Seguridad
              </DialogTitle>
              <DialogDescription>
                Gestiona tu seguridad y configuraciones avanzadas
              </DialogDescription>
            </div>
            <ConnectivityIndicator />
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Seguridad
            </TabsTrigger>
            <TabsTrigger value="sessions" className="flex items-center gap-2">
              <Monitor className="w-4 h-4" />
              Sesiones
            </TabsTrigger>
            <TabsTrigger value="biometric" className="flex items-center gap-2">
              <Smartphone className="w-4 h-4" />
              Biométrica
            </TabsTrigger>
          </TabsList>

          <TabsContent value="security" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-1">
              <TwoFactorAuth
                onEnable={handleEnable2FA}
                onVerify={handleVerify2FA}
                isEnabled={is2FAEnabled}
                isLoading={isLoading}
              />
            </div>
          </TabsContent>

          <TabsContent value="sessions" className="space-y-6">
            <ActiveSessions
              onTerminateSession={handleTerminateSession}
              onTerminateAllSessions={handleTerminateAllSessions}
            />
          </TabsContent>

          <TabsContent value="biometric" className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Smartphone className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Autenticación Biométrica</h3>
                    <p className="text-gray-600">
                      Usa tu huella dactilar o reconocimiento facial para acceder
                    </p>
                  </div>
                  
                  {!isBiometricSupported ? (
                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        Tu dispositivo o navegador no soporta autenticación biométrica
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Button
                        onClick={handleSetupBiometric}
                        disabled={isBiometricLoading}
                        className="w-full"
                      >
                        {isBiometricLoading ? 'Configurando...' : 'Configurar Biométrica'}
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={authenticateWithBiometric}
                        disabled={isBiometricLoading}
                        className="w-full"
                      >
                        Probar Autenticación
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
