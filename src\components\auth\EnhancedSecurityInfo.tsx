
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Lock, Eye, AlertTriangle } from 'lucide-react';

export const EnhancedSecurityInfo: React.FC = () => {
  return (
    <Card className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 border-blue-200 dark:border-blue-700">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 text-sm">
              Seguridad Bancaria
            </h3>
            <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
              <div className="flex items-center space-x-2">
                <Lock className="w-3 h-3" />
                <span>Encriptación AES-256 para todos los datos</span>
              </div>
              <div className="flex items-center space-x-2">
                <Eye className="w-3 h-3" />
                <span>Autenticación de dos factores disponible</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-3 h-3" />
                <span>Protección contra ataques de fuerza bruta</span>
              </div>
            </div>
            <div className="text-xs text-blue-700 dark:text-blue-300 mt-2 p-2 bg-blue-100 dark:bg-blue-800/30 rounded">
              <strong>Contraseña segura:</strong> Mínimo 8 caracteres con mayúsculas, minúsculas, números y símbolos
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
