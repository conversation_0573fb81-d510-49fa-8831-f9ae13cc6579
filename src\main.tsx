import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import { CurrencyProvider } from './contexts/CurrencyContext';
import "./index.css";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ErrorBoundary from './components/ErrorBoundary';
import { logger } from '@/utils/logger';

// Función para aplicar tema en páginas internas de la aplicación
export const applyAppTheme = () => {
  try {
    const savedAppearance = localStorage.getItem('finanz_appearance_settings');
    const root = document.documentElement;
    
    if (savedAppearance) {
      const { theme } = JSON.parse(savedAppearance);
      root.classList.toggle('dark', theme === 'dark');
    } else {
      // Para nuevos usuarios, usar tema claro como predeterminado
      root.classList.remove('dark');
    }
  } catch (error) {
    console.error('Error applying app theme:', error);
    document.documentElement.classList.remove('dark'); // Fallback to light
  }
};

// Se elimina la precarga y la actualización forzada del favicon para evitar
// warnings de preload innecesario y mantener un manejo simple y estándar.

// Inicializar tema antes de renderizar la app
const initializeTheme = () => {
  const currentPath = window.location.pathname;
  logger.debug('Current path:', currentPath);
  
  const publicPaths = ['/', '/auth', '/features']; // Rutas públicas que deben estar en modo claro
  
  if (publicPaths.includes(currentPath)) {
    // Para páginas públicas, forzar modo claro
    logger.debug('Setting light mode for public page');
    document.documentElement.classList.remove('dark');
  } else {
    // Para páginas internas, aplicar tema guardado o claro como predeterminado
    logger.debug('Applying saved theme for internal page');
    applyAppTheme();
  }
};

// Inicializar aplicación
const initializeApp = () => {
  initializeTheme();
};

// Ejecutar inicialización
initializeApp();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes - increased for better caching
      retry: (failureCount, error) => {
        // Don't retry on certain errors
        if (error && typeof error === 'object' && 'code' in error) {
          // Don't retry on authentication errors
          if (error.code === 'PGRST301' || error.code === 'PGRST116') {
            return false;
          }
        }
        return failureCount < 2; // Reduced retries for faster response
      },
      refetchOnWindowFocus: false, // Disable to prevent unnecessary refetches
      refetchOnMount: false, // Use cache first
    },
    mutations: {
      retry: 1, // Quick retry for mutations
    },
  },
});

logger.debug('Starting React app render');

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <CurrencyProvider>
          <App />
        </CurrencyProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  </React.StrictMode>,
);
