-- A<PERSON><PERSON> columnas de auditoría y triggers a tablas faltantes
-- Fecha de creación: 2025-07-04
-- Descripción: Garantiza que las tablas expenses, credit_cards, loans, personal_debts y subscriptions
-- cuenten con los campos created_at y updated_at, con valores por defecto y triggers para mantener
-- updated_at en cada actualización.

-- Asegurar que la función utilitaria exista (la mayoría de las instalaciones base ya la incluyen, pero
-- se define aquí si no está disponible)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column'
  ) THEN
    CREATE OR REPLACE FUNCTION public.update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  END IF;
END $$;

-- Tabla: expenses -------------------------------------------------------------
ALTER TABLE public.expenses
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

DROP TRIGGER IF EXISTS update_expenses_updated_at ON public.expenses;
CREATE TRIGGER update_expenses_updated_at
  BEFORE UPDATE ON public.expenses
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Tabla: credit_cards ---------------------------------------------------------
ALTER TABLE public.credit_cards
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

DROP TRIGGER IF EXISTS update_credit_cards_updated_at ON public.credit_cards;
CREATE TRIGGER update_credit_cards_updated_at
  BEFORE UPDATE ON public.credit_cards
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Tabla: loans ----------------------------------------------------------------
ALTER TABLE public.loans
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

DROP TRIGGER IF EXISTS update_loans_updated_at ON public.loans;
CREATE TRIGGER update_loans_updated_at
  BEFORE UPDATE ON public.loans
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Tabla: personal_debts -------------------------------------------------------
ALTER TABLE public.personal_debts
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

DROP TRIGGER IF EXISTS update_personal_debts_updated_at ON public.personal_debts;
CREATE TRIGGER update_personal_debts_updated_at
  BEFORE UPDATE ON public.personal_debts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Tabla: subscriptions --------------------------------------------------------
ALTER TABLE public.subscriptions
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

DROP TRIGGER IF EXISTS update_subscriptions_updated_at ON public.subscriptions;
CREATE TRIGGER update_subscriptions_updated_at
  BEFORE UPDATE ON public.subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Índices para optimizar consultas por usuario y fecha ------------------------
CREATE INDEX IF NOT EXISTS idx_expenses_user_created ON public.expenses(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_credit_cards_user_created ON public.credit_cards(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_loans_user_created ON public.loans(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_personal_debts_user_created ON public.personal_debts(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_created ON public.subscriptions(user_id, created_at); 