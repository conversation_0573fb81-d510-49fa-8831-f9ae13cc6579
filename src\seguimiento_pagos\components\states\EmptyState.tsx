import React from 'react';

interface EmptyStateProps {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  subtitle: string;
}

export function EmptyState({ icon: Icon, title, subtitle }: EmptyStateProps) {
  return (
    <div className="text-center py-8">
      <Icon className="w-12 h-12 text-finanz-success mx-auto mb-3" />
      <p className="text-finanz-text-secondary">{title}</p>
      <p className="text-sm text-finanz-text-secondary">{subtitle}</p>
    </div>
  );
}
