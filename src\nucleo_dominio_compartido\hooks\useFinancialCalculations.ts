import { useMemo } from 'react';
import { Income, Expense, CreditCard, Loan, PersonalDebt, Reimbursement } from '@/types';
import { useExchangeRate } from '@/hooks/useExchangeRate';

export interface CalculationFilters {
  month?: string;
  startDate?: string;
  endDate?: string;
  currentMonth?: boolean;
}

export const useFinancialCalculations = (
  incomes: Income[],
  expenses: Expense[],
  creditCards: CreditCard[],
  loans: Loan[],
  personalDebts: PersonalDebt[],
  reimbursements: Reimbursement[],
  filters: CalculationFilters = {}
) => {
  const { rate: exchangeRate } = useExchangeRate();

  return useMemo(() => {
    const { month, startDate, endDate, currentMonth } = filters;

    const monthFilter = month || (currentMonth ? new Date().toISOString().slice(0, 7) : undefined);

    const withinMonth = (m: string) => {
      return monthFilter ? m.startsWith(monthFilter) : true;
    };

    const withinRange = (d: string) => {
      if (startDate && new Date(d) < new Date(startDate)) return false;
      if (endDate && new Date(d) > new Date(endDate)) return false;
      return true;
    };

    const getAmountInDOP = (amount: number, currency: 'DOP' | 'USD' | undefined) => {
      if (currency === 'USD') {
        return amount * exchangeRate;
      }
      return amount;
    };

    const filteredIncomes = incomes.filter((i) => withinMonth(i.month) && withinRange(`${i.month}-01`));
    const filteredExpenses = expenses.filter((e) => withinMonth(e.month) && withinRange(e.date));
    const filteredReimbursements = reimbursements.filter((r) => withinMonth(r.date.slice(0, 7)) && withinRange(r.date));

    const calculateNetIncome = (data: Income[]) => data.reduce((total, income) => total + getAmountInDOP(income.netIncome || 0, income.currency), 0);

    const calculateTotalExpenses = (data: Expense[]) =>
      data.reduce((total, expense) => {
        const amount = getAmountInDOP(expense.amount, expense.currency);
        return total + (amount || 0);
      }, 0);
    // Ingreso neto total usando filtros
    const getNetIncome = () => {
      return calculateNetIncome(filteredIncomes);
    };

    // Ingreso neto para un mes específico (YYYY-MM)
    const getNetIncomeFor = (m: string) => {
      const data = incomes.filter((i) => i.month.startsWith(m));
      return calculateNetIncome(data);
    };

    // Gastos totales usando filtros
    const getTotalExpenses = () => {
      return calculateTotalExpenses(filteredExpenses);
    };

    // Gastos totales para un mes específico
    const getTotalExpensesFor = (m: string) => {
      const data = expenses.filter((e) => e.month.startsWith(m));
      return calculateTotalExpenses(data);
    };

    // Balance neto (ingreso - gastos) usando filtros
    const getNetBalance = () => {
      return getNetIncome() - getTotalExpenses();
    };

    const getNetBalanceFor = (m: string) => {
      return getNetIncomeFor(m) - getTotalExpensesFor(m);
    };

    // Deuda total
    const getTotalDebt = () => {
      const creditCardDebt = creditCards
        .filter(card => card.isActive)
        .reduce((total, card) => {
          const amount = getAmountInDOP(card.currentBalance, card.currency);
          return total + (amount || 0);
        }, 0);

      const loanDebt = loans
        .filter(loan => loan.isActive)
        .reduce((total, loan) => {
          const amount = getAmountInDOP(loan.totalAmount, loan.currency);
          return total + (amount || 0);
        }, 0);

      const personalDebtAmount = personalDebts
        .filter(debt => debt.isActive)
        .reduce((total, debt) => {
          // Asumiendo que 'remainingBalance' es el campo principal, si no, 'amount'
          const balance = debt.remainingBalance ?? debt.amount;
          const amount = getAmountInDOP(balance, debt.currency);
          return total + (amount || 0);
        }, 0);

      return creditCardDebt + loanDebt + personalDebtAmount;
    };

    // Reembolsos pendientes
    const getPendingReimbursements = () => {
      return filteredReimbursements
        .filter(reimbursement => reimbursement.status === 'Pendiente')
        .reduce((total, reimbursement) => {
          const amount = getAmountInDOP(reimbursement.amount, reimbursement.currency);
          return total + (amount || 0);
        }, 0);
    };

    // Tasa de ahorro
    const getSavingsRate = () => {
      const netIncome = getNetIncome();
      const netBalance = getNetBalance();
      return netIncome > 0 ? Math.max(0, (netBalance / netIncome) * 100) : 0;
    };

    // Ratio deuda/ingreso
    const getDebtToIncomeRatio = () => {
      const netIncome = getNetIncome();
      const totalDebt = getTotalDebt();
      return netIncome > 0 ? (totalDebt / netIncome) * 100 : 0;
    };

    // Meses de fondo de emergencia
    const getEmergencyFundMonths = () => {
      const totalExpenses = getTotalExpenses();
      const netBalance = getNetBalance();
      return totalExpenses > 0 ? Math.max(0, netBalance / totalExpenses) : 0;
    };

    // Pagos vencidos (simulado - necesitaría datos de pagos reales)
    const getOverduePayments = () => {
      // Por ahora retornamos 0, esta función necesitaría datos de payment_records
      // para calcular pagos realmente vencidos
      return 0;
    };

    return {
      getNetIncome,
      getTotalExpenses,
      getNetBalance,
      getTotalDebt,
      getPendingReimbursements,
      getSavingsRate,
      getDebtToIncomeRatio,
      getEmergencyFundMonths,
      getOverduePayments,
      // Métodos con filtro de mes específico
      getNetIncomeFor,
      getTotalExpensesFor,
      getNetBalanceFor
    };
  }, [
    incomes,
    expenses,
    creditCards,
    loans,
    personalDebts,
    reimbursements,
    filters,
    exchangeRate // CRÍTICO: añadir tasa como dependencia
  ]);
};
