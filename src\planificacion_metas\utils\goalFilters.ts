
import { FinancialGoal } from '@/types';

export const getGoalProgress = (goal: FinancialGoal) => {
  return (goal.currentAmount / goal.amount) * 100;
};

export const filterGoals = (goals: FinancialGoal[], filter: string) => {
  if (filter === 'all') return goals;
  
  return goals.filter(goal => {
    const progress = getGoalProgress(goal);
    
    switch (filter) {
      case 'completed':
        return progress >= 100;
      case 'in-progress':
        return progress > 0 && progress < 100;
      case 'not-started':
        return progress === 0;
      case 'high-priority':
        return goal.priority === 'high';
      default:
        return true;
    }
  });
};

export const getFilteredGoalCounts = (goals: FinancialGoal[]) => {
  const completedGoals = goals.filter(goal => getGoalProgress(goal) >= 100);
  const inProgressGoals = goals.filter(goal => {
    const progress = getGoalProgress(goal);
    return progress > 0 && progress < 100;
  });
  const notStartedGoals = goals.filter(goal => getGoalProgress(goal) === 0);
  const highPriorityGoals = goals.filter(goal => goal.priority === 'high');

  return {
    total: goals.length,
    completed: completedGoals.length,
    inProgress: inProgressGoals.length,
    notStarted: notStartedGoals.length,
    highPriority: highPriorityGoals.length
  };
};
