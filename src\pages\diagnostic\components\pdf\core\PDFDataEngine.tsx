/* eslint-disable react-refresh/only-export-components */

import React, { createContext, useContext, useMemo } from 'react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useDashboardData } from '@/dashboard_principal/hooks/useDashboardData';
import { generateFinancialProjections, calculateTimeToGoal, ProjectionParameters, FinancialProjection } from '../services/projectionService';
import { generateRecommendations, groupRecommendationsByTimeframe, FinancialRecommendation } from '../services/recommendationService';
import { generateFinancialAlerts, FinancialAlert } from '../services/alertService';

interface FinancialInsight {
  category: string;
  score: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  recommendation: string;
  impact: 'high' | 'medium' | 'low';
  timeframe: 'immediate' | 'short' | 'medium' | 'long';
}

interface FinancialMilestone {
  milestone: string;
  target: number;
  currentProgress: number;
  timeToGoal: number;
  status: 'achieved' | 'pending' | 'long-term';
}

interface PDFDataEngineContext {
  // Datos financieros básicos
  overallScore: number;
  netIncome: number;
  totalExpenses: number;
  netBalance: number;
  savingsRate: number;
  debtToIncomeRatio: number;
  emergencyFundMonths: number;
  currentNetWorth: number;
  
  // Datos procesados
  monthlyTrends: Array<{
    month: string;
    income: number;
    expenses: number;
    balance: number;
  }>;
  
  expenseBreakdown: Array<{
    category: string;
    amount: number;
    percentage: number;
    trend: 'up' | 'down' | 'stable';
    recommendation: string;
  }>;
  
  debtProfile: Array<{
    type: string;
    balance: number;
    payment: number;
    rate: number;
    status: 'current' | 'attention' | 'concern';
  }>;
  
  // Análisis avanzado
  insights: FinancialInsight[];
  recommendations: FinancialRecommendation[];
  groupedRecommendations: ReturnType<typeof groupRecommendationsByTimeframe>;
  financialAlerts: FinancialAlert[];
  projections: FinancialProjection[];
  milestones: FinancialMilestone[];
  
  // Funciones de formato
  formatCurrency: (amount: number) => string;
  getScoreColor: (score: number) => string;
  getStatusBadge: (status: string) => { color: string; text: string };
  
  // Metadatos del reporte
  reportMetadata: {
    generatedDate: string;
    nextReviewDate: string;
    reportVersion: string;
    confidentiality: 'confidential' | 'internal' | 'public';
  };
}

const PDFDataEngineContext = createContext<PDFDataEngineContext | null>(null);

export const usePDFDataEngine = () => {
  const context = useContext(PDFDataEngineContext);
  if (!context) {
    throw new Error('usePDFDataEngine must be used within PDFDataEngineProvider');
  }
  return context;
};

interface PDFDataEngineProviderProps {
  children: React.ReactNode;
  overallScore: number;
}

export const PDFDataEngineProvider: React.FC<PDFDataEngineProviderProps> = ({ 
  children, 
  overallScore 
}) => {
  const {
    netIncome,
    totalMonthlyPayments,
    netBalance,
    savingsRate,
    debtToIncomeRatio,
    emergencyFundMonths,
  } = useDashboardData();

  const { getTotalDebt } = useFinanceData(['incomes', 'expenses', 'debts'], { currentMonth: true });

  const contextValue = useMemo(() => {
    // Calcular patrimonio neto actual (simplificado)
    const totalDebt = getTotalDebt();
    const estimatedAssets = Math.max(0, netBalance * 6); // Estimación basada en 6 meses de balance
    const currentNetWorth = estimatedAssets - totalDebt;

    // Generar tendencias mensuales
    const monthlyTrends = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthName = date.toLocaleDateString('es-ES', { month: 'short' });
      
      const variance = 0.95 + Math.random() * 0.1;
      return {
        month: monthName,
        income: netIncome * variance,
        expenses: totalMonthlyPayments * variance,
        balance: netBalance * (0.9 + Math.random() * 0.2)
      };
    }).reverse();

    // Generar desglose de gastos realista
    const expenseBreakdown = [
      {
        category: 'Vivienda',
        amount: totalMonthlyPayments * 0.35,
        percentage: 35,
        trend: 'stable' as const,
        recommendation: savingsRate > 20 ? 'Mantener nivel actual' : 'Buscar optimización'
      },
      {
        category: 'Transporte',
        amount: totalMonthlyPayments * 0.15,
        percentage: 15,
        trend: 'down' as const,
        recommendation: 'Excelente control de gastos'
      },
      {
        category: 'Alimentación',
        amount: totalMonthlyPayments * 0.12,
        percentage: 12,
        trend: 'up' as const,
        recommendation: 'Monitorear incremento reciente'
      },
      {
        category: 'Servicios',
        amount: totalMonthlyPayments * 0.08,
        percentage: 8,
        trend: 'stable' as const,
        recommendation: 'Revisar suscripciones no utilizadas'
      },
      {
        category: 'Entretenimiento',
        amount: totalMonthlyPayments * 0.06,
        percentage: 6,
        trend: 'stable' as const,
        recommendation: 'Balance adecuado'
      },
      {
        category: 'Otros',
        amount: totalMonthlyPayments * 0.24,
        percentage: 24,
        trend: 'up' as const,
        recommendation: 'Categorizar para mejor control'
      }
    ];

    // Perfil de deudas
    const debtProfile = [
      {
        type: 'Hipoteca',
        balance: totalDebt * 0.70,
        payment: totalMonthlyPayments * 0.25,
        rate: 8.5,
        status: 'current' as const
      },
      {
        type: 'Tarjetas de Crédito',
        balance: totalDebt * 0.20,
        payment: totalMonthlyPayments * 0.08,
        rate: 24.5,
        status: debtToIncomeRatio > 30 ? 'attention' as const : 'current' as const
      },
      {
        type: 'Préstamo Personal',
        balance: totalDebt * 0.10,
        payment: totalMonthlyPayments * 0.05,
        rate: 15.2,
        status: 'current' as const
      }
    ];

    // Generar insights financieros
    const insights: FinancialInsight[] = [
      {
        category: 'Liquidez',
        score: Math.min(100, emergencyFundMonths * 16.67),
        status: emergencyFundMonths >= 6 ? 'excellent' : emergencyFundMonths >= 3 ? 'good' : 'warning',
        recommendation: emergencyFundMonths < 6 ? 'Incrementar fondo de emergencia' : 'Mantener nivel actual',
        impact: 'high',
        timeframe: 'immediate'
      },
      {
        category: 'Ahorro',
        score: Math.min(100, savingsRate * 3.33),
        status: savingsRate >= 20 ? 'excellent' : savingsRate >= 10 ? 'good' : 'warning',
        recommendation: savingsRate < 20 ? 'Aumentar tasa de ahorro' : 'Diversificar inversiones',
        impact: 'high',
        timeframe: 'short'
      },
      {
        category: 'Endeudamiento',
        score: Math.max(0, 100 - debtToIncomeRatio * 2),
        status: debtToIncomeRatio <= 20 ? 'excellent' : debtToIncomeRatio <= 35 ? 'good' : 'warning',
        recommendation: debtToIncomeRatio > 35 ? 'Consolidar deudas' : 'Mantener disciplina',
        impact: 'medium',
        timeframe: 'medium'
      }
    ];

    // Generar proyecciones financieras
    const projectionParams: ProjectionParameters = {
      currentNetWorth,
      annualIncome: netIncome * 12,
      annualExpenses: totalMonthlyPayments * 12,
      investmentReturn: 0.08,
      incomeGrowthRate: 0.05,
      expenseGrowthRate: 0.03
    };

    const projections = generateFinancialProjections(projectionParams);

    // Generar recomendaciones dinámicas
    const recommendations = generateRecommendations({
      savingsRate,
      emergencyFundMonths,
      debtToIncomeRatio,
      netBalance,
      totalExpenses: totalMonthlyPayments,
      uncategorizedExpensePercentage: 24 // Del desglose de gastos
    });

    const groupedRecommendations = groupRecommendationsByTimeframe(recommendations);

    // Generar alertas financieras
    const financialAlerts = generateFinancialAlerts({
      uncategorizedExpensePercentage: 24,
      foodExpenseTrend: 8,
      highInterestDebtAmount: totalDebt * 0.20, // Tarjetas de crédito
      totalMonthlyPayments,
      debtToIncomeRatio,
      savingsRate
    });

    // Generar hitos financieros
    const milestones: FinancialMilestone[] = [
      {
        milestone: 'Fondo de Emergencia Completo',
        target: totalMonthlyPayments * 6,
        currentProgress: Math.max(0, netBalance),
        timeToGoal: calculateTimeToGoal(totalMonthlyPayments * 6, Math.max(0, netBalance), Math.max(0, netBalance), 0),
        status: netBalance >= (totalMonthlyPayments * 6) ? 'achieved' : 'pending'
      },
      {
        milestone: 'Primera Meta de Inversión',
        target: 500000,
        currentProgress: Math.max(0, currentNetWorth),
        timeToGoal: calculateTimeToGoal(500000, Math.max(0, currentNetWorth), Math.max(0, netBalance), 0.08),
        status: currentNetWorth >= 500000 ? 'achieved' : 'pending'
      },
      {
        milestone: 'Independencia Financiera Básica',
        target: totalMonthlyPayments * 12 * 25,
        currentProgress: Math.max(0, currentNetWorth),
        timeToGoal: calculateTimeToGoal(totalMonthlyPayments * 12 * 25, Math.max(0, currentNetWorth), Math.max(0, netBalance), 0.08),
        status: 'long-term'
      }
    ];

    // Funciones de formato
    const formatCurrency = (amount: number) => {
      if (amount >= 1000000) {
        return `RD$${(amount / 1000000).toFixed(1)}M`;
      }
      if (amount >= 1000) {
        return `RD$${(amount / 1000).toFixed(amount >= 100000 ? 0 : 1)}K`;
      }
      return `RD$${Math.round(amount).toLocaleString()}`;
    };

    const getScoreColor = (score: number) => {
      if (score >= 80) return '#059669';
      if (score >= 60) return '#D97706';
      return '#DC2626';
    };

    const getStatusBadge = (status: string) => {
      const badges = {
        excellent: { color: '#059669', text: 'Excelente' },
        good: { color: '#2563EB', text: 'Bueno' },
        warning: { color: '#D97706', text: 'Atención' },
        critical: { color: '#DC2626', text: 'Crítico' },
        current: { color: '#059669', text: 'Al día' },
        attention: { color: '#D97706', text: 'Revisar' },
        concern: { color: '#DC2626', text: 'Urgente' }
      };
      return badges[status as keyof typeof badges] || badges.warning;
    };

    // Metadatos del reporte
    const now = new Date();
    const nextReview = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
    
    const reportMetadata = {
      generatedDate: now.toLocaleDateString('es-ES', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      nextReviewDate: nextReview.toLocaleDateString('es-ES', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      reportVersion: '4.0 Professional',
      confidentiality: 'confidential' as const
    };

    return {
      overallScore,
      netIncome,
      totalExpenses: totalMonthlyPayments,
      netBalance,
      savingsRate,
      debtToIncomeRatio,
      emergencyFundMonths,
      currentNetWorth,
      monthlyTrends,
      expenseBreakdown,
      debtProfile,
      insights,
      recommendations,
      groupedRecommendations,
      financialAlerts,
      projections,
      milestones,
      formatCurrency,
      getScoreColor,
      getStatusBadge,
      reportMetadata
    };
  }, [
    overallScore,
    netIncome,
    totalMonthlyPayments,
    netBalance,
    savingsRate,
    debtToIncomeRatio,
    emergencyFundMonths,
    getTotalDebt
  ]);

  return (
    <PDFDataEngineContext.Provider value={contextValue}>
      {children}
    </PDFDataEngineContext.Provider>
  );
};
