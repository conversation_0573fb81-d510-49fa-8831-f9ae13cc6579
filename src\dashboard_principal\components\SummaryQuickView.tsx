
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, DollarSign, Target, AlertCircle } from 'lucide-react';
import { useDashboardData } from '../hooks/useDashboardData';
import { useBreakpoint } from '@/hooks/useBreakpoint';

const SummaryQuickView: React.FC = () => {
  const { 
    netBalance, 
    savingsRate, 
    debtToIncomeRatio,
    paymentToIncomeRatio,
    totalMonthlyPayments
  } = useDashboardData();
  
  const { isMobile, isTablet } = useBreakpoint();

  const formatCurrency = (amount: number) => {
    return `RD$${amount.toLocaleString()}`;
  };

  const getHealthColor = (score: number, thresholds: { good: number; warning: number }) => {
    if (score >= thresholds.good) return 'text-green-600 bg-green-50';
    if (score >= thresholds.warning) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  return (
    <Card className={`${isMobile ? 'shadow-sm' : ''}`}>
      <CardHeader className={`${isMobile ? 'pb-2 px-3 pt-3' : ''}`}>
        <CardTitle className={`flex items-center gap-2 ${isMobile ? 'text-base' : ''}`}>
          <Target className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
          {isMobile ? 'Vista Rápida' : 'Vista Rápida Financiera'}
        </CardTitle>
        <CardDescription className={`${isMobile ? 'text-xs' : ''}`}>
          {isMobile ? 'Ingresos vs Pagos' : 'Comparación real: Ingresos vs Pagos del Mes'}
        </CardDescription>
      </CardHeader>
      <CardContent className={`${isMobile ? 'px-3 pb-3' : ''}`}>
        <div className={`grid gap-3 md:gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-3'}`}>
          {/* Balance Real Disponible */}
          <div className={`p-3 md:p-4 border rounded-lg ${isMobile ? 'border-l-4 border-l-blue-500' : ''}`}>
            <div className="flex items-center justify-between mb-2">
              <span className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                {isMobile ? 'Balance Disponible' : 'Balance Real Disponible'}
              </span>
              {netBalance >= 0 ? (
                <TrendingUp className={`text-green-500 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
              ) : (
                <TrendingDown className={`text-red-500 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
              )}
            </div>
            <p className={`font-bold mb-2 ${netBalance >= 0 ? 'text-green-600' : 'text-red-600'} ${isMobile ? 'text-base' : 'text-xl'}`}>
              {formatCurrency(netBalance)}
            </p>
            <Badge variant={netBalance >= 0 ? 'default' : 'destructive'} className={`${isMobile ? 'text-xs px-2 py-0.5' : 'text-xs'}`}>
              {netBalance >= 0 ? 'Disponible' : 'Déficit'}
            </Badge>
          </div>

          {/* Tasa de Ahorro Real */}
          <div className={`p-3 md:p-4 border rounded-lg ${isMobile ? 'border-l-4 border-l-green-500' : ''}`}>
            <div className="flex items-center justify-between mb-2">
              <span className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                {isMobile ? 'Tasa de Ahorro' : 'Tasa de Ahorro Real'}
              </span>
              <DollarSign className={`text-blue-500 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
            </div>
            <p className={`font-bold text-blue-600 mb-2 ${isMobile ? 'text-base' : 'text-xl'}`}>
              {savingsRate.toFixed(1)}%
            </p>
            <Badge className={`${getHealthColor(savingsRate, { good: 20, warning: 10 })} ${isMobile ? 'text-xs px-2 py-0.5' : 'text-xs'}`}>
              {savingsRate >= 20 ? 'Excelente' : savingsRate >= 10 ? 'Bueno' : 'Mejorar'}
            </Badge>
          </div>

          {/* Ratio Pagos/Ingreso */}
          <div className={`p-3 md:p-4 border rounded-lg ${isMobile ?  'border-l-4 border-l-orange-500' : ''}`}>
            <div className="flex items-center justify-between mb-2">
              <span className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Pagos/Ingreso
              </span>
              <AlertCircle className={`text-orange-500 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
            </div>
            <p className={`font-bold text-orange-600 mb-2 ${isMobile ? 'text-base' : 'text-xl'}`}>
              {paymentToIncomeRatio.toFixed(1)}%
            </p>
            <Badge className={`${getHealthColor(100 - paymentToIncomeRatio, { good: 70, warning: 50 })} ${isMobile ? 'text-xs px-2 py-0.5' : 'text-xs'}`}>
              {paymentToIncomeRatio <= 30 ? 'Saludable' : paymentToIncomeRatio <= 50 ? 'Moderado' : 'Alto'}
            </Badge>
          </div>
        </div>

        {/* Resumen de estado financiero real */}
        <div className={`mt-4 md:mt-6 p-3 md:p-4 bg-gray-50 rounded-lg`}>
          <h4 className={`font-medium mb-2 ${isMobile ? 'text-sm' : ''}`}>
            {isMobile ? 'Estado Financiero' : 'Estado Financiero Real'}
          </h4>
          <div className="flex items-center gap-2">
            {netBalance >= 0 && savingsRate >= 15 && paymentToIncomeRatio <= 40 ? (
              <>
                <Badge className={`bg-green-100 text-green-800 ${isMobile ? 'text-xs px-2 py-0.5' : ''}`}>Excelente</Badge>
                <span className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'Pagos controlados y capacidad de ahorro' : 'Tus pagos están bajo control y tienes capacidad de ahorro'}
                </span>
              </>
            ) : netBalance >= 0 && savingsRate >= 10 ? (
              <>
                <Badge className={`bg-yellow-100 text-yellow-800 ${isMobile ? 'text-xs px-2 py-0.5' : ''}`}>Bueno</Badge>
                <span className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'Puedes cubrir pagos, hay oportunidades de optimización' : 'Puedes cubrir tus pagos, pero hay oportunidades de optimización'}
                </span>
              </>
            ) : (
              <>
                <Badge className={`bg-red-100 text-red-800 ${isMobile ? 'text-xs px-2 py-0.5' : ''}`}>Atención</Badge>
                <span className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? 'Pagos superan ingresos - requiere acción' : 'Tus pagos superan tus ingresos - requiere acción inmediata'}
                </span>
              </>
            )}
          </div>
          <div className={`mt-2 text-gray-500 ${isMobile ? 'text-xs' : 'text-xs'}`}>
            Total pagos del mes: {formatCurrency(totalMonthlyPayments)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SummaryQuickView;
