
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useSubscriptionData } from '@/nucleo_dominio_compartido/hooks/useSubscriptionData';
import { useCurrency } from '@/contexts/CurrencyContext';
import { Reimbursement, FinancialGoal, GoalContribution } from '@/types';

interface UseSupabaseDataOptions {
  enabled?: boolean;
}

export const useSupabaseData = ({ enabled = true }: UseSupabaseDataOptions = {}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { defaultCurrency, setDefaultCurrency: updateDefaultCurrency } = useCurrency();

  // Use the optimized subscription hook
  const subscriptionData = useSubscriptionData();

  // Fetch reimbursements
  const { data: reimbursements = [], isLoading: isLoadingReimbursements } = useQuery({
    queryKey: ['reimbursements', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('reimbursements')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false });

      if (error) throw error;
      
      return data.map(item => ({
        id: item.id,
        date: item.date,
        categoryId: item.category_id,
        categoryName: item.category_name,
        amount: item.amount,
        currency: item.currency,
        status: item.status,
        reimbursementDate: item.reimbursement_date,
        description: item.description || '',
        attachments: item.attachments || [],
        notes: item.notes || '',
        createdAt: item.created_at,
        updatedAt: item.updated_at
      })) as Reimbursement[];
    },
    enabled: enabled && !!user?.id,
  });

  // Fetch financial goals
  const { data: financialGoals = [], isLoading: isLoadingGoals } = useQuery({
    queryKey: ['financial_goals', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('financial_goals')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data.map(goal => ({
        id: goal.id,
        name: goal.name,
        amount: goal.amount,
        currentAmount: goal.current_amount,
        monthlyContribution: goal.monthly_contribution,
        targetDate: goal.target_date,
        currency: goal.currency,
        isActive: goal.is_active,
        category: goal.category,
        priority: goal.priority,
        createdAt: goal.created_at,
        updatedAt: goal.updated_at
      })) as FinancialGoal[];
    },
    enabled: enabled && !!user?.id,
  });

  // Fetch goal contributions
  const { data: goalContributions = [], isLoading: isLoadingContributions } = useQuery({
    queryKey: ['goal_contributions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      
      const { data, error } = await supabase
        .from('goal_contributions')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false });

      if (error) throw error;
      
      return data as GoalContribution[];
    },
    enabled: enabled && !!user?.id,
  });

  // Default currency provided by context

  const isLoading = subscriptionData.isLoading || isLoadingReimbursements || isLoadingGoals || isLoadingContributions;

  // Reimbursement mutations
  const addReimbursementMutation = useMutation({
    mutationFn: async (reimbursement: Omit<Reimbursement, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('reimbursements')
        .insert({
          user_id: user.id,
          date: reimbursement.date,
          category_id: reimbursement.categoryId,
          category_name: reimbursement.categoryName,
          amount: reimbursement.amount,
          currency: reimbursement.currency,
          status: reimbursement.status,
          reimbursement_date: reimbursement.reimbursementDate,
          description: reimbursement.description,
          attachments: reimbursement.attachments,
          notes: reimbursement.notes
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements', user?.id] });
      toast({
        title: "¡Reembolso agregado!",
        description: "El reembolso ha sido registrado exitosamente.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error al agregar reembolso",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const updateReimbursementMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Reimbursement> }) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('reimbursements')
        .update({
          date: updates.date,
          category_id: updates.categoryId,
          category_name: updates.categoryName,
          amount: updates.amount,
          currency: updates.currency,
          status: updates.status,
          reimbursement_date: updates.reimbursementDate,
          description: updates.description,
          attachments: updates.attachments,
          notes: updates.notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements', user?.id] });
      toast({
        title: "¡Reembolso actualizado!",
        description: "Los cambios han sido guardados exitosamente.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar reembolso",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const deleteReimbursementMutation = useMutation({
    mutationFn: async (id: string) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('reimbursements')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements', user?.id] });
      toast({
        title: "¡Reembolso eliminado!",
        description: "El reembolso ha sido eliminado exitosamente.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error al eliminar reembolso",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Financial goal mutations
  const addFinancialGoalMutation = useMutation({
    mutationFn: async (goal: Omit<FinancialGoal, 'id' | 'createdAt' | 'updatedAt'>) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('financial_goals')
        .insert({
          user_id: user.id,
          name: goal.name,
          amount: goal.amount,
          current_amount: goal.currentAmount,
          monthly_contribution: goal.monthlyContribution,
          target_date: goal.targetDate,
          currency: goal.currency,
          is_active: goal.isActive,
          category: goal.category,
          priority: goal.priority
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['financial_goals', user?.id] });
    },
  });

  // Goal contribution mutation
  const addGoalContributionMutation = useMutation({
    mutationFn: async (contribution: { goalId: string; amount: number; note?: string; }) => {
      if (!user?.id) throw new Error('User not authenticated');
      const { error } = await supabase.rpc('add_goal_contribution', {
        p_goal_id: contribution.goalId,
        p_user_id: user.id,
        p_amount: contribution.amount,
        p_note: contribution.note || null,
      });
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['financial_goals', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['goal_contributions', user?.id] });
      toast({
        title: "¡Contribución agregada!",
        description: "Tu avance ha sido registrado exitosamente.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error al agregar contribución",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  return {
    // Subscription data
    subscriptions: subscriptionData.subscriptions,
    addSubscription: subscriptionData.addSubscription,
    updateSubscription: subscriptionData.updateSubscription,
    deleteSubscription: subscriptionData.deleteSubscription,
    toggleSubscriptionStatus: subscriptionData.toggleSubscriptionStatus,
    
    // Other data
    reimbursements,
    financialGoals,
    goalContributions,
    defaultCurrency,
    isLoading,
    
    // Loading states for reimbursements
    isAddingReimbursement: addReimbursementMutation.isPending,
    isUpdatingReimbursement: updateReimbursementMutation.isPending,
    isDeletingReimbursement: deleteReimbursementMutation.isPending,
    
    // Reimbursement actions
    addReimbursement: (reimbursement: Omit<Reimbursement, 'id' | 'createdAt' | 'updatedAt'>) => {
      addReimbursementMutation.mutate(reimbursement);
    },
    updateReimbursement: (id: string, updates: Partial<Reimbursement>) => {
      updateReimbursementMutation.mutate({ id, updates });
    },
    deleteReimbursement: (id: string) => {
      deleteReimbursementMutation.mutate(id);
    },
    
    // Financial goal actions
    addFinancialGoal: (goal: Omit<FinancialGoal, 'id' | 'createdAt' | 'updatedAt'>) => {
      addFinancialGoalMutation.mutate(goal);
    },
    updateFinancialGoal: (id: string, updates: Partial<FinancialGoal>) => {
      // Implementation for update financial goal
    },
    deleteFinancialGoal: (id: string) => {
      // Implementation for delete financial goal
    },
    addGoalContribution: (goalId: string, amount: number, note?: string) => {
      addGoalContributionMutation.mutate({ goalId, amount, note });
    },
    
    // Settings actions
    setDefaultCurrency: (currency: 'DOP' | 'USD') => {
      updateDefaultCurrency(currency);
    }
  };
};
