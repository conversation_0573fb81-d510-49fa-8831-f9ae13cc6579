
import { useState, useCallback, useMemo } from 'react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, isValid } from 'date-fns';
import { es } from 'date-fns/locale';
import type { DateRange } from 'react-day-picker';

export interface TemporalPeriod {
  year: number; // Year of the start of the period or month
  month: number; // Month of the start of the period (1-12)
  startDate: string; // YYYY-MM-DD
  endDate: string; // YYYY-MM-DD
  displayName: string;
  isCurrentMonth: boolean; // True if the period is the current calendar month
  isPastMonth: boolean; // True if the period is a past calendar month
  isFutureMonth: boolean; // True if the period is a future calendar month
  displayMode: 'month' | 'range';
}

export type CurrentPeriod = TemporalPeriod; // Alias for clarity

export const useTemporalNavigation = () => {
  const initialDate = new Date();
  const [selectedDateInput, setSelectedDateInput] = useState<DateRange | undefined>({
    from: startOfMonth(initialDate),
    to: endOfMonth(initialDate),
  });
  const [currentDisplayMode, setCurrentDisplayMode] = useState<'month' | 'range'>('month');

  const currentPeriod = useMemo((): TemporalPeriod => {
    const now = new Date();
    let periodStart: Date | undefined = selectedDateInput?.from;
    let periodEnd: Date | undefined = selectedDateInput?.to;
    let displayName: string;
    let isCurrentM = false;
    let isPastM = false;
    let isFutureM = false;
    let year: number;
    let month: number;

    if (currentDisplayMode === 'month') {
      const displayMonthDate = periodStart || now;
      periodStart = startOfMonth(displayMonthDate);
      periodEnd = endOfMonth(displayMonthDate);
      displayName = format(displayMonthDate, 'MMMM yyyy', { locale: es });

      const smNow = startOfMonth(now);
      isCurrentM = format(periodStart, 'yyyy-MM') === format(smNow, 'yyyy-MM');
      isPastM = periodStart < smNow;
      isFutureM = periodStart > smNow; // More precisely, periodStart > endOfMonth(now)
      year = periodStart.getFullYear();
      month = periodStart.getMonth() + 1;

    } else if (periodStart && periodEnd && isValid(periodStart) && isValid(periodEnd)) {
      displayName = `${format(periodStart, 'dd/MM/yyyy', { locale: es })} - ${format(periodEnd, 'dd/MM/yyyy', { locale: es })}`;
      // For ranges, month-specific flags are less relevant or could be based on the start date
      isCurrentM = false;
      isPastM = false;
      isFutureM = false;
      year = periodStart.getFullYear();
      month = periodStart.getMonth() + 1;
    } else {
      // Fallback or default state, e.g., current month
      const fallbackDate = startOfMonth(now);
      periodStart = fallbackDate;
      periodEnd = endOfMonth(now);
      displayName = format(fallbackDate, 'MMMM yyyy', { locale: es });
      isCurrentM = true;
      year = fallbackDate.getFullYear();
      month = fallbackDate.getMonth() + 1;
    }

    return {
      year,
      month,
      startDate: format(periodStart, 'yyyy-MM-dd'),
      endDate: format(periodEnd, 'yyyy-MM-dd'),
      displayName,
      isCurrentMonth: isCurrentM,
      isPastMonth: isPastM,
      isFutureMonth: isFutureM,
      displayMode: currentDisplayMode,
    };
  }, [selectedDateInput, currentDisplayMode]);

  const setDateRange = useCallback((dateRange: DateRange | undefined) => {
    if (dateRange?.from && dateRange.to && isValid(dateRange.from) && isValid(dateRange.to)) {
       // If 'from' and 'to' are different days, it's a range.
      if (format(dateRange.from, 'yyyy-MM-dd') !== format(dateRange.to, 'yyyy-MM-dd')) {
        setSelectedDateInput(dateRange);
        setCurrentDisplayMode('range');
      } else { // If 'from' and 'to' are the same, treat as selecting that month.
        const selectedMonthDate = dateRange.from;
        setSelectedDateInput({ from: startOfMonth(selectedMonthDate), to: endOfMonth(selectedMonthDate) });
        setCurrentDisplayMode('month');
      }
    } else if (dateRange?.from && isValid(dateRange.from) && !dateRange.to) {
      // Only 'from' is selected, treat as selecting that month
      const selectedMonthDate = dateRange.from;
      setSelectedDateInput({ from: startOfMonth(selectedMonthDate), to: endOfMonth(selectedMonthDate) });
      setCurrentDisplayMode('month');
    } else {
      // Undefined or invalid range, reset to current month
      const currentMonthStart = startOfMonth(new Date());
      const currentMonthEnd = endOfMonth(new Date());
      setSelectedDateInput({ from: currentMonthStart, to: currentMonthEnd });
      setCurrentDisplayMode('month');
    }
  }, []);

  const navigateToMonth = useCallback((year: number, monthNumber: number) => { // monthNumber is 1-12
    const targetDate = new Date(year, monthNumber - 1, 1);
    setSelectedDateInput({ from: startOfMonth(targetDate), to: endOfMonth(targetDate) });
    setCurrentDisplayMode('month');
  }, []);

  const navigateToPreviousMonth = useCallback(() => {
    setCurrentDisplayMode('month');
    setSelectedDateInput(prevRange => {
      const currentFrom = prevRange?.from || new Date();
      const previousMonthDate = subMonths(startOfMonth(currentFrom), 1);
      return { from: startOfMonth(previousMonthDate), to: endOfMonth(previousMonthDate) };
    });
  }, []);

  const navigateToNextMonth = useCallback(() => {
    setCurrentDisplayMode('month');
    setSelectedDateInput(prevRange => {
      const currentFrom = prevRange?.from || new Date();
      const nextMonthDate = addMonths(startOfMonth(currentFrom), 1);
      return { from: startOfMonth(nextMonthDate), to: endOfMonth(nextMonthDate) };
    });
  }, []);

  const navigateToCurrentMonth = useCallback(() => {
    const today = new Date();
    setSelectedDateInput({ from: startOfMonth(today), to: endOfMonth(today) });
    setCurrentDisplayMode('month');
  }, []);

  const getAvailableMonths = useCallback((monthsBack: number = 12, monthsForward: number = 6): Omit<TemporalPeriod, 'startDate' | 'endDate' | 'displayMode'>[] => {
    // This function provides simplified month data for dropdowns, not full TemporalPeriod objects
    const months: Omit<TemporalPeriod, 'startDate' | 'endDate' | 'displayMode'>[] = [];
    const now = new Date();
    
    for (let i = monthsBack; i > 0; i--) {
      const date = subMonths(now, i);
      months.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        displayName: format(date, 'MMMM yyyy', { locale: es }),
        isCurrentMonth: false,
        isPastMonth: true,
        isFutureMonth: false,
      });
    }

    months.push({
      year: now.getFullYear(),
      month: now.getMonth() + 1,
      displayName: format(now, 'MMMM yyyy', { locale: es }),
      isCurrentMonth: true,
      isPastMonth: false,
      isFutureMonth: false,
    });

    for (let i = 1; i <= monthsForward; i++) {
      const date = addMonths(now, i);
      months.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        displayName: format(date, 'MMMM yyyy', { locale: es }),
        isCurrentMonth: false,
        isPastMonth: false,
        isFutureMonth: true,
      });
    }
    return months;
  }, []);

  return {
    currentPeriod,
    selectedDateInput, // Expose this for the DateRangePicker
    currentDisplayMode, // Expose this for UI logic
    setDateRange,
    navigateToMonth,
    navigateToPreviousMonth,
    navigateToNextMonth,
    navigateToCurrentMonth,
    getAvailableMonths
  };
};
