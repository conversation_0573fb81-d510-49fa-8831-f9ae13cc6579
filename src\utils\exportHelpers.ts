
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

interface ExportOptions {
  scale?: number;
  quality?: number;
  compression?: boolean;
  chunks?: boolean;
}

export async function exportToPDF(
  element: HTMLElement, 
  filename: string, 
  quality: 'standard' | 'high' | 'print' = 'high',
  options: ExportOptions = {}
) {
  try {
    // Configuración optimizada según calidad
    const scaleMap = {
      standard: 1.5,
      high: 2.5,
      print: 4
    };

    const scale = options.scale || scaleMap[quality];
    const useCompression = options.compression !== false;
    const useChunks = options.chunks || quality === 'print';

    console.log(`🚀 Iniciando exportación PDF profesional - Calidad: ${quality}, Escala: ${scale}x`);

    // Preparar elemento para renderizado PDF profesional con colores optimizados
    const prepareElementForPDFExport = (el: HTMLElement) => {
      // Aplicar estilos específicos para PDF con alta legibilidad
      el.style.transform = 'none';
      el.style.fontSize = quality === 'print' ? '11px' : quality === 'high' ? '12px' : '13px';
      el.style.fontFamily = "'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif";
      el.style.lineHeight = '1.4';
      el.style.color = '#1F2937'; // gray-800 - Contraste 13.1:1
      el.style.backgroundColor = '#FFFFFF';
      
      // Optimizar elementos de texto para máxima legibilidad
      const textElements = el.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, td, th');
      textElements.forEach(textEl => {
        const htmlEl = textEl as HTMLElement;
        // Aplicar optimización de renderizado para mejor legibilidad
        htmlEl.style.textRendering = 'optimizeLegibility';
        
        // Asegurar colores de alto contraste
        const computedStyle = window.getComputedStyle(htmlEl);
        if (computedStyle.color === 'rgb(156, 163, 175)') { // gray-400
          htmlEl.style.color = '#374151'; // gray-700 - Mejor contraste
        }
        if (computedStyle.color === 'rgb(107, 114, 128)') { // gray-500
          htmlEl.style.color = '#374151'; // gray-700 - Mejor contraste
        }
      });

      // Asegurar visibilidad de bordes y fondos
      const borderedElements = el.querySelectorAll('[style*="border"], .border, [class*="border-"]');
      borderedElements.forEach(borderEl => {
        const htmlEl = borderEl as HTMLElement;
        // Aplicar estilos para mejor visibilidad en PDF
        const style = htmlEl.style as any;
        if (style.setProperty) {
          style.setProperty('print-color-adjust', 'exact');
        }
      });
    };

    // Configuración optimizada para html2canvas con máxima calidad
    const canvasOptions = {
      scale: scale,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      scrollX: 0,
      scrollY: 0,
      width: element.scrollWidth,
      height: element.scrollHeight,
      windowWidth: 1200,
      windowHeight: element.scrollHeight,
      imageTimeout: 20000,
      removeContainer: true,
      foreignObjectRendering: true,
      onclone: (clonedDoc: Document) => {
        console.log('📋 Preparando elemento clonado para renderizado profesional');
        const clonedElement = clonedDoc.body.firstElementChild as HTMLElement;
        if (clonedElement) {
          prepareElementForPDFExport(clonedElement);
        }
      }
    };

    console.log('🎨 Renderizando elemento a canvas con calidad empresarial...');
    const canvas = await html2canvas(element, canvasOptions);

    const compressionQuality = quality === 'print' ? 1.0 : quality === 'high' ? 0.98 : 0.95;
    const imgData = canvas.toDataURL('image/png', compressionQuality);
    console.log(`📸 Canvas generado: ${canvas.width}x${canvas.height}px con calidad ${compressionQuality}`);
    
    // Configurar PDF profesional con formato A4 optimizado
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const ratio = imgWidth / imgHeight;
    
    // Configuraciones A4 profesionales
    const pdfWidth = 210; // A4 width in mm
    const pdfHeight = 297; // A4 height in mm
    
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
      compress: useCompression,
      precision: 2
    });

    // Calcular dimensiones profesionales con márgenes optimizados
    const margin = quality === 'print' ? 20 : quality === 'high' ? 18 : 15;
    const maxWidth = pdfWidth - (margin * 2);
    const maxHeight = pdfHeight - (margin * 2);
    
    let finalWidth = maxWidth;
    let finalHeight = finalWidth / ratio;
    
    console.log('📄 Configurando paginación profesional del PDF...');

    // Procesamiento profesional con chunks optimizados
    if (finalHeight > maxHeight && useChunks) {
      console.log('📚 Documento multipágina - Procesando con calidad empresarial...');
      const totalPages = Math.ceil(finalHeight / maxHeight);
      
      for (let i = 0; i < totalPages; i++) {
        if (i > 0) {
          pdf.addPage();
          console.log(`📄 Agregando página ${i + 1}/${totalPages}`);
        }
        
        const sourceY = (imgHeight / totalPages) * i;
        const sourceHeight = Math.min(imgHeight / totalPages, imgHeight - sourceY);
        
        // Crear canvas temporal optimizado para máxima calidad
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = imgWidth;
        tempCanvas.height = sourceHeight;
        const tempCtx = tempCanvas.getContext('2d', { 
          alpha: false,
          desynchronized: true,
          willReadFrequently: false
        });
        
        if (tempCtx) {
          // Configurar contexto para máxima calidad
          tempCtx.imageSmoothingEnabled = true;
          tempCtx.imageSmoothingQuality = 'high';
          
          tempCtx.drawImage(canvas, 0, sourceY, imgWidth, sourceHeight, 0, 0, imgWidth, sourceHeight);
          const tempImgData = tempCanvas.toDataURL('image/png', compressionQuality);
          
          // Renderizado de máxima calidad
          const renderQuality = quality === 'print' ? 'SLOW' : 'MEDIUM';
          pdf.addImage(tempImgData, 'PNG', margin, margin, finalWidth, Math.min(maxHeight, finalHeight - (maxHeight * i)), undefined, renderQuality);
        }
      }
    } else {
      console.log('📄 Documento de una página con calidad profesional');
      if (finalHeight > maxHeight) {
        finalHeight = maxHeight;
        finalWidth = finalHeight * ratio;
      }
      
      const renderQuality = quality === 'print' ? 'SLOW' : quality === 'high' ? 'MEDIUM' : 'FAST';
      pdf.addImage(imgData, 'PNG', margin, margin, finalWidth, finalHeight, undefined, renderQuality);
    }
    
    // Metadatos profesionales empresariales
    const currentDate = new Date().toLocaleDateString('es-DO');
    pdf.setProperties({
      title: `Estado de Cuenta Financiero Profesional - ${currentDate}`,
      subject: 'Análisis Integral de Salud Financiera con Arquitectura Empresarial',
      author: 'FINANZ - Financial Analytics & Insights Platform',
      creator: 'Sistema de Diagnóstico Financiero FINANZ v3.0 Professional',
      keywords: 'finanzas,diagnóstico,salud financiera,análisis profesional,reporte empresarial,proyecciones'
    });

    // Watermark empresarial discreto
    if (quality === 'print' || quality === 'high') {
      const pageCount = pdf.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        pdf.setPage(i);
        pdf.setTextColor(180, 180, 180); // Color más visible para watermark
        pdf.setFontSize(8);
        pdf.text('FINANZ Professional - Confidencial', pdfWidth - 65, pdfHeight - 8);
        pdf.text(`Página ${i} de ${pageCount}`, pdfWidth - 35, pdfHeight - 3);
      }
    }
    
    console.log('💾 Guardando PDF profesional con calidad empresarial...');
    pdf.save(filename);
    
    console.log('✅ Exportación PDF profesional completada exitosamente');
  } catch (error) {
    console.error('❌ Error al generar PDF profesional:', error);
    throw new Error(`Error al generar el PDF profesional: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

export async function exportToPNG(
  element: HTMLElement, 
  filename: string,
  options: ExportOptions = {}
) {
  try {
    console.log('🚀 Iniciando exportación PNG optimizada...');
    
    const scale = options.scale || 3;
    const quality = options.quality || 1.0;

    // Preparar elemento para exportación PNG con colores optimizados
    const prepareForPNG = (el: HTMLElement) => {
      el.style.transform = 'none';
      el.style.maxWidth = 'none';
      el.style.maxHeight = 'none';
      el.style.color = '#1F2937'; // Asegurar texto legible
      
      // Optimizar elementos para PNG
      const elements = el.querySelectorAll('*');
      elements.forEach((elem) => {
        const htmlElem = elem as HTMLElement;
        if (htmlElem.style) {
          htmlElem.style.backfaceVisibility = 'hidden';
          htmlElem.style.transform = 'translateZ(0)';
        }
      });
    };

    const canvas = await html2canvas(element, {
      scale: scale,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      scrollX: 0,
      scrollY: 0,
      width: element.scrollWidth,
      height: element.scrollHeight,
      windowWidth: element.scrollWidth,
      windowHeight: element.scrollHeight,
      imageTimeout: 10000,
      onclone: (clonedDoc) => {
        console.log('📋 Preparando elemento para PNG...');
        const clonedElement = clonedDoc.body.firstElementChild as HTMLElement;
        if (clonedElement) {
          prepareForPNG(clonedElement);
        }
      }
    });

    console.log(`📸 Canvas PNG generado: ${canvas.width}x${canvas.height}px`);

    // Convertir a blob con compresión optimizada
    const convertToBlob = (): Promise<Blob | null> => {
      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob);
        }, 'image/png', quality);
      });
    };

    const blob = await convertToBlob();
    
    if (blob) {
      console.log(`💾 Guardando PNG optimizado: ${(blob.size / 1024 / 1024).toFixed(2)}MB`);
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Limpiar memoria
      setTimeout(() => URL.revokeObjectURL(url), 100);
      
      console.log('✅ Exportación PNG completada exitosamente');
    } else {
      throw new Error('No se pudo generar el blob de la imagen');
    }
  } catch (error) {
    console.error('❌ Error al generar PNG:', error);
    throw new Error(`Error al generar la imagen PNG: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

// Utilidad para optimizar elementos antes de la exportación
export const optimizeElementForExport = (element: HTMLElement, type: 'pdf' | 'png' = 'pdf') => {
  console.log(`🔧 Optimizando elemento para exportación ${type.toUpperCase()}...`);
  
  // Aplicar estilos base para exportación con alta legibilidad
  element.style.position = 'static';
  element.style.transform = 'none';
  element.style.color = '#1F2937'; // gray-800 para máximo contraste
  
  if (type === 'pdf') {
    // Optimizaciones específicas para PDF
    element.style.fontSize = '12px'; // Tamaño legible
    element.style.lineHeight = '1.4';
  } else {
    // Optimizaciones específicas para PNG
    element.style.fontSize = '14px'; // Ligeramente más grande para PNG
    element.style.lineHeight = '1.5';
  }
  
  // Optimizar elementos hijos para mejor legibilidad
  const childElements = element.querySelectorAll('*');
  childElements.forEach((child) => {
    const htmlChild = child as HTMLElement;
    if (htmlChild.style) {
      htmlChild.style.backfaceVisibility = 'hidden';
      // Mejorar contraste de textos grises
      const computedStyle = window.getComputedStyle(htmlChild);
      if (computedStyle.color === 'rgb(156, 163, 175)') { // gray-400
        htmlChild.style.color = '#374151'; // gray-700
      }
    }
  });
  
  console.log('✅ Optimización de legibilidad completada');
};
