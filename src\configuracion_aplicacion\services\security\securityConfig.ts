import { logger } from "@/utils/logger";

import { SecuritySettings, SecurityLevel } from './types';

export class SecurityConfig {
  static getDefaultSettings(): SecuritySettings {
    return {
      twoFactorAuth: false,
      biometricAuth: false,
      sessionTimeout: '30',
      autoLogout: true,
      securityLevel: 'basic',
      requireReauth: false,
      lockOnMinimize: false,
    };
  }

  static applySecurityLevel(settings: SecuritySettings, level: SecurityLevel): SecuritySettings {
    logger.debug('SecurityConfig: Applying security level:', level);
    
    const updatedSettings = { ...settings };
    
    switch (level) {
      case 'basic':
        updatedSettings.sessionTimeout = '60';
        updatedSettings.requireReauth = false;
        updatedSettings.lockOnMinimize = false;
        updatedSettings.autoLogout = true;
        break;
      case 'strict':
        updatedSettings.sessionTimeout = '30';
        updatedSettings.requireReauth = false;
        updatedSettings.lockOnMinimize = true;
        updatedSettings.autoLogout = true;
        break;
      case 'maximum':
        updatedSettings.sessionTimeout = '10';
        updatedSettings.requireReauth = true;
        updatedSettings.twoFactorAuth = true;
        updatedSettings.lockOnMinimize = true;
        updatedSettings.autoLogout = true;
        break;
    }
    
    return updatedSettings;
  }

  static getTimeoutMs(sessionTimeout: string): number {
    return parseInt(sessionTimeout) * 60 * 1000;
  }
}
