
import React, { useState } from 'react';
import { Plus, FileText, Calculator, Edit, Trash2 } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TouchButton } from '@/components/ui/touch-button';
import { Badge } from '@/components/ui/badge';
import { useFinanceData } from '@/hooks/useFinanceData';
import { LoanForm } from './LoanForm';
import { AmortizationTable } from './AmortizationTable';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Loan } from '@/types';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export function LoansSection() {
  const { loans, addLoan, updateLoan, deleteLoan } = useFinanceData();
  const [showForm, setShowForm] = useState(false);
  const [editingLoan, setEditingLoan] = useState<Loan | null>(null);
  const [showAmortization, setShowAmortization] = useState<Loan | null>(null);
  const { isMobile, isTablet } = useBreakpoint();

  const handleAddLoan = (loanData: Omit<Loan, 'id'>) => {
    addLoan(loanData);
    setShowForm(false);
    setEditingLoan(null);
  };

  const handleUpdateLoan = (loanData: Omit<Loan, 'id'>) => {
    if (editingLoan) {
      updateLoan(editingLoan.id, loanData);
      setShowForm(false);
      setEditingLoan(null);
    }
  };

  const handleDeleteLoan = (id: string) => {
    deleteLoan(id);
  };

  const handleEditLoan = (loan: Loan) => {
    setEditingLoan(loan);
    setShowForm(true);
  };

  const handleCancelEdit = () => {
    setShowForm(false);
    setEditingLoan(null);
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge className="bg-green-100 text-green-800 text-xs">Activo</Badge>
    ) : (
      <Badge variant="secondary" className="text-xs">Inactivo</Badge>
    );
  };

  const activeLoans = loans.filter(loan => loan.isActive);
  const inactiveLoans = loans.filter(loan => !loan.isActive);

  // Si se está mostrando la tabla de amortización
  if (showAmortization) {
    return (
      <AmortizationTable 
        loan={showAmortization} 
        onClose={() => setShowAmortization(null)} 
      />
    );
  }

  const LoansList = ({ items }: { items: Loan[] }) => (
    <div className={`space-y-2 md:space-y-3 ${isMobile ? 'px-1' : ''}`}>
      {items.length === 0 ? (
        <div className="text-center py-6 md:py-8">
          <FileText className={`${isMobile ? 'w-8 h-8' : 'w-12 h-12'} text-finanz-text-secondary mx-auto mb-3`} />
          <p className={`text-finanz-text-secondary ${isMobile ? 'text-sm' : ''}`}>
            No hay préstamos en esta categoría
          </p>
        </div>
      ) : (
        items.map((loan) => (
          <Card key={loan.id} className={`hover:shadow-md transition-shadow ${isMobile ? 'shadow-sm' : ''}`}>
            <CardContent className={`${isMobile ? 'p-3' : 'p-4'}`}>
              <div className={`${isMobile ? 'space-y-3' : 'flex items-center justify-between'}`}>
                <div className={`flex items-center gap-3 ${isMobile ? 'flex-1' : ''}`}>
                  <div className={`p-2 bg-finanz-neutral/10 rounded-lg ${isMobile ? 'flex-shrink-0' : ''}`}>
                    <FileText className="w-4 h-4" />
                  </div>
                  <div className={`${isMobile ? 'flex-1 min-w-0' : ''}`}>
                    <h4 className={`font-medium text-finanz-text-primary ${isMobile ? 'text-sm truncate' : ''}`}>
                      {loan.name}
                    </h4>
                    <p className={`text-finanz-text-secondary ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      {loan.type} • Pago: {new Date(loan.paymentDate).toLocaleDateString()}
                    </p>
                    <div className={`flex gap-4 mt-1 text-finanz-text-secondary ${isMobile ? 'text-xs flex-wrap' : 'text-xs'}`}>
                      <span>Total: {formatCurrency(loan.totalAmount, loan.currency)}</span>
                      <span>Plazo: {loan.loanTerm}m</span>
                      <span>Interés: {loan.interestRate}%</span>
                    </div>
                  </div>
                </div>
                
                <div className={`${isMobile ? 'flex items-center justify-between' : 'text-right'}`}>
                  <div className={`${isMobile ? 'flex-1' : ''}`}>
                    <div className={`flex items-center gap-2 mb-1 ${isMobile ? 'justify-start' : 'justify-end'}`}>
                      <span className={`font-semibold text-finanz-primary ${isMobile ? 'text-sm' : ''}`}>
                        {formatCurrency(loan.monthlyPayment, loan.currency)}
                      </span>
                      {getStatusBadge(loan.isActive)}
                    </div>
                  </div>
                  
                  <div className={`flex gap-1 ${isMobile ? 'flex-shrink-0' : 'gap-2'}`}>
                    <TouchButton
                      variant="outline"
                      size={isMobile ? "sm" : "sm"}
                      onClick={() => setShowAmortization(loan)}
                      title="Ver tabla de amortización"
                      className={`${isMobile ? 'h-8 w-8 p-0' : 'gap-1'}`}
                    >
                      <Calculator className="w-4 h-4" />
                      {!isMobile && <span className="sr-only">Tabla</span>}
                    </TouchButton>
                    <TouchButton
                      variant="outline"
                      size={isMobile ? "sm" : "sm"}
                      onClick={() => handleEditLoan(loan)}
                      title="Editar préstamo"
                      className={`${isMobile ? 'h-8 w-8 p-0' : 'gap-1'}`}
                    >
                      <Edit className="w-4 h-4" />
                      {!isMobile && <span className="sr-only">Editar</span>}
                    </TouchButton>
                    <TouchButton
                      variant="destructive"
                      size={isMobile ? "sm" : "sm"}
                      onClick={() => handleDeleteLoan(loan.id)}
                      title="Eliminar préstamo"
                      className={`${isMobile ? 'h-8 w-8 p-0' : ''}`}
                    >
                      <Trash2 className="w-4 h-4" />
                      {!isMobile && <span className="sr-only">Borrar</span>}
                    </TouchButton>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );

  return (
    <div className={`space-y-4 md:space-y-6 ${isMobile ? 'px-1' : ''}`}>
      <div className={`flex justify-between items-center ${isMobile ? 'flex-col space-y-3' : ''}`}>
        <div className={`${isMobile ? 'text-center' : ''}`}>
          <h2 className={`font-bold ${isMobile ? 'text-lg' : 'text-2xl'}`}>
            {isMobile ? 'Préstamos' : 'Gestión de Préstamos'}
          </h2>
          {!isMobile && (
            <p className="text-sm text-gray-600 mt-1">Administra tus préstamos y financiamientos</p>
          )}
        </div>
        <TouchButton 
          onClick={() => setShowForm(true)} 
          className="gap-2"
          size={isMobile ? "sm" : "default"}
        >
          <Plus className="w-4 h-4" />
          {isMobile ? 'Nuevo' : 'Nuevo Préstamo'}
        </TouchButton>
      </div>

      {showForm && (
        <LoanForm
          onSubmit={editingLoan ? handleUpdateLoan : handleAddLoan}
          onCancel={handleCancelEdit}
          editingLoan={editingLoan}
        />
      )}

      <div className="space-y-4 md:space-y-6">
        <div>
          <h3 className={`font-bold mb-3 ${isMobile ? 'text-base' : 'text-xl'}`}>
            {isMobile ? 'Activos' : 'Préstamos Activos'}
          </h3>
          <LoansList items={activeLoans} />
        </div>

        <div>
          <h3 className={`font-bold mb-3 ${isMobile ? 'text-base' : 'text-xl'}`}>
            {isMobile ? 'Inactivos' : 'Préstamos Inactivos'}
          </h3>
          <LoansList items={inactiveLoans} />
        </div>
      </div>
    </div>
  );
}
