
import React from 'react';
import { FileText, Edit, Trash2, MoreVertical, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Reimbursement } from '@/types';

interface ReimbursementsListProps {
  items: Reimbursement[];
  onEdit: (reimbursement: Reimbursement) => void;
  onDelete: (id: string) => void;
  onStatusChange: (id: string, newStatus: Reimbursement['status']) => void;
  isUpdating?: boolean;
  isDeleting?: boolean;
}

export function ReimbursementsList({ 
  items, 
  onEdit, 
  onDelete, 
  onStatusChange, 
  isUpdating = false,
  isDeleting = false 
}: ReimbursementsListProps) {
  const getStatusBadge = (status: Reimbursement['status']) => {
    switch (status) {
      case 'Pendiente':
        return <Badge variant="secondary">Pendiente</Badge>;
      case 'Procesando':
        return <Badge className="bg-blue-100 text-blue-800">Procesando</Badge>;
      case 'Completado':
        return <Badge className="bg-green-100 text-green-800">Completado</Badge>;
      case 'Rechazado':
        return <Badge variant="destructive">Rechazado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusOptions = (currentStatus: Reimbursement['status']) => {
    const allStatuses: Reimbursement['status'][] = ['Pendiente', 'Procesando', 'Completado', 'Rechazado'];
    return allStatuses.filter(status => status !== currentStatus);
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="w-12 h-12 text-finanz-text-secondary mx-auto mb-3" />
        <p className="text-finanz-text-secondary">No hay reembolsos registrados</p>
        <p className="text-sm text-finanz-text-secondary mt-1">
          Haz clic en "Nuevo Reembolso" para agregar tu primer reembolso
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {items.map((reimbursement) => (
        <Card key={reimbursement.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-finanz-neutral/10 rounded-lg">
                  <FileText className="w-4 h-4" />
                </div>
                <div>
                  <h4 className="font-medium text-finanz-text-primary">{reimbursement.description}</h4>
                  <p className="text-sm text-finanz-text-secondary">
                    {reimbursement.categoryName} • {new Date(reimbursement.date).toLocaleDateString()}
                  </p>
                  {reimbursement.reimbursementDate && (
                    <p className="text-xs text-finanz-success">
                      Reembolsado: {new Date(reimbursement.reimbursementDate).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-right">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-semibold text-finanz-primary">
                      {formatCurrency(reimbursement.amount, reimbursement.currency)}
                    </span>
                    {getStatusBadge(reimbursement.status)}
                  </div>
                  {reimbursement.notes && (
                    <p className="text-xs text-finanz-text-secondary max-w-40 truncate">
                      {reimbursement.notes}
                    </p>
                  )}
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      className="h-8 w-8 p-0" 
                      disabled={isUpdating || isDeleting}
                    >
                      {(isUpdating || isDeleting) ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <MoreVertical className="h-4 w-4" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-popover dark:bg-neutral-800">
                    <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuItem 
                      onClick={() => onEdit(reimbursement)}
                      disabled={isUpdating || isDeleting}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Editar
                    </DropdownMenuItem>
                    
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Cambiar estado</DropdownMenuLabel>
                    
                    {getStatusOptions(reimbursement.status).map((status) => (
                      <DropdownMenuItem 
                        key={status}
                        onClick={() => onStatusChange(reimbursement.id, status)}
                        disabled={isUpdating || isDeleting}
                      >
                        Marcar como {status}
                      </DropdownMenuItem>
                    ))}
                    
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => onDelete(reimbursement.id)}
                      className="text-red-600"
                      disabled={isUpdating || isDeleting}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Eliminar
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
