
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  BarChart3,
  DollarSign,
  Target,
  Minus
} from 'lucide-react';
import { useOptimizedCalculations } from '../hooks/useOptimizedCalculations';
import { formatCurrency } from '@/components/ui/numeric-input';
import { EnhancedTooltip } from './EnhancedTooltip';

type ComparisonPeriod = 'month' | 'quarter' | 'year';

interface ComparisonCardProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  current: number;
  previous: number;
  trend: 'up' | 'down' | 'stable';
  changePercentage: number;
  currency?: 'DOP' | 'USD';
  isPositive?: boolean;
  context?: string;
  recommendations?: string[];
}

const ComparisonCard: React.FC<ComparisonCardProps> = ({
  title,
  icon: Icon,
  current,
  previous,
  trend,
  changePercentage,
  currency = 'DOP',
  isPositive = true,
  context,
  recommendations = []
}) => {
  const getTrendColor = () => {
    if (trend === 'stable') return 'text-gray-500 bg-gray-50';
    if (trend === 'up') return isPositive ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50';
    return isPositive ? 'text-red-600 bg-red-50' : 'text-green-600 bg-green-50';
  };

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus;

  return (
    <EnhancedTooltip
      title={title}
      currentValue={current}
      previousValue={previous}
      currency={currency}
      trend={trend}
      changePercentage={changePercentage}
      context={context}
      recommendations={recommendations}
      isPositiveTrend={isPositive}
    >
      <Card className="hover:shadow-md transition-shadow cursor-help">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Icon className="w-4 h-4 text-gray-600" />
              <h3 className="font-medium text-sm text-gray-900">{title}</h3>
            </div>
            <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${getTrendColor()}`}>
              <TrendIcon className="w-3 h-3" />
              <span className="text-xs font-medium">
                {Math.abs(changePercentage).toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Actual:</span>
              <span className="font-semibold text-sm">
                {formatCurrency(current, currency)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Anterior:</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(previous, currency)}
              </span>
            </div>
            <div className="flex items-center justify-between pt-1 border-t">
              <span className="text-xs text-gray-500">Diferencia:</span>
              <span className={`text-sm font-medium ${
                trend === 'up' ? (isPositive ? 'text-green-600' : 'text-red-600') :
                trend === 'down' ? (isPositive ? 'text-red-600' : 'text-green-600') :
                'text-gray-600'
              }`}>
                {trend !== 'stable' && (trend === 'up' ? '+' : '-')}
                {formatCurrency(Math.abs(current - previous), currency)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </EnhancedTooltip>
  );
};

export const PeriodComparison: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<ComparisonPeriod>('month');
  const optimizedMetrics = useOptimizedCalculations();

  const comparisonData = {
    month: [
      {
        title: 'Ingresos Netos',
        icon: DollarSign,
        ...optimizedMetrics.netIncomeComparison,
        isPositive: true,
        context: 'Comparación con el mes anterior. Los ingresos incluyen salario fijo y variables.',
        recommendations: [
          'Considera negociar un aumento si tienes buen rendimiento',
          'Busca fuentes de ingresos adicionales si es necesario'
        ]
      },
      {
        title: 'Gastos Totales',
        icon: BarChart3,
        ...optimizedMetrics.expensesComparison,
        isPositive: false,
        context: 'Incluye todos los gastos registrados del mes actual vs anterior.',
        recommendations: [
          'Revisa las categorías con mayor aumento',
          'Identifica gastos prescindibles'
        ]
      },
      {
        title: 'Ahorro Neto',
        icon: Target,
        ...optimizedMetrics.savingsComparison,
        isPositive: true,
        context: 'Diferencia entre ingresos y gastos totales (incluyendo pagos programados).',
        recommendations: [
          'Intenta ahorrar al menos 20% de tus ingresos',
          'Automatiza tus ahorros para mayor consistencia'
        ]
      }
    ]
  };

  const periodLabels = {
    month: 'vs Mes Anterior',
    quarter: 'vs Trimestre Anterior',
    year: 'vs Año Anterior'
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Comparación Temporal
          </CardTitle>
          <div className="flex gap-2">
            {(Object.keys(periodLabels) as ComparisonPeriod[]).map((period) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period)}
                disabled={period !== 'month'} // Solo mes está implementado por ahora
              >
                {period === 'month' ? 'Mensual' : 
                 period === 'quarter' ? 'Trimestral' : 'Anual'}
              </Button>
            ))}
          </div>
        </div>
        <p className="text-sm text-gray-600">
          Análisis comparativo {periodLabels[selectedPeriod].toLowerCase()}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-3">
          {comparisonData[selectedPeriod].map((item, index) => (
            <ComparisonCard key={index} {...item} />
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-3">
            <BarChart3 className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="space-y-2">
              <h4 className="font-medium text-sm text-blue-900">
                Resumen del Período
              </h4>
              <div className="space-y-1 text-sm text-blue-800">
                <p>
                  • <strong>Eficiencia Financiera:</strong> {optimizedMetrics.efficiency.toFixed(1)}%
                </p>
                <p>
                  • <strong>Ratio de Liquidez:</strong> {optimizedMetrics.liquidityRatio.toFixed(2)}x
                </p>
                <p>
                  • <strong>Score de Salud:</strong> {optimizedMetrics.financialHealthScore.toFixed(0)}/100
                </p>
              </div>
              <div className="mt-2">
                <Badge 
                  variant={optimizedMetrics.financialHealthScore > 75 ? 'default' : 
                          optimizedMetrics.financialHealthScore > 50 ? 'secondary' : 'destructive'}
                >
                  {optimizedMetrics.financialHealthScore > 75 ? 'Excelente' :
                   optimizedMetrics.financialHealthScore > 50 ? 'Bueno' : 'Necesita Atención'}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
