import { logger } from "@/utils/logger";
import { useMemo, useEffect } from 'react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { getPreviousPeriod, getFormattedPeriod } from '@/seguimiento_pagos/utils/temporalUtils'; // Helper for period calculations
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { PaymentGenerationService } from '../services/paymentGenerationService';
import { PaymentMergeService } from '../services/paymentMergeService';
import { PaymentCategorizationService } from '../services/paymentCategorizationService';
import { PaymentActionService } from '../services/paymentActionService';
import { PaymentItem } from '../types/paymentTypes';
import type { PaymentRecord } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useExchangeRate } from '@/hooks/useExchangeRate';
import { TemporalPeriod } from "./useTemporalNavigation"; // Import TemporalPeriod
import { QUERY_KEYS } from '@/constants/queryKeys';

export const usePaymentsLogic = (currentPeriod?: TemporalPeriod) => { // Accept currentPeriod
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();
  const { rate: exchangeRate } = useExchangeRate();
  
  const { 
    creditCards, 
    loans, 
    subscriptions, 
    personalDebts,
    personalDebtPayments,
    expenses,
    paymentRecords,
    addPaymentRecord,
    markPaymentAsPaid,
    updatePaymentRecord,
    addPersonalDebtPayment,
    updatePersonalDebt,
    deletePersonalDebtPayment
  } = useFinanceData();

  // Inicializar servicios, PaymentGenerationService is now period-aware
  const paymentGenerationService = useMemo(() => {
    logger.debug("usePaymentsLogic: Initializing PaymentGenerationService with period:", currentPeriod);
    return new PaymentGenerationService(currentPeriod);
  }, [currentPeriod]); // Re-initialize if currentPeriod changes

  const paymentMergeService = useMemo(() => new PaymentMergeService(), []);
  // PaymentCategorizationService uses current date for overdue logic, so it doesn't need the period
  const paymentCategorizationService = useMemo(() => new PaymentCategorizationService(), []);
  const paymentActionService = useMemo(() => new PaymentActionService(), []);

  // Función optimizada para invalidar queries relacionadas
  const invalidateAllPaymentQueries = async () => {
    if (!user?.id) return;

    const queryKeysToInvalidate = [
      [QUERY_KEYS.PAYMENT_RECORDS, user.id],
      [QUERY_KEYS.DEBTS, user.id],
      [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, user.id],
      [QUERY_KEYS.EXPENSES, user.id],
      [QUERY_KEYS.LOANS, user.id],
      [QUERY_KEYS.SUBSCRIPTIONS, user.id]
    ];

    // Invalidar y refetch en paralelo para mejor rendimiento
    await Promise.all([
      ...queryKeysToInvalidate.map(queryKey =>
        queryClient.invalidateQueries({ queryKey, exact: true })
      ),
      ...queryKeysToInvalidate.map(queryKey =>
        queryClient.refetchQueries({ queryKey, exact: true })
      )
    ]);
  };

  // Efecto para invalidar cache cuando cambien las entidades relacionadas
  useEffect(() => {
    if (!user?.id) return;

    logger.debug('Entities changed, refreshing payments logic...');
    
    const timeoutId = setTimeout(() => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user.id],
        exact: true
      });
    }, 100);
    
    return () => clearTimeout(timeoutId);
  }, [
    creditCards, 
    loans, 
    subscriptions, 
    personalDebts, 
    expenses,
    paymentRecords,
    user?.id,
    queryClient
  ]);

  // Generar pagos próximos desde entidades financieras
  const generateUpcomingPayments = useMemo(() => {
    logger.debug('usePaymentsLogic: Generating upcoming payments for current period:', currentPeriod);
    
    const currentPeriodPayments = paymentGenerationService.generateAllPayments(
      creditCards,
      loans,
      subscriptions,
      personalDebts,
      expenses
    );

    logger.debug('usePaymentsLogic: Generated payments for current period summary:', {
      total: currentPeriodPayments.length,
      // ... (add byType logging if desired)
    });

    // --- BEGIN ROLLOVER LOGIC ---
    let carriedOverPayments: PaymentItem[] = [];
    if (currentPeriod) {
      // CRITICAL FIX: Only perform rollover logic when viewing the ACTUAL current month
      // This prevents showing overdue payments from future months in past months
      const now = new Date();
      const actualCurrentYear = now.getFullYear();
      const actualCurrentMonth = now.getMonth() + 1; // Convert from 0-11 to 1-12
      
      const isViewingActualCurrentMonth = 
        currentPeriod.year === actualCurrentYear && 
        currentPeriod.month === actualCurrentMonth;
      
      if (isViewingActualCurrentMonth) {
        logger.debug('usePaymentsLogic: Viewing actual current month, processing rollover logic');
        
        const previousPeriod = getPreviousPeriod(currentPeriod);
        if (previousPeriod) {
          logger.debug('usePaymentsLogic: Calculating payments for previous period (for rollover):', previousPeriod);
          const prevPeriodGenerationService = new PaymentGenerationService(previousPeriod);
          const prevPeriodGeneratedPayments = prevPeriodGenerationService.generateAllPayments(
            creditCards,
            loans,
            subscriptions,
            personalDebts,
            expenses
          );

          logger.debug('usePaymentsLogic: Generated payments from previous period:', prevPeriodGeneratedPayments.length);

          // Merge with records to get their true status
          const prevPeriodMergedPayments = paymentMergeService.mergePaymentsWithRecords(
            prevPeriodGeneratedPayments,
            paymentRecords // Use all available payment records
          );
          logger.debug('usePaymentsLogic: Merged payments from previous period:', prevPeriodMergedPayments.length);

          const todayForOverdueCheck = new Date();
          todayForOverdueCheck.setHours(0, 0, 0, 0);

          carriedOverPayments = prevPeriodMergedPayments.filter(p => {
            const dueDate = new Date(p.dueDate + "T00:00:00");
            const isEffectivelyOverdue = p.status === 'overdue' || (p.status === 'pending' && dueDate < todayForOverdueCheck);

            if (p.status === 'paid' || !isEffectivelyOverdue) {
              return false; // Don't carry over if paid or not effectively overdue
            }

            // CRITICAL TEMPORAL VALIDATION for expenses in rollover
            if (p.type === 'expense') {
              const originalExpense = expenses.find(exp => exp.id === p.referenceId);
              if (originalExpense) {
                // Validate creation date exists
                if (!originalExpense.createdAt) {
                  logger.warn(`usePaymentsLogic: Expense ${p.name} (ID: ${p.referenceId}) lacks createdAt. Data integrity issue - not carrying over.`);
                  return false;
                }
                
                // Parse and validate creation date
                const createdAtDate = new Date(originalExpense.createdAt);
                if (isNaN(createdAtDate.getTime())) {
                  logger.warn(`usePaymentsLogic: Expense ${p.name} has invalid createdAt: ${originalExpense.createdAt}. Not carrying over.`);
                  return false;
                }
                
                const currentPeriodStart = new Date(currentPeriod.year, currentPeriod.month - 1, 1);
                const prevPeriodEnd = new Date(previousPeriod.year, previousPeriod.month - 1, 0, 23, 59, 59, 999);
                const originalDueDate = new Date(p.dueDate + "T00:00:00");
                const dueBeforeCurrentPeriod = originalDueDate < currentPeriodStart;

                if (!dueBeforeCurrentPeriod && createdAtDate > prevPeriodEnd) {
                  logger.debug(`usePaymentsLogic: Expense ${p.name} (ID: ${p.referenceId}) created ${originalExpense.createdAt} after previous period ended ${prevPeriodEnd.toISOString().slice(0,10)} - not carrying over.`);
                  return false;
                }

                if (!dueBeforeCurrentPeriod && createdAtDate > originalDueDate) {
                  logger.warn(`usePaymentsLogic: Expense ${p.name} created ${originalExpense.createdAt} after its due date ${p.dueDate} - data integrity issue, not carrying over.`);
                  return false;
                }

                if (!dueBeforeCurrentPeriod && createdAtDate > currentPeriodStart) {
                  logger.debug(`usePaymentsLogic: Expense ${p.name} created ${originalExpense.createdAt} during/after current period start - not carrying over as it will be generated normally.`);
                  return false;
                }
              } else {
                logger.warn(`usePaymentsLogic: Could not find original expense for payment ${p.name} (RefID: ${p.referenceId}). Not carrying over.`);
                return false;
              }
            }
            return true; // Carry over
          }).map(p => ({ 
            ...p, 
            isCarriedOver: true, 
            id: `${p.id}-carried-${currentPeriod.year}-${currentPeriod.month}` 
          }));

          logger.debug('usePaymentsLogic: Payments to carry over from previous period (after enhanced validation):', carriedOverPayments.length, carriedOverPayments.map(p=>({name: p.name, id: p.id, type: p.type, refId: p.referenceId, dueDate: p.dueDate })));

          // --- NEW LOGIC: Include expenses created in current month with past due date ---
          const currentPeriodStart = new Date(currentPeriod.year, currentPeriod.month - 1, 1);
          const currentPeriodEnd = new Date(currentPeriod.year, currentPeriod.month, 0, 23, 59, 59, 999);

          const lateExpenses = expenses.filter(exp => {
            const created = new Date(exp.createdAt);
            const dueStr = exp.paymentDate ?? exp.date;
            const due = new Date(dueStr);
            return (
              created >= currentPeriodStart &&
              created <= currentPeriodEnd &&
              due < currentPeriodStart &&
              exp.status !== 'paid'
            );
          }).map(exp => {
            const dueStr = exp.paymentDate ?? exp.date;
            const due = new Date(dueStr + "T00:00:00");
            const idBase = `expense-${exp.id}-${due.getFullYear()}-${due.getMonth() + 1}`;
            return {
              id: `${idBase}-carried-${currentPeriod.year}-${currentPeriod.month}`,
              type: 'expense',
              name: `${exp.categoryName}${exp.description ? ' - ' + exp.description : ''}`,
              amount: exp.amount,
              currency: exp.currency,
              dueDate: dueStr,
              status: 'overdue',
              referenceId: exp.id,
              createdAt: exp.createdAt,
              isCarriedOver: true
            } as PaymentItem & { isCarriedOver: true };
          });

          carriedOverPayments = [...carriedOverPayments, ...lateExpenses];
        } else {
          logger.debug('usePaymentsLogic: No previous period to calculate rollover from.');
        }
      } else {
        logger.debug(`usePaymentsLogic: Viewing historical/future period (${currentPeriod.year}-${currentPeriod.month}), not actual current month (${actualCurrentYear}-${actualCurrentMonth}). Rollover logic disabled.`);
      }
    } else {
      logger.debug('usePaymentsLogic: currentPeriod is undefined, skipping rollover logic.');
    }
    // --- END ROLLOVER LOGIC ---

    // Combine current period payments with carried-over payments
    // Ensure unique items if there's overlap (e.g. by checking ID if not modified, or a composite key)
    // For now, simple concatenation. PaymentMergeService might handle some level of this later if keys are truly identical.
    // A more robust de-duplication might be needed if issues arise.
    const combinedPayments = [...currentPeriodPayments, ...carriedOverPayments];

    // Deduplicate based on a composite key: type-referenceId-dueDate
    // This is important because a payment might be generated for the current period
    // and also be identified as a carried-over item if its original due date was in the past
    // but it's a recurring item. We want the one from the current generation if it exists.
    const uniquePaymentsMap = new Map<string, PaymentItem>();

    // Prioritize current period payments
    currentPeriodPayments.forEach(p => {
      const key = `${p.type}-${p.referenceId}-${p.dueDate}`;
      uniquePaymentsMap.set(key, p);
    });

    // Add carried-over payments only if not already present from current generation
    carriedOverPayments.forEach(p => {
      // The ID of a carried-over payment is now like `originalId-carried-YYYY-MM`
      // We need to extract the original ID part for matching with `existingPayment.id`.
      const carriedSuffixIndex = p.id.lastIndexOf('-carried-');
      const originalIdFromCarried = carriedSuffixIndex !== -1 ? p.id.substring(0, carriedSuffixIndex) : p.id;

      // The key for the map uses the original due date of the payment.
      // The `p.dueDate` on a carried-over item is its original due date.
      const key = `${p.type}-${p.referenceId}-${p.dueDate}`;

      if (!uniquePaymentsMap.has(key)) {
        // If the payment (based on type, refId, and original dueDate) isn't in the current period's generation,
        // add the carried-over version.
        uniquePaymentsMap.set(key, p);
      } else {
        // If a payment with the same key (type, refId, original dueDate) IS generated for the current period:
        const existingPayment = uniquePaymentsMap.get(key)!; // Should exist due to .has(key) check

        // Check if this `existingPayment` corresponds to the `carriedOverPayment` `p`.
        // `existingPayment.id` is the original ID format (e.g., `expense-uuid-YYYY-MM-DD`)
        // `originalIdFromCarried` is also the original ID format.
        if (existingPayment.id === originalIdFromCarried) {
          // It's the same fundamental payment. Mark the one generated for the current period as `isCarriedOver`.
          (existingPayment as PaymentItem & { isCarriedOver?: boolean }).isCarriedOver = true;

          // If the carried-over version had a more severe status (e.g., overdue), update the existing one.
          // This ensures that if a payment was pending, became overdue, and is now generated again as pending,
          // its overdue status (as a carried item) takes precedence.
          if (p.status === 'overdue' && existingPayment.status === 'pending') {
            existingPayment.status = 'overdue';
          }
          logger.debug(`usePaymentsLogic: Marked existing payment ${existingPayment.name} (ID: ${existingPayment.id}) as isCarriedOver=true. Status updated to ${existingPayment.status} if carried was 'overdue'.`);
        } else {
          // This case (same key, different original IDs) should be rare if IDs are unique per (type, refId, originalDueDate).
          // If it occurs, it might indicate an issue with ID generation or data.
          // For safety, we might log or decide on a strategy, but typically we prioritize the current period's generation.
          logger.warn(`usePaymentsLogic: Deduplication conflict. Key: ${key}. Existing ID: ${existingPayment.id}, Carried Original ID: ${originalIdFromCarried}. Prioritizing existing payment. Carried item ${p.id} not added separately.`);
        }
      }
    });

    const finalPaymentsToProcess = Array.from(uniquePaymentsMap.values());


    logger.debug('usePaymentsLogic: Total payments after combining current and carried-over (and deduplication):', finalPaymentsToProcess.length);
    return finalPaymentsToProcess;

  }, [creditCards, loans, subscriptions, personalDebts, expenses, paymentGenerationService, currentPeriod, paymentMergeService, paymentRecords]);

  // Fusionar pagos generados con registros de pago
  const allPayments = useMemo(() => {
    // generateUpcomingPayments now includes carried-over items
    logger.debug('usePaymentsLogic: Starting payment merge with records (including carried-over items):', {
      generatedPaymentsCount: generateUpcomingPayments.length,
      paymentRecordsCount: paymentRecords.length
    });

    // The paymentMergeService should ideally handle the merged list which now contains current + carriedOver
    const merged = paymentMergeService.mergePaymentsWithRecords(generateUpcomingPayments, paymentRecords);
    
    logger.debug('usePaymentsLogic: Payments merged successfully (including carried-over):', {
      total: merged.length,
      paid: merged.filter(p => p.status === 'paid').length,
      pending: merged.filter(p => p.status === 'pending').length,
      overdue: merged.filter(p => p.status === 'overdue').length
    });
    
    return merged;
  }, [generateUpcomingPayments, paymentRecords, paymentMergeService]);

  // Categorizar pagos por estado
  const categorizedPayments = useMemo(() => {
    logger.debug('Categorizing payments with immediate updates...');
    
    const categorized = paymentCategorizationService.categorizePayments(allPayments);
    
    logger.debug('Payments categorized with immediate sync:', {
      pending: categorized.pending.length,
      paid: categorized.paid.length,
      overdue: categorized.overdue.length
    });

    if (categorized.paid.length > 0) {
      logger.debug('Paid payments details:', categorized.paid.map(p => ({
        id: p.id,
        name: p.name,
        status: p.status,
        paidDate: p.paidDate,
        recordId: p.recordId
      })));
    }
    
    // Refuerzo: garantizar que los pagos arrastrados mantengan la bandera isCarriedOver basada en su id o propiedades.
    const markCarryOver = (p: PaymentItem): PaymentItem & { isCarriedOver?: boolean } => {
      if (p.id.includes('-carried-')) return { ...(p as any), isCarriedOver: true } as any;

      // Inferencia adicional: si el dueDate es anterior al inicio del mes actual y la fecha de creación pertenece al mes actual, marcar como arrastrado.
      const due = new Date(p.dueDate + "T00:00:00");
      const created = p.createdAt ? new Date(p.createdAt) : null;
      const periodStart = new Date(currentPeriod?.year ?? 0, (currentPeriod?.month ?? 1) - 1, 1);
      if (created && created >= periodStart && due < periodStart) {
        return { ...(p as any), isCarriedOver: true } as any;
      }
      return p as any;
    };

    return {
      pending: categorized.pending.map(markCarryOver),
      paid: categorized.paid.map(markCarryOver),
      overdue: categorized.overdue.map(markCarryOver)
    };
  }, [allPayments, paymentCategorizationService, currentPeriod]);

  // Calcular totales con la tasa de cambio
  const totals = useMemo(() => {
    logger.debug('Calculating payment totals with exchange rate:', exchangeRate);
    return paymentCategorizationService.calculateTotals(categorizedPayments, exchangeRate);
  }, [categorizedPayments, paymentCategorizationService, exchangeRate]);

  // Función para marcar pago como pagado
  const handleMarkAsPaid = async (payment: PaymentItem, paymentData?: any) => {
    logger.info('handleMarkAsPaid: Function entry for payment ID:', payment.id);

    try {
      if (!user) {
        logger.error('handleMarkAsPaid: User not authenticated');
        throw new Error("User not authenticated for marking payment as paid.");
      }

      const queryKey = [QUERY_KEYS.PAYMENT_RECORDS, user.id];
      const previousRecords = queryClient.getQueryData<PaymentRecord[]>(queryKey);
      const paidDate = new Date().toISOString().split('T')[0];

      if (payment.recordId) {
        queryClient.setQueryData<PaymentRecord[]>(queryKey, records =>
          records?.map(r =>
            r.id === payment.recordId
              ? { ...r, status: 'paid' as const, paidDate, notes: paymentData?.notes ?? r.notes }
              : r
          )
        );
      }
      
      try {
        await paymentActionService.handleMarkAsPaid(
          payment,
          personalDebts,
          addPersonalDebtPayment,
          updatePersonalDebt,
          markPaymentAsPaid,
          addPaymentRecord,
          paymentData
        );
        
        logger.info('handleMarkAsPaid: Payment action service completed successfully');
        
        await invalidateAllPaymentQueries();

        toast({
          title: "Pago Completado",
          description: `El pago "${payment.name}" ha sido marcado como completado.`,
        });

      } catch (serviceError) {
        if (payment.recordId) {
          queryClient.setQueryData(queryKey, previousRecords);
        }
        logger.error('handleMarkAsPaid: Error calling paymentActionService.handleMarkAsPaid', {
          paymentId: payment.id,
          error: serviceError
        });
        throw serviceError;
      }
      
    } catch (error) {
      logger.error('handleMarkAsPaid: General error in payment marking process', {
        paymentId: payment?.id,
        error: error
      });
      
      toast({
        title: "Error al Procesar Pago",
        description: `Hubo un problema al procesar el pago "${payment?.name}". Por favor, inténtalo de nuevo.`,
        variant: "destructive"
      });
      throw error;
    } finally {
      logger.info('handleMarkAsPaid: Function exit for payment ID:', payment?.id);
    }
  };

  // Función para desmarcar pago como pagado - CORREGIDA
  const handleUnmarkAsPaid = async (payment: PaymentItem) => {
    logger.info('handleUnmarkAsPaid: Function entry for payment ID:', payment.id);
    logger.debug('handleUnmarkAsPaid: Payment object to unmark', payment);

    try {
      if (!user) {
        logger.error('handleUnmarkAsPaid: User not authenticated');
        throw new Error("User not authenticated for unmarking payment.");
      }

      if (!payment.recordId) {
        logger.error('handleUnmarkAsPaid: Payment missing recordId', payment);
        throw new Error("Cannot unmark payment without a recordId.");
      }

      const queryKey = [QUERY_KEYS.PAYMENT_RECORDS, user.id];
      const previousRecords = queryClient.getQueryData<PaymentRecord[]>(queryKey);

      logger.debug('handleUnmarkAsPaid: Applying optimistic update for recordId:', payment.recordId);
      queryClient.setQueryData<PaymentRecord[]>(queryKey, records => {
        if (!records) return records;
        return records.map(r =>
          r.id === payment.recordId 
            ? { ...r, status: 'pending' as const, paidDate: null }
            : r
        );
      });

      try {
        logger.debug('handleUnmarkAsPaid: Calling paymentActionService.handleUnmarkAsPaid');
        
        // Create adapter function to match expected signature with proper typing
        const updatePaymentRecordAdapter = async (id: string, updates: { status: string; paidDate: string | null; notes?: string }) => {
          return await updatePaymentRecord(id, {
            ...updates,
            status: updates.status as 'pending' | 'paid' | 'overdue'
          });
        };

        await paymentActionService.handleUnmarkAsPaid(
          payment,
          updatePaymentRecordAdapter
        );
        
        logger.info('handleUnmarkAsPaid: Payment action service completed successfully');
        
        await invalidateAllPaymentQueries();

        toast({
          title: "Pago Desmarcado",
          description: `El pago "${payment.name}" ha sido marcado como pendiente.`,
        });

        logger.info('handleUnmarkAsPaid: Success toast displayed for payment ID:', payment.id);

      } catch (serviceError) {
        logger.error('handleUnmarkAsPaid: Error calling paymentActionService.handleUnmarkAsPaid', {
          paymentId: payment.id,
          error: serviceError
        });
        
        if (previousRecords) {
          queryClient.setQueryData(queryKey, previousRecords);
        }
        throw serviceError;
      }

    } catch (error) {
      logger.error('handleUnmarkAsPaid: General error in payment unmarking process', {
        paymentId: payment?.id,
        error: error
      });

      toast({
        title: "Error al Desmarcar Pago",
        description: `Hubo un problema al desmarcar el pago "${payment?.name}". Por favor, inténtalo de nuevo.`,
        variant: "destructive"
      });
      throw error;
    } finally {
      logger.info('handleUnmarkAsPaid: Function exit for payment ID:', payment?.id);
    }
  };

  return {
    payments: categorizedPayments,
    totals,
    handleMarkAsPaid,
    handleUnmarkAsPaid,
    updatePaymentRecord,
    allPayments
  };
};

export type { PaymentItem } from '../types/paymentTypes';
