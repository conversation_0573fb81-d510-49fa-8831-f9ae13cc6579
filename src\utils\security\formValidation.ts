
import { z } from 'zod';
import { sanitizeInput, sanitizeFinancialAmount, sanitizeCurrency, sanitizeDate } from './sanitization';

// Enhanced financial form validation schemas
export const expenseFormSchema = z.object({
  amount: z.number()
    .min(0.01, 'El monto debe ser mayor a 0')
    .max(1000000000, 'El monto es demasiado alto')
    .refine((val) => Number.isFinite(val), 'Monto inválido'),
  description: z.string()
    .min(1, 'La descripción es requerida')
    .max(500, 'La descripción es demasiado larga')
    .transform(sanitizeInput),
  category: z.string()
    .min(1, 'La categoría es requerida')
    .transform(sanitizeInput),
  currency: z.string()
    .transform(sanitizeCurrency),
  date: z.string()
    .transform(sanitizeDate),
  paymentMethod: z.string()
    .min(1, 'El método de pago es requerido')
    .transform(sanitizeInput),
  month: z.string()
    .optional()
    .transform((val) => val ? sanitizeInput(val) : undefined),
  type: z.string()
    .min(1, 'El tipo es requerido')
    .transform(sanitizeInput),
  status: z.string()
    .min(1, 'El estado es requerido')
    .transform(sanitizeInput)
});

export const incomeFormSchema = z.object({
  month: z.string()
    .regex(/^\d{4}-\d{2}$/, 'Formato de mes inválido (YYYY-MM)'),
  fixedSalary: z.number()
    .min(0, 'El salario no puede ser negativo')
    .max(1000000000, 'El salario es demasiado alto'),
  variablePercentage: z.number()
    .min(0, 'El porcentaje variable no puede ser negativo'),
  currency: z.string()
    .transform(sanitizeCurrency),
  legalDeductions: z.number()
    .min(0, 'Las deducciones no pueden ser negativas'),
  payrollLoan: z.number()
    .min(0, 'El préstamo de nómina no puede ser negativo')
});

export const loanFormSchema = z.object({
  name: z.string()
    .min(1, 'El nombre es requerido')
    .max(100, 'El nombre es demasiado largo')
    .transform(sanitizeInput),
  type: z.string()
    .min(1, 'El tipo es requerido')
    .transform(sanitizeInput),
  totalAmount: z.number()
    .min(1, 'El monto total debe ser mayor a 0')
    .max(1000000000, 'El monto total es demasiado alto'),
  interestRate: z.number()
    .min(0, 'La tasa de interés no puede ser negativa')
    .max(100, 'La tasa de interés no puede exceder 100%'),
  loanTerm: z.number()
    .int('El plazo debe ser un número entero')
    .min(1, 'El plazo debe ser al menos 1 mes')
    .max(600, 'El plazo no puede exceder 50 años'),
  currency: z.string()
    .transform(sanitizeCurrency),
  startDate: z.string()
    .transform(sanitizeDate),
  paymentDate: z.string()
    .min(1, 'La fecha de pago es requerida')
    .transform(sanitizeInput)
});

export const financialGoalFormSchema = z.object({
  name: z.string()
    .min(1, 'El nombre es requerido')
    .max(100, 'El nombre es demasiado largo')
    .transform(sanitizeInput),
  amount: z.number()
    .min(1, 'El monto debe ser mayor a 0')
    .max(1000000000, 'El monto es demasiado alto'),
  currentAmount: z.number()
    .min(0, 'El monto actual no puede ser negativo'),
  targetDate: z.string()
    .transform(sanitizeDate),
  monthlyContribution: z.number()
    .min(0, 'La contribución mensual no puede ser negativa'),
  currency: z.string()
    .transform(sanitizeCurrency),
  category: z.string()
    .optional()
    .transform((val) => val ? sanitizeInput(val) : undefined)
});

// CSRF token validation
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const validateCSRFToken = (token: string, storedToken: string): boolean => {
  if (!token || !storedToken || token.length !== storedToken.length) {
    return false;
  }
  
  // Constant-time comparison to prevent timing attacks
  let result = 0;
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ storedToken.charCodeAt(i);
  }
  return result === 0;
};

// Enhanced validation with security checks
export const validateFinancialForm = <T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  csrfToken?: string
): { isValid: boolean; data?: T; errors?: string[] } => {
  try {
    // CSRF validation for sensitive operations
    if (csrfToken) {
      const storedToken = sessionStorage.getItem('csrf_token');
      if (!validateCSRFToken(csrfToken, storedToken || '')) {
        return { isValid: false, errors: ['Token de seguridad inválido'] };
      }
    }

    const validatedData = schema.parse(data);
    return { isValid: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => err.message);
      return { isValid: false, errors };
    }
    return { isValid: false, errors: ['Error de validación'] };
  }
};
