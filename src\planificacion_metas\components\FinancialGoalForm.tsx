
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { NumericInput } from '@/components/ui/numeric-input';
import { FinancialGoal } from '@/types';

interface FinancialGoalFormProps {
  onSubmit: (goal: Omit<FinancialGoal, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  editingGoal?: FinancialGoal | null;
}

export function FinancialGoalForm({ onSubmit, onCancel, editingGoal }: FinancialGoalFormProps) {
  const [formData, setFormData] = useState({
    name: editingGoal?.name || '',
    amount: editingGoal?.amount || 0,
    currentAmount: editingGoal?.currentAmount || 0,
    monthlyContribution: editingGoal?.monthlyContribution || 0,
    currency: editingGoal?.currency || 'DOP' as 'DOP' | 'USD',
    category: editingGoal?.category || '',
    targetDate: editingGoal?.targetDate || '',
    priority: editingGoal?.priority || 'medium' as 'high' | 'medium' | 'low',
    isActive: editingGoal?.isActive ?? true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const categories = [
    'Fondo de Emergencia',
    'Viaje',
    'Casa',
    'Automóvil',
    'Educación',
    'Jubilación',
    'Inversión',
    'Negocio',
    'Otros'
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {editingGoal ? 'Editar Meta Financiera' : 'Nueva Meta Financiera'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre de la Meta *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ej: Fondo de emergencia"
                required
                name="name" // Added name attribute
                autoComplete="off" // Añadir autocomplete
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Categoría *</Label>
              <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                <SelectTrigger id="category" name="category" autoComplete="off"> {/* Añadir id y autocomplete */}
                  <SelectValue placeholder="Seleccionar categoría" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Monto Objetivo *</Label>
              <NumericInput
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={(value) => setFormData({ ...formData, amount: value })}
                currency={formData.currency}
                showCurrency={true}
                autoComplete="off" // Añadir autocomplete
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currentAmount">Monto Actual</Label>
              <NumericInput
                id="currentAmount"
                name="current_amount"
                value={formData.currentAmount}
                onChange={(value) => setFormData({ ...formData, currentAmount: value })}
                currency={formData.currency}
                showCurrency={true}
                autoComplete="off" // Añadir autocomplete
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="monthlyContribution">Contribución Mensual</Label>
              <NumericInput
                id="monthlyContribution"
                name="monthly_contribution"
                value={formData.monthlyContribution}
                onChange={(value) => setFormData({ ...formData, monthlyContribution: value })}
                currency={formData.currency}
                showCurrency={true}
                autoComplete="off" // Añadir autocomplete
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency-select">Moneda</Label>
              <Select value={formData.currency} onValueChange={(value: 'DOP' | 'USD') => setFormData({ ...formData, currency: value })}>
                <SelectTrigger id="currency-select" name="currency" autoComplete="off"> {/* Añadir id y autocomplete */}
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOP">Pesos (DOP)</SelectItem>
                  <SelectItem value="USD">Dólares (USD)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetDate">Fecha Objetivo</Label>
              <Input
                id="targetDate"
                type="date"
                value={formData.targetDate}
                onChange={(e) => setFormData({ ...formData, targetDate: e.target.value })}
                name="target_date" // Added name attribute
                autoComplete="off" // Añadir autocomplete
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Prioridad</Label>
              <Select value={formData.priority} onValueChange={(value: 'high' | 'medium' | 'low') => setFormData({ ...formData, priority: value })}>
                <SelectTrigger id="priority" name="priority" autoComplete="off"> {/* Añadir id y autocomplete */}
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">Alta</SelectItem>
                  <SelectItem value="medium">Media</SelectItem>
                  <SelectItem value="low">Baja</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1">
              {editingGoal ? 'Actualizar' : 'Crear'} Meta
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
