
import React from 'react';
import { Header } from '@/components/landing/Header';
import { HeroSection } from '@/components/landing/HeroSection';
import { RealTimeStatsSection } from '@/components/landing/RealTimeStatsSection';
import { FeaturesSection } from '@/components/landing/FeaturesSection';
import { BenefitsSection } from '@/components/landing/BenefitsSection';
import { TestimonialsSection } from '@/components/landing/TestimonialsSection';
import { Footer } from '@/components/landing/Footer';
import { usePublicStatistics } from '@/hooks/usePublicStatistics';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { logger } from '@/utils/logger';

export default function IndexPage() {
  logger.debug('IndexPage is rendering with enhanced mobile optimization');
  
  const { statistics, loading, lastUpdate, isConnected } = usePublicStatistics();
  const { isMobile, isTablet, deviceType } = useBreakpoint();

  return (
    <div className={`
      min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50
      ${isMobile ? 'text-sm' : isTablet ? 'text-base' : 'text-base'}
    `} data-device-type={deviceType}>
      <Header 
        isConnected={isConnected} 
        lastUpdate={lastUpdate || undefined}
      />
      
      <HeroSection 
        statistics={statistics} 
        loading={loading} 
        lastUpdate={lastUpdate || undefined}
        isConnected={isConnected}
      />
      
      <RealTimeStatsSection 
        statistics={statistics}
        loading={loading}
        isConnected={isConnected}
      />
      
      <FeaturesSection />
      
      <TestimonialsSection />
      
      <BenefitsSection />
      
      <Footer />
    </div>
  );
}
