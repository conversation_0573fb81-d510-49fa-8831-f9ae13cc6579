
import React, { useState } from 'react';
import { ReimbursementForm } from './ReimbursementForm';
import { ReimbursementsList } from './ReimbursementsList';
import { ReimbursementsSummaryCards } from './ReimbursementsSummaryCards';
import { DeleteConfirmationModal } from './DeleteConfirmationModal';
import { useFinanceData } from '@/hooks/useFinanceData';
import { Reimbursement } from '@/types';

export function ReimbursementsSection() {
  const { 
    reimbursements, 
    addReimbursement, 
    updateReimbursement, 
    deleteReimbursement,
    isAddingReimbursement,
    isUpdatingReimbursement,
    isDeletingReimbursement
  } = useFinanceData(['reimbursements']);
  
  const [showForm, setShowForm] = useState(false);
  const [editingReimbursement, setEditingReimbursement] = useState<Reimbursement | null>(null);
  const [reimbursementToDelete, setReimbursementToDelete] = useState<string | null>(null);

  const handleAddReimbursement = (reimbursementData: Omit<Reimbursement, 'id' | 'createdAt' | 'updatedAt'>) => {
    console.log('ReimbursementsSection - Adding new reimbursement:', reimbursementData);
    addReimbursement(reimbursementData);
    setShowForm(false);
  };

  const handleUpdateReimbursement = (id: string, updates: Partial<Reimbursement>) => {
    console.log('ReimbursementsSection - Updating reimbursement:', id, updates);
    updateReimbursement(id, updates);
    setEditingReimbursement(null);
    setShowForm(false);
  };

  const handleEditReimbursement = (reimbursement: Reimbursement) => {
    console.log('ReimbursementsSection - Edit button clicked for:', reimbursement);
    setEditingReimbursement(reimbursement);
    setShowForm(true);
  };

  const handleCancelEdit = () => {
    console.log('ReimbursementsSection - Cancel edit');
    setShowForm(false);
    setEditingReimbursement(null);
  };

  const handleStatusChange = (id: string, newStatus: Reimbursement['status']) => {
    console.log('ReimbursementsSection - Status change:', id, newStatus);
    updateReimbursement(id, { status: newStatus });
  };

  const handleDeleteReimbursement = (id: string) => {
    setReimbursementToDelete(id);
  };

  const confirmDelete = () => {
    if (reimbursementToDelete) {
      deleteReimbursement(reimbursementToDelete);
      setReimbursementToDelete(null);
    }
  };

  const cancelDelete = () => {
    setReimbursementToDelete(null);
  };

  // Calculate summary data for the cards
  const pendingReimbursements = reimbursements.filter(r => r.status === 'Pendiente');
  const processingReimbursements = reimbursements.filter(r => r.status === 'Procesando');
  const completedReimbursements = reimbursements.filter(r => r.status === 'Completado');

  const pendingCount = pendingReimbursements.length;
  const processingCount = processingReimbursements.length;
  const completedCount = completedReimbursements.length;
  const totalCount = reimbursements.length;

  const pendingAmount = pendingReimbursements.reduce((sum, r) => sum + r.amount, 0);
  const processingAmount = processingReimbursements.reduce((sum, r) => sum + r.amount, 0);
  const completedAmount = completedReimbursements.reduce((sum, r) => sum + r.amount, 0);
  const totalAmount = reimbursements.reduce((sum, r) => sum + r.amount, 0);

  if (showForm) {
    return (
      <ReimbursementForm
        onSubmit={handleAddReimbursement}
        onUpdate={handleUpdateReimbursement}
        onCancel={handleCancelEdit}
        editingReimbursement={editingReimbursement}
        isLoading={isAddingReimbursement || isUpdatingReimbursement}
      />
    );
  }

  return (
    <div className="space-y-6">
      <ReimbursementsSummaryCards 
        pendingCount={pendingCount}
        processingCount={processingCount}
        completedCount={completedCount}
        totalCount={totalCount}
        pendingAmount={pendingAmount}
        processingAmount={processingAmount}
        completedAmount={completedAmount}
        totalAmount={totalAmount}
      />
      <ReimbursementsList 
        items={reimbursements}
        onEdit={handleEditReimbursement}
        onDelete={handleDeleteReimbursement}
        onStatusChange={handleStatusChange}
        isUpdating={isUpdatingReimbursement}
        isDeleting={isDeletingReimbursement}
      />
      <div className="flex justify-end">
        <button
          onClick={() => setShowForm(true)}
          disabled={isAddingReimbursement}
          className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2"
        >
          {isAddingReimbursement && (
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          )}
          Nuevo Reembolso
        </button>
      </div>

      <DeleteConfirmationModal
        isOpen={!!reimbursementToDelete}
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isDeleting={isDeletingReimbursement}
        title="Eliminar Reembolso"
        description="¿Estás seguro de que deseas eliminar este reembolso? Esta acción no se puede deshacer."
      />
    </div>
  );
}
