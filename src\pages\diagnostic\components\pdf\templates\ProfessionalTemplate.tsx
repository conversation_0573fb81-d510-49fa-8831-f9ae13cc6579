
import React from 'react';
import { usePDFDataEngine } from '../core/PDFDataEngine';
import { ExecutiveSummaryPage } from './pages/ExecutiveSummaryPage';
import { FinancialAnalysisPage } from './pages/FinancialAnalysisPage';
import { RecommendationsPage } from './pages/RecommendationsPage';
import { ProjectionsPage } from './pages/ProjectionsPage';
import { createBaseStyle, pdfStyles } from '../styles/pdfStyles';

interface ProfessionalTemplateProps {
  format: 'executive' | 'complete';
}

export const ProfessionalTemplate: React.FC<ProfessionalTemplateProps> = ({ format }) => {
  // const dataEngine = usePDFDataEngine(); // Unused variable

  const containerStyle = createBaseStyle({
    WebkitFontSmoothing: 'antialiased',
    textRendering: 'optimizeLegibility',
    width: pdfStyles.layout.pageWidth,
    margin: '0 auto',
    backgroundColor: '#FFFFFF'
  });

  const pageStyle = {
    pageBreakAfter: 'always' as const,
    minHeight: pdfStyles.layout.pageHeight,
    width: pdfStyles.layout.pageWidth,
    position: 'relative' as const,
    overflow: 'hidden' as const,
    backgroundColor: '#FFFFFF'
  };

  const lastPageStyle = {
    minHeight: pdfStyles.layout.pageHeight,
    width: pdfStyles.layout.pageWidth,
    position: 'relative' as const,
    overflow: 'hidden' as const,
    backgroundColor: '#FFFFFF'
  };

  return (
    <div style={containerStyle}>
      {/* Página 1: Resumen Ejecutivo */}
      <div style={pageStyle}>
        <ExecutiveSummaryPage />
      </div>

      {/* Página 2: Análisis Financiero Detallado */}
      <div style={format === 'complete' ? pageStyle : lastPageStyle}>
        <FinancialAnalysisPage />
      </div>

      {/* Solo en formato completo */}
      {format === 'complete' && (
        <>
          {/* Página 3: Recomendaciones Estratégicas */}
          <div style={pageStyle}>
            <RecommendationsPage />
          </div>

          {/* Página 4: Proyecciones y Metas */}
          <div style={lastPageStyle}>
            <ProjectionsPage />
          </div>
        </>
      )}
    </div>
  );
};
