
import { auditLog } from '@/utils/securityUtils';

// Enhanced error handling that prevents information leakage
export class SecureErrorHandler {
  private static readonly SENSITIVE_PATTERNS = [
    /password/gi,
    /token/gi,
    /key/gi,
    /secret/gi,
    /credential/gi,
    /auth/gi,
    /session/gi,
    /bearer/gi
  ];

  private static readonly ERROR_CODES = {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    AUTHENTICATION_ERROR: 'AUTH_ERROR',
    AUTHORIZATION_ERROR: 'AUTHZ_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    STORAGE_ERROR: 'STORAGE_ERROR',
    ENCRYPTION_ERROR: 'ENCRYPTION_ERROR',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR'
  } as const;

  // Sanitize error messages for client display
  static sanitizeErrorMessage(error: unknown): string {
    let message: string;

    if (typeof error === 'string') {
      message = error;
    } else if (error instanceof Error) {
      message = error.message;
    } else {
      message = 'Ha ocurrido un error inesperado';
    }

    // Remove sensitive information
    this.SENSITIVE_PATTERNS.forEach(pattern => {
      message = message.replace(pattern, '[PROTECTED]');
    });

    // Limit message length
    if (message.length > 200) {
      message = message.substring(0, 200) + '...';
    }

    return message;
  }

  // Log errors securely with context
  static logError(
    error: unknown,
    context: string,
    userId?: string,
    additionalData?: Record<string, any>
  ): void {
    const errorData = {
      context,
      message: this.sanitizeErrorMessage(error),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...additionalData
    };

    // Log to audit system
    auditLog('error_occurred', errorData, userId, 'medium');

    // Console log for development
    if (import.meta.env.DEV) {
      console.error(`[SecureErrorHandler] ${context}:`, error, errorData);
    }
  }

  // Categorize errors for better handling
  static categorizeError(error: unknown): string {
    if (typeof error === 'string') {
      if (error.includes('validation') || error.includes('invalid')) {
        return this.ERROR_CODES.VALIDATION_ERROR;
      }
      if (error.includes('auth') || error.includes('login')) {
        return this.ERROR_CODES.AUTHENTICATION_ERROR;
      }
      if (error.includes('permission') || error.includes('forbidden')) {
        return this.ERROR_CODES.AUTHORIZATION_ERROR;
      }
      if (error.includes('network') || error.includes('fetch')) {
        return this.ERROR_CODES.NETWORK_ERROR;
      }
    }

    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      if (message.includes('validation') || message.includes('schema')) {
        return this.ERROR_CODES.VALIDATION_ERROR;
      }
      if (message.includes('unauthorized') || message.includes('invalid login')) {
        return this.ERROR_CODES.AUTHENTICATION_ERROR;
      }
      if (message.includes('forbidden') || message.includes('access denied')) {
        return this.ERROR_CODES.AUTHORIZATION_ERROR;
      }
      if (message.includes('network') || message.includes('failed to fetch')) {
        return this.ERROR_CODES.NETWORK_ERROR;
      }
      if (message.includes('storage') || message.includes('localStorage')) {
        return this.ERROR_CODES.STORAGE_ERROR;
      }
      if (message.includes('encrypt') || message.includes('decrypt')) {
        return this.ERROR_CODES.ENCRYPTION_ERROR;
      }
    }

    return this.ERROR_CODES.UNKNOWN_ERROR;
  }

  // Handle different types of errors appropriately
  static handleError(
    error: unknown,
    context: string,
    userId?: string,
    showToUser: boolean = true
  ): string {
    const errorCategory = this.categorizeError(error);
    const sanitizedMessage = this.sanitizeErrorMessage(error);
    
    // Log the error
    this.logError(error, context, userId, { category: errorCategory });

    // Return appropriate user message based on category
    switch (errorCategory) {
      case this.ERROR_CODES.VALIDATION_ERROR:
        return 'Por favor verifica los datos ingresados';
      case this.ERROR_CODES.AUTHENTICATION_ERROR:
        return 'Error de autenticación. Por favor inicia sesión nuevamente';
      case this.ERROR_CODES.AUTHORIZATION_ERROR:
        return 'No tienes permisos para realizar esta acción';
      case this.ERROR_CODES.NETWORK_ERROR:
        return 'Error de conexión. Verifica tu internet e intenta nuevamente';
      case this.ERROR_CODES.STORAGE_ERROR:
        return 'Error al acceder al almacenamiento local';
      case this.ERROR_CODES.ENCRYPTION_ERROR:
        return 'Error de seguridad. Por favor contacta soporte';
      default:
        return showToUser ? sanitizedMessage : 'Ha ocurrido un error inesperado';
    }
  }
}

// Global error handler
export const setupGlobalErrorHandler = (): void => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    SecureErrorHandler.logError(
      event.reason,
      'unhandled_promise_rejection',
      undefined,
      { type: 'promise_rejection' }
    );
  });

  // Handle JavaScript errors
  window.addEventListener('error', (event) => {
    SecureErrorHandler.logError(
      event.error || event.message,
      'javascript_error',
      undefined,
      { 
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    );
  });
};
