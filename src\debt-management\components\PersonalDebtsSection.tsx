import React, { useState } from 'react';
import { Card, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, FileText, History, ExternalLink } from 'lucide-react';
import { ROUTES } from '@/constants/routes';
import { useFinanceData } from '@/hooks/useFinanceData';
import { PersonalDebtForm } from './PersonalDebtForm';
import { PersonalDebtPaymentHistory } from './PersonalDebtPaymentHistory';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PersonalDebt } from '@/types';
import { useNavigate } from 'react-router-dom';
import { logger } from '@/utils/logger';

export function PersonalDebtsSection() {
  const navigate = useNavigate();
  const { 
    personalDebts, 
    personalDebtPayments,
    paymentRecords,
    addPersonalDebt, 
    updatePersonalDebt, 
    deletePersonalDebt,
    deletePersonalDebtPayment,
    updatePaymentRecord
  } = useFinanceData();
  
  const [showForm, setShowForm] = useState(false);
  const [editingDebt, setEditingDebt] = useState<PersonalDebt | null>(null);
  const [showPaymentHistory, setShowPaymentHistory] = useState<PersonalDebt | null>(null);

  const handleAddDebt = (debtData: Omit<PersonalDebt, 'id' | 'createdAt' | 'updatedAt'>) => {
    logger.debug('Adding new debt:', debtData);
    addPersonalDebt(debtData);
    setShowForm(false);
    setEditingDebt(null);
  };

  const handleUpdateDebt = (debtData: Omit<PersonalDebt, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingDebt) {
      logger.debug('Updating debt with ID:', editingDebt.id, 'Data:', debtData);
      
      // Pasar directamente los datos del formulario, ya que incluyen todos los campos necesarios
      updatePersonalDebt(editingDebt.id, debtData);
      setEditingDebt(null);
      setShowForm(false);
    } else {
      logger.error('No debt selected for editing');
    }
  };

  const handleDeleteDebt = (id: string) => {
    logger.debug('Deleting debt with ID:', id);
    deletePersonalDebt(id);
  };

  const handleEditDebt = (debt: PersonalDebt) => {
    logger.debug('Editing debt:', debt);
    setEditingDebt(debt);
    setShowForm(true);
  };

  const handleCancelEdit = () => {
    logger.debug('Cancelling edit');
    setShowForm(false);
    setEditingDebt(null);
  };

  const handleDeletePayment = (paymentId: string) => {
    // Find the payment to get details for cleanup
    const payment = personalDebtPayments?.find(p => p.id === paymentId);
    if (payment) {
      const debt = personalDebts.find(d => d.id === payment.personalDebtId);
      if (debt) {
        // Recalculate remaining balance by adding back the principal amount
        const currentBalance = debt.remainingBalance || 0;
        const newBalance = currentBalance + payment.principalAmount;
        updatePersonalDebt(debt.id, { remainingBalance: newBalance });
        
        // Remove corresponding payment record if it exists
        const correspondingRecord = paymentRecords?.find(
          r => r.paymentType === 'personal-debt' && 
               r.referenceId === debt.id && 
               r.paidDate === payment.paymentDate &&
               r.amount === payment.totalAmount
        );
        if (correspondingRecord) {
          // Update the payment record to mark as cancelled or pending
          updatePaymentRecord(correspondingRecord.id, { 
            status: 'pending', 
            paidDate: undefined,
            notes: `Pago cancelado - ${correspondingRecord.notes || ''}`.trim()
          });
        }
      }
    }
    
    deletePersonalDebtPayment(paymentId);
  };

  const handleGoToPayments = () => {
    navigate(ROUTES.APP_PAYMENTS);
  };

  // Si se está mostrando el historial de pagos
  if (showPaymentHistory) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            onClick={() => setShowPaymentHistory(null)}
          >
            ← Volver
          </Button>
          <h2 className="text-2xl font-bold">Historial de Abonos</h2>
        </div>
        <PersonalDebtPaymentHistory
          debt={showPaymentHistory}
          payments={personalDebtPayments || []}
          onDeletePayment={handleDeletePayment}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Deudas Personales</h2>
        <Button onClick={() => setShowForm(true)} className="gap-2">
          <Plus className="w-4 h-4" />
          Agregar Deuda
        </Button>
      </div>

      {personalDebts.length > 0 && (
        <Alert>
          <ExternalLink className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              Para realizar abonos o pagos a tus deudas personales, ve al apartado de <strong>Seguimiento de Pagos</strong>.
            </span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleGoToPayments}
              className="ml-4 gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              Ir a Pagos
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {showForm && (
        <PersonalDebtForm
          onSubmit={editingDebt ? handleUpdateDebt : handleAddDebt}
          onCancel={handleCancelEdit}
          editingDebt={editingDebt}
        />
      )}

      {personalDebts.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <FileText className="w-10 h-10 text-finanz-text-secondary mx-auto mb-4" />
            <p className="text-finanz-text-secondary">No tienes deudas personales registradas.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {personalDebts.map((debt) => {
            const remainingBalance = debt.remainingBalance !== undefined ? debt.remainingBalance : debt.amount;
            const progressPercentage = debt.amount > 0 ? ((debt.amount - remainingBalance) / debt.amount) * 100 : 0;
            const debtPayments = personalDebtPayments?.filter(p => p.personalDebtId === debt.id) || [];
            const isPaidOff = remainingBalance <= 0;
            
            // Calcular meses estimados basado en el presupuesto mensual
            const estimatedMonths = debt.monthlyBudget && debt.monthlyBudget > 0 && remainingBalance > 0
              ? Math.ceil(remainingBalance / debt.monthlyBudget)
              : null;
            
            return (
              <Card key={debt.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-finanz-neutral/10 rounded-lg">
                          <FileText className="w-4 h-4" />
                        </div>
                        <div>
                          <h4 className="font-medium text-finanz-text-primary">{debt.name}</h4>
                          <p className="text-sm text-finanz-text-secondary">
                            Próximo abono: {new Date(debt.paymentDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                        <div>
                          <span className="text-finanz-text-secondary">Monto Original:</span>
                          <div className="font-semibold">{formatCurrency(debt.amount, debt.currency)}</div>
                        </div>
                        <div>
                          <span className="text-finanz-text-secondary">Saldo Restante:</span>
                          <div className={`font-semibold ${isPaidOff ? 'text-finanz-success' : 'text-finanz-danger'}`}>
                            {formatCurrency(remainingBalance, debt.currency)}
                          </div>
                        </div>
                        <div>
                          <span className="text-finanz-text-secondary">Presupuesto Mensual:</span>
                          <div className="font-semibold text-finanz-primary">
                            {debt.monthlyBudget ? formatCurrency(debt.monthlyBudget, debt.currency) : 'No definido'}
                          </div>
                        </div>
                        <div>
                          <span className="text-finanz-text-secondary">Progreso:</span>
                          <div className="font-semibold text-finanz-primary">{progressPercentage.toFixed(1)}%</div>
                        </div>
                        <div>
                          <span className="text-finanz-text-secondary">Abonos:</span>
                          <div className="font-semibold">{debtPayments.length}</div>
                        </div>
                      </div>

                      {/* Información del tiempo estimado */}
                      {estimatedMonths && (
                        <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                          <span className="text-blue-600">
                            Tiempo estimado para liquidar: <strong>{estimatedMonths} {estimatedMonths === 1 ? 'mes' : 'meses'}</strong>
                          </span>
                        </div>
                      )}

                      {progressPercentage > 0 && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full transition-all duration-300 ${
                                isPaidOff ? 'bg-finanz-success' : 'bg-finanz-primary'
                              }`}
                              style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="text-right ml-4">
                      <div className="flex items-center gap-2 mb-2">
                        {!debt.isActive && (
                          <Badge variant="outline">Inactiva</Badge>
                        )}
                        {isPaidOff && (
                          <Badge className="bg-green-100 text-green-800">Pagada</Badge>
                        )}
                      </div>
                      <div className="flex flex-col gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowPaymentHistory(debt)}
                          className="gap-2"
                        >
                          <History className="w-4 h-4" />
                          Ver Historial
                        </Button>
                        <div className="flex gap-2">
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => handleEditDebt(debt)}
                          >
                            Editar
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteDebt(debt.id)}
                          >
                            Eliminar
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
