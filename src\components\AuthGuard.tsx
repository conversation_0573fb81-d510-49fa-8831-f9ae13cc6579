
import { logger } from "@/utils/logger";

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { AUTH_GUARD_DELAY } from '@/constants/config';
import { ROUTES } from '@/constants/routes';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { user, loading, session } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;
    
    // Don't do anything while loading
    if (loading) return;

    // Public routes
    const publicRoutes = [ROUTES.ROOT, ROUTES.AUTH, ROUTES.FEATURES];
    const isPublicRoute = publicRoutes.includes(location.pathname);

    // Allow access to public routes
    if (isPublicRoute) return;

    // For protected routes, check auth state with a longer delay
    if (location.pathname.startsWith(ROUTES.APP)) {
      const checkAuth = setTimeout(() => {
        if (!user || !session) {
          logger.debug('AuthGuard: Redirecting to auth - no user or session');
          navigate(ROUTES.AUTH, { replace: true });
        }
      }, AUTH_GUARD_DELAY); // Wait configured time before redirecting

      return () => clearTimeout(checkAuth);
    }
  }, [user, session, loading, navigate, location.pathname, mounted]);

  return <>{children}</>;
};
