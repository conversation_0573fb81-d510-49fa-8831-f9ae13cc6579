
import React from 'react';
import { usePDFDataEngine } from '../../core/PDFDataEngine';
import { TrendIcon } from '../../components/TrendIcon';
import { createPageStyle, createHeaderStyle, createTableStyle, createTableCellStyle, createBaseStyle, pdfStyles } from '../../styles/pdfStyles';

export const FinancialAnalysisPage: React.FC = () => {
  const { 
    expenseBreakdown, 
    debtProfile, 
    monthlyTrends, 
    formatCurrency,
    getStatusBadge,
    financialAlerts
  } = usePDFDataEngine();

  return (
    <div style={createPageStyle()}>
      {/* Header */}
      <div style={createHeaderStyle({ textAlign: 'center' })}>
        <h1 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize['3xl'], 
          fontWeight: '700',
          margin: '0 0 6px 0'
        })}>
          Aná<PERSON><PERSON> Financiero Detallado
        </h1>
        <p style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.lg,
          color: pdfStyles.colors.neutral[500],
          margin: 0
        })}>
          Desglose completo de ingresos, gastos y tendencias financieras
        </p>
      </div>

      {/* Distribución de Gastos */}
      <div style={{ marginBottom: pdfStyles.spacing['4xl'] }}>
        <h2 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center',
          borderBottom: `2px solid ${pdfStyles.colors.primary}`,
          paddingBottom: '6px'
        })}>
          📊 Distribución de Gastos por Categoría
        </h2>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: pdfStyles.spacing['2xl'] }}>
          {/* Tabla de gastos */}
          <div>
            <table style={createTableStyle()}>
              <thead>
                <tr style={{ backgroundColor: pdfStyles.colors.neutral[100] }}>
                  <th style={createTableCellStyle('left')}>Categoría</th>
                  <th style={createTableCellStyle('right')}>Monto</th>
                  <th style={createTableCellStyle('center')}>%</th>
                  <th style={createTableCellStyle('center')}>Tendencia</th>
                </tr>
              </thead>
              <tbody>
                {expenseBreakdown.map((expense, index) => (
                  <tr key={index}>
                    <td style={createTableCellStyle('left')}>
                      {expense.category}
                    </td>
                    <td style={createTableCellStyle('right')}>
                      {formatCurrency(expense.amount)}
                    </td>
                    <td style={createTableCellStyle('center')}>
                      {expense.percentage}%
                    </td>
                    <td style={createTableCellStyle('center')}>
                      <TrendIcon trend={expense.trend} size={14} />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Representación visual */}
          <div>
            <div style={createBaseStyle({ 
              marginBottom: '10px', 
              fontWeight: '600',
              textAlign: 'center'
            })}>
              Representación Visual
            </div>
            {expenseBreakdown.slice(0, 6).map((expense, index) => (
              <div key={index} style={{ marginBottom: '8px' }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  fontSize: pdfStyles.typography.fontSize.xs,
                  marginBottom: '3px'
                }}>
                  <span>{expense.category}</span>
                  <span style={{ fontWeight: '600' }}>{expense.percentage}%</span>
                </div>
                <div style={{
                  width: '100%',
                  height: '10px',
                  backgroundColor: pdfStyles.colors.neutral[200],
                  borderRadius: '5px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${expense.percentage}%`,
                    height: '100%',
                    backgroundColor: index % 3 === 0 ? pdfStyles.colors.primary : 
                                   index % 3 === 1 ? pdfStyles.colors.success : pdfStyles.colors.warning,
                    borderRadius: '5px'
                  }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Perfil de Deudas */}
      <div style={{ marginBottom: pdfStyles.spacing['4xl'] }}>
        <h2 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center',
          borderBottom: `2px solid ${pdfStyles.colors.primary}`,
          paddingBottom: '6px'
        })}>
          💳 Perfil de Endeudamiento
        </h2>
        
        <table style={createTableStyle()}>
          <thead>
            <tr style={{ backgroundColor: pdfStyles.colors.neutral[100] }}>
              <th style={createTableCellStyle('left')}>Tipo de Deuda</th>
              <th style={createTableCellStyle('right')}>Saldo</th>
              <th style={createTableCellStyle('right')}>Pago Mensual</th>
              <th style={createTableCellStyle('center')}>Tasa</th>
              <th style={createTableCellStyle('center')}>Estado</th>
            </tr>
          </thead>
          <tbody>
            {debtProfile.map((debt, index) => {
              const statusBadge = getStatusBadge(debt.status);
              return (
                <tr key={index}>
                  <td style={createTableCellStyle('left')}>
                    {debt.type}
                  </td>
                  <td style={createTableCellStyle('right')}>
                    {formatCurrency(debt.balance)}
                  </td>
                  <td style={createTableCellStyle('right')}>
                    {formatCurrency(debt.payment)}
                  </td>
                  <td style={createTableCellStyle('center')}>
                    {debt.rate}%
                  </td>
                  <td style={createTableCellStyle('center')}>
                    <span style={{
                      backgroundColor: statusBadge.color,
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: pdfStyles.typography.fontSize.xs,
                      fontWeight: '600'
                    }}>
                      {statusBadge.text}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Tendencias Mensuales */}
      <div style={{ marginBottom: pdfStyles.spacing['2xl'] }}>
        <h2 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center',
          borderBottom: `2px solid ${pdfStyles.colors.primary}`,
          paddingBottom: '6px'
        })}>
          📈 Tendencias de los Últimos 6 Meses
        </h2>
        
        <table style={createTableStyle()}>
          <thead>
            <tr style={{ backgroundColor: pdfStyles.colors.neutral[100] }}>
              <th style={createTableCellStyle('left')}>Mes</th>
              <th style={createTableCellStyle('right')}>Ingresos</th>
              <th style={createTableCellStyle('right')}>Gastos</th>
              <th style={createTableCellStyle('right')}>Balance</th>
              <th style={createTableCellStyle('center')}>Ahorro %</th>
            </tr>
          </thead>
          <tbody>
            {monthlyTrends.map((month, index) => {
              const savingsRate = ((month.income - month.expenses) / month.income * 100);
              return (
                <tr key={index}>
                  <td style={createTableCellStyle('left')}>
                    {month.month}
                  </td>
                  <td style={createTableCellStyle('right')}>
                    {formatCurrency(month.income)}
                  </td>
                  <td style={createTableCellStyle('right')}>
                    {formatCurrency(month.expenses)}
                  </td>
                  <td style={createTableCellStyle('right')}>
                    {formatCurrency(month.balance)}
                  </td>
                  <td style={createTableCellStyle('center')}>
                    {savingsRate.toFixed(1)}%
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Alertas Dinámicas */}
      <div style={{
        backgroundColor: '#FEF2F2',
        border: `2px solid ${pdfStyles.colors.danger}`,
        borderRadius: '10px',
        padding: pdfStyles.spacing.lg
      }}>
        <h3 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.lg, 
          fontWeight: '700', 
          color: '#991B1B',
          marginBottom: '10px',
          textAlign: 'center'
        })}>
          ⚠️ Alertas y Observaciones Importantes
        </h3>
        
        <div style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.sm, color: '#7F1D1D', lineHeight: pdfStyles.typography.lineHeight.relaxed })}>
          {financialAlerts.slice(0, 3).map((alert, index) => (
            <div key={index} style={{ marginBottom: '8px' }}>
              • <strong>{alert.category}:</strong> {alert.message} {alert.recommendation}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
