
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { usePaymentsLogic } from '@/seguimiento_pagos/hooks/usePaymentsLogic';
import { Calendar, CreditCard, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { PaymentItem } from '@/seguimiento_pagos/types/paymentTypes';

export const ComprehensivePaymentsView: React.FC = () => {
  const { payments, totals, allPayments } = usePaymentsLogic();
  const { isMobile } = useBreakpoint();

  const formatCurrency = (amount: number, currency: 'DOP' | 'USD' = 'DOP') => {
    const symbol = currency === 'USD' ? '$' : 'RD$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'overdue': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <Calendar className="w-4 h-4 text-blue-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-50';
      case 'overdue': return 'text-red-600 bg-red-50';
      default: return 'text-blue-600 bg-blue-50';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle
          className={`flex items-center gap-2 ${isMobile ? 'text-lg' : 'text-xl'}`}
        >
          <CreditCard className={`${isMobile ? 'w-6 h-6' : 'w-5 h-5'}`} />
          Vista Comprehensiva de Pagos
        </CardTitle>
        <CardDescription>
          Gestión completa de tus pagos programados y tendencias
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="payments">Pagos</TabsTrigger>
            <TabsTrigger value="trends">Tendencias</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pagos Pendientes</p>
                      <p className={`font-bold text-blue-600 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
                        {formatCurrency(totals.pending)}
                      </p>
                      <p className="text-xs text-gray-500">{payments.pending.length} pagos</p>
                    </div>
                    <Calendar className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} text-blue-500`} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pagos Vencidos</p>
                      <p className={`font-bold text-red-600 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
                        {formatCurrency(totals.overdue)}
                      </p>
                      <p className="text-xs text-gray-500">{payments.overdue.length} pagos</p>
                    </div>
                    <AlertTriangle className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} text-red-500`} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pagados este Mes</p>
                      <p className={`font-bold text-green-600 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
                        {formatCurrency(totals.paidThisMonth)}
                      </p>
                      <p className="text-xs text-gray-500">{payments.paid.length} pagos</p>
                    </div>
                    <CheckCircle className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} text-green-500`} />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="payments" className="space-y-4">
            <div className="space-y-4">
              {/* Pending Payments */}
              {payments.pending.length > 0 && (
                <div>
                  <h4 className="font-semibold text-blue-600 mb-3">Pendientes</h4>
                  <div className="space-y-2">
                    {payments.pending.slice(0, 5).map((payment: PaymentItem) => (
                      <div key={payment.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(payment.status)}
                          <div>
                            <p className="font-medium">{payment.name}</p>
                            <p className="text-sm text-gray-600">
                              Vence: {new Date(payment.dueDate).toLocaleDateString('es-ES')}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{formatCurrency(payment.amount, payment.currency)}</p>
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(payment.status)}`}>
                            Pendiente
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Overdue Payments */}
              {payments.overdue.length > 0 && (
                <div>
                  <h4 className="font-semibold text-red-600 mb-3">Vencidos</h4>
                  <div className="space-y-2">
                    {payments.overdue.slice(0, 3).map((payment: PaymentItem) => (
                      <div key={payment.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(payment.status)}
                          <div>
                            <p className="font-medium">{payment.name}</p>
                            <p className="text-sm text-gray-600">
                              Venció: {new Date(payment.dueDate).toLocaleDateString('es-ES')}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{formatCurrency(payment.amount, payment.currency)}</p>
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(payment.status)}`}>
                            Vencido
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <div className="text-center py-8">
              <TrendingUp className={`${isMobile ? 'w-6 h-6' : 'w-12 h-12'} mx-auto text-gray-400 mb-4`} />
              <p className="text-gray-600">Análisis de tendencias de pagos integrado</p>
              <p className="text-sm text-gray-500">Esta funcionalidad se combina con el análisis financiero principal</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
