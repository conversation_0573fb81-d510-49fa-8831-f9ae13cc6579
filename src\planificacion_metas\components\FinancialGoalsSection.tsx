import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFinanceData } from '@/hooks/useFinanceData';
import { FinancialGoalForm } from './FinancialGoalForm';
import { AddContributionModal } from './AddContributionModal';
import { ContributionHistory } from './ContributionHistory';
import { GoalsList } from './GoalsList';
import { GoalsSummaryCards } from './GoalsSummaryCards';
import { filterGoals, getFilteredGoalCounts } from '../utils/goalFilters';
import { FinancialGoal, GoalContribution } from '@/types';

export function FinancialGoalsSection() {
  const { 
    financialGoals, 
    addFinancialGoal, 
    updateFinancialGoal, 
    deleteFinancialGoal,
    goalContributions,
    addGoalContribution
  } = useFinanceData();

  const [showForm, setShowForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<FinancialGoal | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [contributionModal, setContributionModal] = useState<{ goal: FinancialGoal | null; isOpen: boolean }>({
    goal: null,
    isOpen: false
  });
  const [historyModal, setHistoryModal] = useState<{ goal: FinancialGoal | null; isOpen: boolean }>({
    goal: null,
    isOpen: false
  });

  const getContributionsForGoal = (goalId: string): GoalContribution[] => {
    return goalContributions.filter(contrib => contrib.goal_id === goalId);
  };

  const handleAddGoal = (goalData: Omit<FinancialGoal, 'id' | 'createdAt' | 'updatedAt'>) => {
    addFinancialGoal(goalData);
    setShowForm(false);
    setEditingGoal(null);
  };

  const handleEditGoal = (goal: FinancialGoal) => {
    setEditingGoal(goal);
    setShowForm(true);
  };

  const handleUpdateGoal = (goalData: Omit<FinancialGoal, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingGoal) {
      updateFinancialGoal(editingGoal.id, goalData);
      setShowForm(false);
      setEditingGoal(null);
    }
  };

  const handleDeleteGoal = (goalId: string) => {
    if (confirm('¿Estás seguro de que quieres eliminar esta meta? Esta acción no se puede deshacer.')) {
      deleteFinancialGoal(goalId);
    }
  };

  const handleAddContribution = (goalId: string, amount: number, note?: string) => {
    addGoalContribution(goalId, amount, note);
  };

  const counts = getFilteredGoalCounts(financialGoals);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Metas Financieras</h2>
        <Button onClick={() => setShowForm(true)} className="gap-2">
          <Plus className="w-4 h-4" />
          Nueva Meta
        </Button>
      </div>

      {showForm && (
        <FinancialGoalForm
          onSubmit={editingGoal ? handleUpdateGoal : handleAddGoal}
          onCancel={() => {
            setShowForm(false);
            setEditingGoal(null);
          }}
          editingGoal={editingGoal}
        />
      )}

      <GoalsSummaryCards financialGoals={financialGoals} />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">Todas ({counts.total})</TabsTrigger>
          <TabsTrigger value="completed">Completadas ({counts.completed})</TabsTrigger>
          <TabsTrigger value="in-progress">En Progreso ({counts.inProgress})</TabsTrigger>
          <TabsTrigger value="not-started">Sin Iniciar ({counts.notStarted})</TabsTrigger>
          <TabsTrigger value="high-priority">Prioridad Alta ({counts.highPriority})</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <GoalsList 
            goals={filterGoals(financialGoals, 'all')} 
            getContributionsForGoal={getContributionsForGoal}
            onEdit={handleEditGoal}
            onDelete={handleDeleteGoal}
            onAddContribution={(goal) => setContributionModal({ goal, isOpen: true })}
            onShowHistory={(goal) => setHistoryModal({ goal, isOpen: true })}
          />
        </TabsContent>

        <TabsContent value="completed">
          <GoalsList 
            goals={filterGoals(financialGoals, 'completed')} 
            getContributionsForGoal={getContributionsForGoal}
            onEdit={handleEditGoal}
            onDelete={handleDeleteGoal}
            onAddContribution={(goal) => setContributionModal({ goal, isOpen: true })}
            onShowHistory={(goal) => setHistoryModal({ goal, isOpen: true })}
          />
        </TabsContent>

        <TabsContent value="in-progress">
          <GoalsList 
            goals={filterGoals(financialGoals, 'in-progress')} 
            getContributionsForGoal={getContributionsForGoal}
            onEdit={handleEditGoal}
            onDelete={handleDeleteGoal}
            onAddContribution={(goal) => setContributionModal({ goal, isOpen: true })}
            onShowHistory={(goal) => setHistoryModal({ goal, isOpen: true })}
          />
        </TabsContent>

        <TabsContent value="not-started">
          <GoalsList 
            goals={filterGoals(financialGoals, 'not-started')} 
            getContributionsForGoal={getContributionsForGoal}
            onEdit={handleEditGoal}
            onDelete={handleDeleteGoal}
            onAddContribution={(goal) => setContributionModal({ goal, isOpen: true })}
            onShowHistory={(goal) => setHistoryModal({ goal, isOpen: true })}
          />
        </TabsContent>

        <TabsContent value="high-priority">
          <GoalsList 
            goals={filterGoals(financialGoals, 'high-priority')} 
            getContributionsForGoal={getContributionsForGoal}
            onEdit={handleEditGoal}
            onDelete={handleDeleteGoal}
            onAddContribution={(goal) => setContributionModal({ goal, isOpen: true })}
            onShowHistory={(goal) => setHistoryModal({ goal, isOpen: true })}
          />
        </TabsContent>
      </Tabs>

      {contributionModal.goal && (
        <AddContributionModal
          goal={contributionModal.goal}
          isOpen={contributionModal.isOpen}
          onClose={() => setContributionModal({ goal: null, isOpen: false })}
          onAddContribution={handleAddContribution}
        />
      )}

      {historyModal.goal && (
        <ContributionHistory
          goal={historyModal.goal}
          contributions={getContributionsForGoal(historyModal.goal.id)}
          isOpen={historyModal.isOpen}
          onClose={() => setHistoryModal({ goal: null, isOpen: false })}
        />
      )}
    </div>
  );
}
