
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { AppearanceFormData } from '../../schemas/settingsSchemas';

interface LanguageSelectorProps {
  appearanceForm: UseFormReturn<AppearanceFormData>;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  appearanceForm,
}) => {
  return (
    <FormField
      control={appearanceForm.control}
      name="language"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Idioma</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona un idioma" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="es">Español</SelectItem>
              <SelectItem value="en">English</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
