
-- FASE 1: CORRECCIÓN DE LA BASE DE DATOS
-- El problema principal es que la regla de unicidad actual no distingue entre pagos
-- recurrentes del mismo servicio (ej. Netflix de Enero vs. Netflix de Febrero).
-- Esta corrección arregla eso.

-- Primero, eliminamos la restricción incorrecta que solo considera el tipo y la referencia.
ALTER TABLE public.payment_records
DROP CONSTRAINT IF EXISTS payment_records_unique_payment_key;

-- <PERSON><PERSON>, creamos la restricción correcta. Ahora, un pago es único si la combinación de
-- usuario, tipo, referencia Y fecha de vencimiento es única.
-- Esto permite tener múltiples registros para una misma suscripción, pero en diferentes fechas.
ALTER TABLE public.payment_records
ADD CONSTRAINT payment_records_unique_payment_key UNIQUE (user_id, payment_type, reference_id, due_date);
