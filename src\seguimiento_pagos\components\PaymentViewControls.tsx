import React from 'react';
import { Calendar, TrendingUp, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { PaymentFilters } from './PaymentFilters';
import type { PaymentFilterValues } from './PaymentFilters';
import type { PaymentItem } from '../types/paymentTypes';

interface PaymentViewControlsProps {
  viewMode: 'list' | 'calendar' | 'trends';
  onViewModeChange: (mode: 'list' | 'calendar' | 'trends') => void;
  onFiltersChange: (filters: PaymentFilterValues) => void;
  totalPayments: number;
  filteredCount: number;
  allPaymentsList: PaymentItem[]; // Needed for PaymentFilters if it doesn't fetch its own
}

export function PaymentViewControls({
  viewMode,
  onViewModeChange,
  onFiltersChange,
  totalPayments,
  filteredCount,
}: PaymentViewControlsProps) {
  return (
    <div className="flex items-center justify-between">
      {/* Filtros de pagos */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="gap-1">
            <Filter className="w-4 h-4" />
            Filtros
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 w-[90vw] max-w-md">
          <PaymentFilters
            onFiltersChange={onFiltersChange}
            totalPayments={totalPayments}
            filteredCount={filteredCount}
          />
        </PopoverContent>
      </Popover>

      {/* Selector de vista */}
      <div className="flex items-center gap-2">
        <Button
          variant={viewMode === 'list' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('list')}
        >
          Lista
        </Button>
        <Button
          variant={viewMode === 'calendar' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('calendar')}
        >
          <Calendar className="w-4 h-4 mr-1" />
          Calendario
        </Button>
        <Button
          variant={viewMode === 'trends' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onViewModeChange('trends')}
        >
          <TrendingUp className="w-4 h-4 mr-1" />
          Tendencias
        </Button>
      </div>
    </div>
  );
}
