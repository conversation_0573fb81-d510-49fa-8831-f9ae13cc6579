import { logger } from "@/utils/logger";
import { useMemo } from 'react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useIncomeData } from './useIncomeData';
import { useExpenseData } from './useExpenseData';
import { useDebtData } from './useDebtData';
import { useOtherData } from './useOtherData';
import { usePaymentRecordsData } from './usePaymentRecordsData';
import { useFinancialCalculations } from '@/nucleo_dominio_compartido/hooks/useFinancialCalculations';
import { auditLog } from '@/utils/securityUtils';
import { useAuth } from '@/contexts/AuthContext';
import { QUERY_KEYS } from '@/constants/queryKeys';

import type { CalculationFilters } from '@/nucleo_dominio_compartido/hooks/useFinancialCalculations';

export const useFinanceData = (
  dataTypes?: (
    | 'incomes'
    | 'expenses'
    | 'debts'
    | 'subscriptions'
    | 'reimbursements'
    | 'goals'
    | 'payments'
    | 'contributions'
  )[],
  filters: CalculationFilters = {}
) => {
  logger.debug('useFinanceData: Initializing with selective data loading...', dataTypes);

  const { user } = useAuth();
  const queryClient = useQueryClient();

  const shouldLoadIncomes = !dataTypes || dataTypes.includes('incomes');
  const shouldLoadExpenses = !dataTypes || dataTypes.includes('expenses');
  const shouldLoadDebts = !dataTypes || dataTypes.includes('debts');
  const shouldLoadOther =
    !dataTypes || dataTypes.some(type => ['subscriptions', 'reimbursements', 'goals', 'contributions'].includes(type));
  const shouldLoadPayments = !dataTypes || dataTypes.includes('payments');

  const incomeData = useIncomeData({ enabled: shouldLoadIncomes });
  const expenseData = useExpenseData({ enabled: shouldLoadExpenses });
  const debtData = useDebtData({ enabled: shouldLoadDebts });
  const otherData = useOtherData({ enabled: shouldLoadOther });
  const paymentRecordsData = usePaymentRecordsData({ enabled: shouldLoadPayments });

  const applyCreditCardPaymentMutation = useMutation({
    mutationFn: async ({ cardId, amount, paidDate, notes, currency, dueDate }: {
      cardId: string;
      amount: number;
      paidDate: Date;
      notes?: string;
      currency: 'DOP' | 'USD';
      dueDate: string;
    }) => {
      if (!user?.id) throw new Error('User not authenticated');

      auditLog('credit_card_payment_attempt', { userId: user.id, cardId });
      const { error } = await supabase.rpc('apply_credit_card_payment', {
        p_card_id: cardId,
        p_user_id: user.id,
        p_amount: amount,
        p_paid_date: paidDate.toISOString().split('T')[0],
        p_due_date: new Date(dueDate).toISOString().split('T')[0],
        p_notes: notes,
        p_currency: currency,
      });

      if (error) {
        auditLog('credit_card_payment_error', { userId: user.id, cardId, error: error.message });
        throw error;
      }
      return true;
    },
    onSuccess: (_, variables) => {
      auditLog('credit_card_payment_success', { userId: user?.id, cardId: variables.cardId });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.CREDIT_CARDS, user?.id] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user?.id] });
    },
  });

  const calculationFunctions = useFinancialCalculations(
    incomeData.incomes || [],
    expenseData.expenses || [],
    debtData.creditCards || [],
    debtData.loans || [],
    debtData.personalDebts || [],
    otherData.reimbursements || [],
    filters
  );

  const memoizedData = useMemo(
    () => ({
      incomes: incomeData.incomes || [],
      isLoadingIncomes: incomeData.isLoading,
      errorIncomes: incomeData.error,
      isAddingIncome: incomeData.isAddingIncome,
      isUpdatingIncome: incomeData.isUpdatingIncome,
      isDeletingIncome: incomeData.isDeletingIncome,

      expenses: expenseData.expenses || [],
      isAddingExpense: expenseData.isAddingExpense,
      isUpdatingExpense: expenseData.isUpdatingExpense,
      isDeletingExpense: expenseData.isDeletingExpense,

      creditCards: debtData.creditCards || [],
      loans: debtData.loans || [],
      personalDebts: debtData.personalDebts || [],
      personalDebtPayments: debtData.personalDebtPayments || [],
      isAddingCreditCard: debtData.isAddingCreditCard,
      isUpdatingCreditCard: debtData.isUpdatingCreditCard,
      isDeletingCreditCard: debtData.isDeletingCreditCard,
      isAddingLoan: debtData.isAddingLoan,
      isUpdatingLoan: debtData.isUpdatingLoan,
      isDeletingLoan: debtData.isDeletingLoan,
      isAddingPersonalDebt: debtData.isAddingPersonalDebt,
      isUpdatingPersonalDebt: debtData.isUpdatingPersonalDebt,
      isDeletingPersonalDebt: debtData.isDeletingPersonalDebt,
      isAddingPersonalDebtPayment: debtData.isAddingPersonalDebtPayment,
      isUpdatingPersonalDebtPayment: debtData.isUpdatingPersonalDebtPayment,
      isDeletingPersonalDebtPayment: debtData.isDeletingPersonalDebtPayment,

      subscriptions: otherData.subscriptions || [],
      reimbursements: otherData.reimbursements || [],
      financialGoals: otherData.financialGoals || [],
      goalContributions: otherData.goalContributions || [],
      defaultCurrency: otherData.defaultCurrency,
      isAddingReimbursement: otherData.isAddingReimbursement,
      isUpdatingReimbursement: otherData.isUpdatingReimbursement,
      isDeletingReimbursement: otherData.isDeletingReimbursement,

      paymentRecords: paymentRecordsData.paymentRecords || [],
      isApplyingCreditCardPayment: applyCreditCardPaymentMutation.isPending,

      isLoading:
        incomeData.isLoading ||
        expenseData.isLoading ||
        debtData.isLoading ||
        otherData.isLoading ||
        paymentRecordsData.isLoading,
      ...calculationFunctions,
    }),
    [
      incomeData.incomes,
      incomeData.isLoading,
      incomeData.error,
      incomeData.isAddingIncome,
      incomeData.isUpdatingIncome,
      incomeData.isDeletingIncome,
      expenseData.expenses,
      expenseData.isAddingExpense,
      expenseData.isUpdatingExpense,
      expenseData.isDeletingExpense,
      debtData.creditCards,
      debtData.loans,
      debtData.personalDebts,
      debtData.personalDebtPayments,
      debtData.isAddingCreditCard,
      debtData.isUpdatingCreditCard,
      debtData.isDeletingCreditCard,
      debtData.isAddingLoan,
      debtData.isUpdatingLoan,
      debtData.isDeletingLoan,
      debtData.isAddingPersonalDebt,
      debtData.isUpdatingPersonalDebt,
      debtData.isDeletingPersonalDebt,
      debtData.isAddingPersonalDebtPayment,
      debtData.isUpdatingPersonalDebtPayment,
      debtData.isDeletingPersonalDebtPayment,
      otherData.subscriptions,
      otherData.reimbursements,
      otherData.financialGoals,
      otherData.goalContributions,
      otherData.defaultCurrency,
      otherData.isAddingReimbursement,
      otherData.isUpdatingReimbursement,
      otherData.isDeletingReimbursement,
      paymentRecordsData.paymentRecords,
      applyCreditCardPaymentMutation.isPending,
      calculationFunctions,
      expenseData.isLoading,
      debtData.isLoading,
      otherData.isLoading,
      paymentRecordsData.isLoading,
    ]
  );

  const actions = useMemo(
    () => ({
      addIncome: incomeData.addIncome,
      updateIncome: incomeData.updateIncome,
      deleteIncome: incomeData.deleteIncome,
      addExpense: expenseData.addExpense,
      updateExpense: expenseData.updateExpense,
      deleteExpense: expenseData.deleteExpense,
      addCreditCard: debtData.addCreditCard,
      updateCreditCard: debtData.updateCreditCard,
      deleteCreditCard: debtData.deleteCreditCard,
      addLoan: debtData.addLoan,
      updateLoan: debtData.updateLoan,
      deleteLoan: debtData.deleteLoan,
      addPersonalDebt: debtData.addPersonalDebt,
      updatePersonalDebt: debtData.updatePersonalDebt,
      deletePersonalDebt: debtData.deletePersonalDebt,
      addPersonalDebtPayment: debtData.addPersonalDebtPayment,
      updatePersonalDebtPayment: debtData.updatePersonalDebtPayment,
      deletePersonalDebtPayment: debtData.deletePersonalDebtPayment,
      addSubscription: otherData.addSubscription,
      updateSubscription: otherData.updateSubscription,
      deleteSubscription: otherData.deleteSubscription,
      toggleSubscriptionStatus: otherData.toggleSubscriptionStatus,
      addReimbursement: otherData.addReimbursement,
      updateReimbursement: otherData.updateReimbursement,
      deleteReimbursement: otherData.deleteReimbursement,
      addFinancialGoal: otherData.addFinancialGoal,
      updateFinancialGoal: otherData.updateFinancialGoal,
      deleteFinancialGoal: otherData.deleteFinancialGoal,
      addGoalContribution: otherData.addGoalContribution,
      setDefaultCurrency: otherData.setDefaultCurrency,
      addPaymentRecord: paymentRecordsData.addPaymentRecord,
      updatePaymentRecord: paymentRecordsData.updatePaymentRecord,
      deletePaymentRecord: paymentRecordsData.deletePaymentRecord,
      markPaymentAsPaid: paymentRecordsData.markPaymentAsPaid,
      applyCreditCardPayment: applyCreditCardPaymentMutation.mutateAsync,
    }),
    [
      incomeData.addIncome,
      incomeData.updateIncome,
      incomeData.deleteIncome,
      expenseData.addExpense,
      expenseData.updateExpense,
      expenseData.deleteExpense,
      debtData.addCreditCard,
      debtData.updateCreditCard,
      debtData.deleteCreditCard,
      debtData.addLoan,
      debtData.updateLoan,
      debtData.deleteLoan,
      debtData.addPersonalDebt,
      debtData.updatePersonalDebt,
      debtData.deletePersonalDebt,
      debtData.addPersonalDebtPayment,
      debtData.updatePersonalDebtPayment,
      debtData.deletePersonalDebtPayment,
      otherData.addSubscription,
      otherData.updateSubscription,
      otherData.deleteSubscription,
      otherData.toggleSubscriptionStatus,
      otherData.addReimbursement,
      otherData.updateReimbursement,
      otherData.deleteReimbursement,
      otherData.addFinancialGoal,
      otherData.updateFinancialGoal,
      otherData.deleteFinancialGoal,
      otherData.addGoalContribution,
      otherData.setDefaultCurrency,
      paymentRecordsData.addPaymentRecord,
      paymentRecordsData.updatePaymentRecord,
      paymentRecordsData.deletePaymentRecord,
      paymentRecordsData.markPaymentAsPaid,
      applyCreditCardPaymentMutation.mutateAsync,
    ]
  );

  return {
    ...memoizedData,
    ...actions,
  };
};

