import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  value: number | string;
  subtitle: string;
  color: string;
}

export function StatCard({ icon: Icon, title, value, subtitle, color }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium text-finanz-text-secondary flex items-center gap-2">
          <Icon className="w-4 h-4" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className={`text-2xl font-bold ${color}`}>
          {value}
        </div>
        <p className="text-xs text-finanz-text-secondary mt-1">
          {subtitle}
        </p>
      </CardContent>
    </Card>
  );
}
