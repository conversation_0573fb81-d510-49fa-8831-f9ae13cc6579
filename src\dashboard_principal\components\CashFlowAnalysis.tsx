import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useDashboardPayments } from '../hooks/useDashboardPayments';
import { TrendingUp, TrendingDown, AlertTriangle, DollarSign } from 'lucide-react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';

export const CashFlowAnalysis: React.FC = () => {
  const { netIncome, totalMonthlyPayments } = useDashboardPayments();
  const isDark = useIsDarkMode();

  // Análisis de flujo de caja proyectado
  const cashFlowProjection = useMemo(() => {
    const projection = [];
    const today = new Date();
    
    for (let i = 0; i < 6; i++) {
      const projectionDate = new Date(today.getFullYear(), today.getMonth() + i, 1);
      const monthName = projectionDate.toLocaleDateString('es-ES', { month: 'short', year: '2-digit' });

      // Usar ingresos netos reales del dashboard
      const monthlyIncomeDOP = netIncome;

      // Usar pagos reales del dashboard
      const monthPaymentsDOP = totalMonthlyPayments;

      // Gastos fijos adicionales (excluyendo pagos que ya están contabilizados)
      // const additionalExpensesDOP = expenses
      //  .filter(exp => exp.currency === 'DOP' && exp.type === 'Fijo')
      //  .reduce((sum, exp) => sum + (exp.amount || 0), 0);

      const totalIncomeDOP = monthlyIncomeDOP;
      const totalExpensesDOP = monthPaymentsDOP; // Simplified: total expenses are now just total monthly payments
      const netFlowDOP = totalIncomeDOP - totalExpensesDOP;

      projection.push({
        month: monthName,
        incomeDOP: totalIncomeDOP,
        expensesDOP: totalExpensesDOP,
        netFlowDOP: netFlowDOP,
        incomeUSD: 0,
        expensesUSD: 0,
        netFlowUSD: 0,
        netFlowTotal: netFlowDOP
      });
    }

    return projection;
  }, [netIncome, totalMonthlyPayments]);

  // Análisis de capacidad de pago
  const paymentCapacity = useMemo(() => {
    const totalMonthlyIncome = netIncome;
    const totalMonthlyObligations = totalMonthlyPayments;

    const utilizationRatio = totalMonthlyIncome > 0 ? (totalMonthlyObligations / totalMonthlyIncome) * 100 : 0;
    const availableCapacity = Math.max(0, totalMonthlyIncome - totalMonthlyObligations);


    return {
      totalIncome: totalMonthlyIncome,
      totalObligations: totalMonthlyObligations,
      utilizationRatio,
      availableCapacity,
      riskLevel: utilizationRatio > 80 ? 'high' : utilizationRatio > 60 ? 'medium' : 'low'
    };
  }, [netIncome, totalMonthlyPayments]);

  // Alertas de flujo de caja
  const cashFlowAlerts = useMemo(() => {
    const alerts = [];
    
    // Verificar meses con flujo negativo
    const negativeMonths = cashFlowProjection.filter(month => month.netFlowTotal < 0);
    if (negativeMonths.length > 0) {
      alerts.push({
        type: 'warning',
        message: `${negativeMonths.length} mes(es) con flujo de caja negativo proyectado`,
        severity: negativeMonths.length > 2 ? 'high' : 'medium'
      });
    }

    // Verificar alta utilización de capacidad de pago
    if (paymentCapacity.utilizationRatio > 80) {
      alerts.push({
        type: 'danger',
        message: 'Capacidad de pago altamente utilizada',
        severity: 'high'
      });
    }

    // Verificar si hay déficit
    if (paymentCapacity.availableCapacity <= 0) {
      alerts.push({
        type: 'danger',
        message: 'Déficit en capacidad de pago - Ingresos insuficientes',
        severity: 'high'
      });
    }

    return alerts;
  }, [cashFlowProjection, paymentCapacity]);

  const formatCurrency = (amount: number, currency: 'DOP' | 'USD' = 'DOP') => {
    const symbol = currency === 'USD' ? '$' : 'RD$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Análisis de Flujo de Caja
          </CardTitle>
          <CardDescription>
            Proyección y análisis de tu flujo de caja por los próximos 6 meses
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Métricas clave */}
            <div className="space-y-4">
              <h4 className="font-semibold text-lg">Capacidad de Pago</h4>
              
              <div className="space-y-3">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/40 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-800">Ingresos Netos</span>
                    <DollarSign className="w-4 h-4 text-blue-600" />
                  </div>
                  <p className="text-xl font-bold text-blue-600">
                    {formatCurrency(paymentCapacity.totalIncome)}
                  </p>
                </div>

                <div className="p-4 bg-orange-50 dark:bg-orange-900/40 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-orange-800">Pagos del Mes</span>
                    <AlertTriangle className="w-4 h-4 text-orange-600" />
                  </div>
                  <p className="text-xl font-bold text-orange-600">
                    {formatCurrency(paymentCapacity.totalObligations)}
                  </p>
                </div>

                <div className={`p-4 rounded-lg ${
                  paymentCapacity.riskLevel === 'high'
                    ? 'bg-red-50 dark:bg-red-900/40'
                    : paymentCapacity.riskLevel === 'medium'
                      ? 'bg-yellow-50 dark:bg-yellow-900/40'
                      : 'bg-green-50 dark:bg-green-900/40'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Utilización</span>
                    {paymentCapacity.riskLevel === 'high' ? (
                      <TrendingDown className="w-4 h-4 text-red-600" />
                    ) : (
                      <TrendingUp className="w-4 h-4 text-green-600" />
                    )}
                  </div>
                  <p className={`text-xl font-bold ${
                    paymentCapacity.riskLevel === 'high' 
                      ? 'text-red-600' 
                      : paymentCapacity.riskLevel === 'medium' 
                        ? 'text-yellow-600' 
                        : 'text-green-600'
                  }`}>
                    {paymentCapacity.utilizationRatio.toFixed(1)}%
                  </p>
                </div>

                <div className={`p-4 rounded-lg ${
                  paymentCapacity.availableCapacity >= 0
                    ? 'bg-green-50 dark:bg-green-900/40'
                    : 'bg-red-50 dark:bg-red-900/40'
                }`}>
                  <span className="text-sm font-medium text-gray-700">Capacidad Disponible</span>
                  <p className={`text-lg font-bold ${
                    paymentCapacity.availableCapacity >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatCurrency(paymentCapacity.availableCapacity)}
                  </p>
                </div>
              </div>
            </div>

            {/* Proyección de flujo de caja */}
            <div className="lg:col-span-2 space-y-4">
              <h4 className="font-semibold text-lg">Proyección de Flujo de Caja</h4>
              <p className="text-xs text-gray-500 mb-2">
                Esta proyección asume que sus ingresos netos y pagos mensuales totales actuales se mantendrán constantes durante los próximos 6 meses.
              </p>
              <div className="chart-container-responsive">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={cashFlowProjection}>
                    <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
                    <XAxis dataKey="month" stroke={isDark ? '#e5e7eb' : '#374151'} />
                    <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} />
                    <Tooltip
                      formatter={(value: number, name: string) => [
                        formatCurrency(value),
                        name === 'netFlowTotal' ? 'Flujo Neto Total' : name
                      ]}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="netFlowTotal" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.6}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>

              {/* Tabla resumen */}
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Mes</th>
                      <th className="text-right p-2">Ingresos</th>
                      <th className="text-right p-2">Pagos</th>
                      <th className="text-right p-2">Flujo Neto</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cashFlowProjection.slice(0, 3).map((month, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-2 font-medium">{month.month}</td>
                        <td className="text-right p-2 text-green-600">
                          {formatCurrency(month.incomeDOP)}
                        </td>
                        <td className="text-right p-2 text-red-600">
                          {formatCurrency(month.expensesDOP)}
                        </td>
                        <td className={`text-right p-2 font-semibold ${
                          month.netFlowTotal >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatCurrency(month.netFlowTotal)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Alertas */}
          {cashFlowAlerts.length > 0 && (
            <div className="mt-6">
              <h4 className="font-semibold text-lg mb-3 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-500" />
                Alertas de Flujo de Caja
              </h4>
              <div className="space-y-2">
                {cashFlowAlerts.map((alert, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg flex items-center gap-3 ${
                      alert.severity === 'high'
                        ? 'bg-red-50 dark:bg-red-900/40 border border-red-200 dark:border-red-700'
                        : alert.severity === 'medium'
                          ? 'bg-yellow-50 dark:bg-yellow-900/40 border border-yellow-200 dark:border-yellow-700'
                          : 'bg-blue-50 dark:bg-blue-900/40 border border-blue-200 dark:border-blue-700'
                    }`}
                  >
                    <AlertTriangle className={`w-4 h-4 ${
                      alert.severity === 'high' 
                        ? 'text-red-500' 
                        : alert.severity === 'medium'
                          ? 'text-yellow-500'
                          : 'text-blue-500'
                    }`} />
                    <span className="text-sm font-medium">{alert.message}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
