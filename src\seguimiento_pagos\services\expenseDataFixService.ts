import { supabase } from '@/integrations/supabase/client';
import { logger } from '@/utils/logger';

export class ExpenseDataFixService {
  /**
   * Corrige las descripciones de los pagos generados para julio
   */
  static async fixPaymentDescriptions(userId?: string) {
    try {
      logger.info('Iniciando corrección de descripciones de pagos...');

      // Primero, obtener los payment_records con descripciones genéricas
      const { data: payments, error: fetchError } = await supabase
        .from('payment_records')
        .select(`
          id,
          reference_id,
          notes,
          due_date,
          payment_type
        `)
        .eq('payment_type', 'expense')
        .like('notes', '%Gasto recurrente de%')
        .gte('due_date', '2025-07-01')
        .lt('due_date', '2025-08-01');

      if (fetchError) {
        logger.error('Error obteniendo pagos:', fetchError);
        throw fetchError;
      }

      if (!payments || payments.length === 0) {
        logger.info('No se encontraron pagos para corregir');
        return { updated: 0 };
      }

      // Obtener información de los gastos originales
      const expenseIds = payments.map(p => p.reference_id).filter(Boolean);
      const { data: expenses, error: expenseError } = await supabase
        .from('expenses')
        .select('id, description, category_name, amount')
        .in('id', expenseIds);

      if (expenseError) {
        logger.error('Error obteniendo gastos:', expenseError);
        throw expenseError;
      }

      // Crear un mapa de gastos para búsqueda rápida
      const expenseMap = new Map(
        expenses?.map(e => [e.id, e]) || []
      );

      // Actualizar las descripciones de los pagos
      let updatedCount = 0;
      for (const payment of payments) {
        const expense = expenseMap.get(payment.reference_id);
        if (expense) {
          let newNotes = '';
          
          if (expense.description && expense.description.trim() !== '') {
            newNotes = expense.description;
          } else if (expense.category_name) {
            newNotes = `Pago mensual - ${expense.category_name}`;
          } else {
            newNotes = `Gasto recurrente mensual - RD$${expense.amount}`;
          }

          const { error: updateError } = await supabase
            .from('payment_records')
            .update({ notes: newNotes })
            .eq('id', payment.id);

          if (updateError) {
            logger.error(`Error actualizando pago ${payment.id}:`, updateError);
          } else {
            updatedCount++;
          }
        }
      }

      logger.info(`Descripciones actualizadas: ${updatedCount} de ${payments.length}`);
      return { updated: updatedCount };
    } catch (error) {
      logger.error('Error en fixPaymentDescriptions:', error);
      throw error;
    }
  }

  /**
   * Corrige los payment_date faltantes en los gastos
   */
  static async fixMissingPaymentDates(userId?: string) {
    try {
      logger.info('Iniciando corrección de payment_date en gastos...');

      // Obtener gastos sin payment_date
      const query = supabase
        .from('expenses')
        .select('id, date')
        .is('payment_date', null);

      if (userId) {
        query.eq('user_id', userId);
      }

      const { data: expenses, error: fetchError } = await query;

      if (fetchError) {
        logger.error('Error obteniendo gastos sin payment_date:', fetchError);
        throw fetchError;
      }

      if (!expenses || expenses.length === 0) {
        logger.info('No se encontraron gastos sin payment_date');
        return { updated: 0 };
      }

      // Actualizar payment_date con el valor de date
      let updatedCount = 0;
      for (const expense of expenses) {
        const { error: updateError } = await supabase
          .from('expenses')
          .update({ payment_date: expense.date })
          .eq('id', expense.id);

        if (updateError) {
          logger.error(`Error actualizando gasto ${expense.id}:`, updateError);
        } else {
          updatedCount++;
        }
      }

      logger.info(`Payment dates actualizados: ${updatedCount} de ${expenses.length}`);
      return { updated: updatedCount };
    } catch (error) {
      logger.error('Error en fixMissingPaymentDates:', error);
      throw error;
    }
  }

  /**
   * Añade descripciones mejoradas a los gastos sin descripción
   */
  static async addMissingDescriptions(userId?: string) {
    try {
      logger.info('Iniciando adición de descripciones faltantes...');

      const query = supabase
        .from('expenses')
        .select('id, category_name, amount, date, payment_method')
        .or('description.is.null,description.eq.');

      if (userId) {
        query.eq('user_id', userId);
      }

      const { data: expenses, error: fetchError } = await query;

      if (fetchError) {
        logger.error('Error obteniendo gastos sin descripción:', fetchError);
        throw fetchError;
      }

      if (!expenses || expenses.length === 0) {
        logger.info('No se encontraron gastos sin descripción');
        return { updated: 0 };
      }

      // Generar descripciones basadas en categoría y método de pago
      let updatedCount = 0;
      for (const expense of expenses) {
        let description = '';
        
        if (expense.category_name) {
          const monthName = new Date(expense.date).toLocaleDateString('es-ES', { month: 'long' });
          description = `${expense.category_name} - ${monthName}`;
          
          if (expense.payment_method) {
            const methodNames: Record<string, string> = {
              'cash': 'Efectivo',
              'debit-card': 'Tarjeta de Débito',
              'credit-card': 'Tarjeta de Crédito',
              'transfer': 'Transferencia'
            };
            description += ` (${methodNames[expense.payment_method] || expense.payment_method})`;
          }
        } else {
          description = `Gasto del ${new Date(expense.date).toLocaleDateString('es-ES')}`;
        }

        const { error: updateError } = await supabase
          .from('expenses')
          .update({ description })
          .eq('id', expense.id);

        if (updateError) {
          logger.error(`Error actualizando descripción del gasto ${expense.id}:`, updateError);
        } else {
          updatedCount++;
        }
      }

      logger.info(`Descripciones añadidas: ${updatedCount} de ${expenses.length}`);
      return { updated: updatedCount };
    } catch (error) {
      logger.error('Error en addMissingDescriptions:', error);
      throw error;
    }
  }

  /**
   * Ejecuta todas las correcciones necesarias
   */
  static async runAllFixes(userId?: string) {
    try {
      logger.info('Ejecutando todas las correcciones de datos...');
      
      const results = {
        paymentDates: await this.fixMissingPaymentDates(userId),
        descriptions: await this.addMissingDescriptions(userId),
        paymentDescriptions: await this.fixPaymentDescriptions(userId)
      };

      logger.info('Correcciones completadas:', results);
      return results;
    } catch (error) {
      logger.error('Error ejecutando correcciones:', error);
      throw error;
    }
  }
}