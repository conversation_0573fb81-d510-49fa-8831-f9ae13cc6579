
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';

interface ExpenseStatsCardsProps {
  totalExpensesMonth: number;
  fixedExpenses: number;
  variableExpenses: number;
  transactionCount: number;
}

export function ExpenseStatsCards({ 
  totalExpensesMonth, 
  fixedExpenses, 
  variableExpenses, 
  transactionCount 
}: ExpenseStatsCardsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card className="border-finanz-border">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Total del Mes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-finanz-danger">
            {formatCurrency(totalExpensesMonth)}
          </div>
          <p className="text-xs text-finanz-text-secondary">
            {transactionCount} transacciones
          </p>
        </CardContent>
      </Card>

      <Card className="border-finanz-border">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Gastos Fijos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-finanz-purple">
            {formatCurrency(fixedExpenses)}
          </div>
          <p className="text-xs text-finanz-text-secondary">
            {((fixedExpenses / totalExpensesMonth) * 100 || 0).toFixed(1)}% del total
          </p>
        </CardContent>
      </Card>

      <Card className="border-finanz-border">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Gastos Variables</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-finanz-warning">
            {formatCurrency(variableExpenses)}
          </div>
          <p className="text-xs text-finanz-text-secondary">
            {((variableExpenses / totalExpensesMonth) * 100 || 0).toFixed(1)}% del total
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
