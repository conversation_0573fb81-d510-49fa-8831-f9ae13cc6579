import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Mail, MessageCircle, Phone, Clock, /*Headphones,*/ FileText } from 'lucide-react'; // Headphones removed
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function SupportPage() {
  const supportOptions = [
    {
      icon: Mail,
      title: 'Email de Soporte',
      description: 'Envíanos un email y te responderemos en menos de 24 horas',
      action: '<EMAIL>',
      actionType: 'email'
    },
    {
      icon: MessageCircle,
      title: 'Chat en Vivo',
      description: 'Chatea con nuestro equipo de soporte en tiempo real',
      action: 'Iniciar Chat',
      actionType: 'chat'
    },
    {
      icon: Phone,
      title: 'Soporte Telefónico',
      description: 'Llámanos para soporte inmediato',
      action: '+1 (809) XXX-XXXX',
      actionType: 'phone'
    }
  ];

  const faqItems = [
    {
      question: '¿Cómo puedo exportar mis datos?',
      answer: 'Ve a Configuración > Gestión de Datos > Exportar Datos'
    },
    {
      question: '¿Es segura mi información financiera?',
      answer: 'Sí, utilizamos encriptación de nivel bancario para proteger tus datos'
    },
    {
      question: '¿Puedo sincronizar con múltiples dispositivos?',
      answer: 'Sí, tu cuenta se sincroniza automáticamente en todos tus dispositivos'
    },
    {
      question: '¿Cómo cambio mi contraseña?',
      answer: 'Ve a Configuración > Seguridad > Cambiar Contraseña'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver al inicio
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Centro de Soporte</h1>
          <p className="text-gray-600 text-lg">
            Estamos aquí para ayudarte. Encuentra respuestas o contáctanos directamente.
          </p>
        </div>

        {/* Opciones de contacto */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {supportOptions.map((option, index) => (
            <Card key={index} className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <option.icon className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">{option.title}</CardTitle>
                <CardDescription>{option.description}</CardDescription>
              </CardHeader>
              <CardContent>
                {option.actionType === 'email' ? (
                  <Button asChild variant="outline" className="w-full">
                    <a href={`mailto:${option.action}`}>{option.action}</a>
                  </Button>
                ) : option.actionType === 'phone' ? (
                  <Button asChild variant="outline" className="w-full">
                    <a href={`tel:${option.action}`}>{option.action}</a>
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full">
                    {option.action}
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Horarios de atención */}
        <Card className="mb-12">
          <CardHeader>
            <div className="flex items-center">
              <Clock className="w-6 h-6 text-blue-600 mr-3" />
              <CardTitle>Horarios de Atención</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Soporte General</h4>
                <p className="text-gray-600">Lunes a Viernes: 8:00 AM - 8:00 PM</p>
                <p className="text-gray-600">Sábados: 9:00 AM - 5:00 PM</p>
                <p className="text-gray-600">Domingos: Cerrado</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Soporte de Emergencia</h4>
                <p className="text-gray-600">24/7 para problemas críticos</p>
                <p className="text-gray-600">Email: <EMAIL></p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* FAQ */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <FileText className="w-6 h-6 text-blue-600 mr-3" />
              <CardTitle>Preguntas Frecuentes</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {faqItems.map((item, index) => (
                <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <h4 className="font-semibold text-gray-900 mb-2">{item.question}</h4>
                  <p className="text-gray-600">{item.answer}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
