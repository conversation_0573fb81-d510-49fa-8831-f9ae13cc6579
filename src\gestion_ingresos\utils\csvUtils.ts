
/**
 * Escapa correctamente una celda CSV para evitar problemas con comas, comillas y saltos de línea
 */
export const escapeCsvCell = (cell: string | number): string => {
  const cellStr = String(cell);
  
  // Si contiene caracteres especiales (coma, comillas dobles, salto de línea),
  // encerrarlo en comillas dobles y escapar las comillas internas duplicándolas
  if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
    return `"${cellStr.replace(/"/g, '""')}"`;
  }
  
  return cellStr;
};

/**
 * Convierte un array de arrays a formato CSV con escape adecuado
 */
export const arrayToCsv = (data: (string | number)[][]): string => {
  return data
    .map(row => row.map(escapeCsvCell).join(','))
    .join('\n');
};

/**
 * Crea una fecha UTC segura desde un string YYYY-MM para evitar problemas de zona horaria
 */
export const createSafeUTCDate = (monthString: string): Date => {
  return new Date(monthString + '-01T00:00:00Z');
};

/**
 * Formatea una fecha como string de mes y año en español usando UTC
 */
export const formatMonthYear = (monthString: string): string => {
  const date = createSafeUTCDate(monthString);
  return date.toLocaleDateString('es-ES', { 
    month: 'long', 
    year: 'numeric',
    timeZone: 'UTC'
  });
};
