import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';
import { FinancialGoal } from '@/types';
import { useExchangeRate } from '@/hooks/useExchangeRate';

interface GoalsSummaryCardsProps {
  financialGoals: FinancialGoal[];
}

export function GoalsSummaryCards({ financialGoals }: GoalsSummaryCardsProps) {
  const { rate: exchangeRate } = useExchangeRate();

  const getAmountInDOP = (amount: number, currency: 'DOP' | 'USD') => {
    if (currency === 'USD') {
      return amount * exchangeRate;
    }
    return amount;
  };

  const getGoalProgress = (goal: FinancialGoal) => {
    const targetInDOP = getAmountInDOP(goal.amount, goal.currency);
    const currentInDOP = getAmountInDOP(goal.currentAmount, goal.currency);
    if (targetInDOP === 0) return 0;
    return (currentInDOP / targetInDOP) * 100;
  };

  const completedGoals = financialGoals.filter(goal => getGoalProgress(goal) >= 100);
  const inProgressGoals = financialGoals.filter(goal => {
    const progress = getGoalProgress(goal);
    return progress > 0 && progress < 100;
  });
  const highPriorityGoals = financialGoals.filter(goal => goal.priority === 'high');

  const totalTargetAmount = financialGoals.reduce((total, goal) => total + getAmountInDOP(goal.amount, goal.currency), 0);
  const totalCurrentAmount = financialGoals.reduce((total, goal) => total + getAmountInDOP(goal.currentAmount, goal.currency), 0);
  const overallProgress = totalTargetAmount > 0 ? (totalCurrentAmount / totalTargetAmount) * 100 : 0;

  return (
    <div className="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Total Metas</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-primary">{financialGoals.length}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            {formatCurrency(totalTargetAmount)}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Completadas</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-success">{completedGoals.length}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            {financialGoals.length > 0 ? `${((completedGoals.length / financialGoals.length) * 100).toFixed(1)}%` : '0%'}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">En Progreso</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-warning">{inProgressGoals.length}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            Progreso General: {overallProgress.toFixed(1)}%
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Prioridad Alta</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-danger">{highPriorityGoals.length}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            Requieren atención
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
