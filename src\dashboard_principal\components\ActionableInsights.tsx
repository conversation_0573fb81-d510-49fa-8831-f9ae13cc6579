
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Calendar,
  DollarSign,
  ArrowRight
} from 'lucide-react';
import { useFinanceData } from '@/hooks/useFinanceData';

interface Insight {
  id: string;
  type: 'trend' | 'goal' | 'opportunity' | 'warning';
  title: string;
  description: string;
  value?: string;
  progress?: number;
  action?: string;
  actionLabel?: string;
  icon: React.ReactNode;
}

export function ActionableInsights() {
  const { 
    incomes,
    expenses,
    getNetBalance,
    getSavingsRate
  } = useFinanceData();

  const generateInsights = (): Insight[] => {
    const insights: Insight[] = [];
    const currentMonth = new Date().toISOString().slice(0, 7);
    const lastMonth = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().slice(0, 7);
    
    // Análisis de tendencias de ingresos
    const currentIncome = incomes.find(i => i.month === currentMonth);
    const lastIncome = incomes.find(i => i.month === lastMonth);
    
    if (currentIncome && lastIncome) {
      const incomeChange = ((currentIncome.netIncome - lastIncome.netIncome) / lastIncome.netIncome) * 100;
      
      if (Math.abs(incomeChange) > 5) {
        insights.push({
          id: 'income-trend',
          type: incomeChange > 0 ? 'opportunity' : 'warning',
          title: incomeChange > 0 ? 'Ingresos en Crecimiento' : 'Disminución de Ingresos',
          description: `Tus ingresos ${incomeChange > 0 ? 'aumentaron' : 'disminuyeron'} ${Math.abs(incomeChange).toFixed(1)}% vs el mes anterior.`,
          value: `${incomeChange > 0 ? '+' : ''}${incomeChange.toFixed(1)}%`,
          action: 'analyze-income',
          actionLabel: 'Analizar Ingresos',
          icon: incomeChange > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />
        });
      }
    }
    
    // Análisis de gastos
    const currentExpenses = expenses.filter(e => e.month === currentMonth);
    const lastExpenses = expenses.filter(e => e.month === lastMonth);
    
    if (currentExpenses.length > 0 && lastExpenses.length > 0) {
      const currentTotal = currentExpenses.reduce((sum, e) => sum + e.amount, 0);
      const lastTotal = lastExpenses.reduce((sum, e) => sum + e.amount, 0);
      const expenseChange = ((currentTotal - lastTotal) / lastTotal) * 100;
      
      if (Math.abs(expenseChange) > 10) {
        insights.push({
          id: 'expense-trend',
          type: expenseChange > 0 ? 'warning' : 'opportunity',
          title: expenseChange > 0 ? 'Gastos Incrementados' : 'Gastos Controlados',
          description: `Tus gastos ${expenseChange > 0 ? 'aumentaron' : 'disminuyeron'} ${Math.abs(expenseChange).toFixed(1)}% este mes.`,
          value: `${expenseChange > 0 ? '+' : ''}${expenseChange.toFixed(1)}%`,
          action: 'review-expenses',
          actionLabel: 'Revisar Gastos',
          icon: expenseChange > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />
        });
      }
    }

    // Meta de ahorro
    const savingsRate = getSavingsRate();
    const savingsGoal = 20; // Meta del 20%
    
    insights.push({
      id: 'savings-goal',
      type: 'goal',
      title: 'Meta de Ahorro',
      description: `Progreso hacia tu meta de ahorro del ${savingsGoal}%.`,
      value: `${savingsRate.toFixed(1)}%`,
      progress: Math.min((savingsRate / savingsGoal) * 100, 100),
      action: 'improve-savings',
      actionLabel: 'Mejorar Ahorros',
      icon: <Target className="w-4 h-4" />
    });

    // Oportunidad de optimización
    const netBalance = getNetBalance();
    if (netBalance > 0) {
      const monthlyExpenses = currentExpenses.reduce((sum, e) => sum + e.amount, 0);
      const emergencyFundRatio = netBalance / (monthlyExpenses || 1);
      
      if (emergencyFundRatio > 6) {
        insights.push({
          id: 'investment-opportunity',
          type: 'opportunity',
          title: 'Oportunidad de Inversión',
          description: 'Tienes exceso de efectivo que podrías considerar invertir.',
          value: `${emergencyFundRatio.toFixed(1)} meses`,
          action: 'explore-investments',
          actionLabel: 'Explorar Inversiones',
          icon: <DollarSign className="w-4 h-4" />
        });
      }
    }

    // Próximos pagos importantes
    const today = new Date();
    const dayOfMonth = today.getDate();
    
    if (dayOfMonth >= 25) { // Fin de mes, recordar pagos próximos
      insights.push({
        id: 'upcoming-payments',
        type: 'warning',
        title: 'Pagos Próximos',
        description: 'Se acercan las fechas de pago de tus compromisos financieros.',
        action: 'view-payments',
        actionLabel: 'Ver Pagos',
        icon: <Calendar className="w-4 h-4" />
      });
    }

    return insights;
  };

  const insights = generateInsights();

  const getInsightColor = (type: Insight['type']) => {
    switch (type) {
      case 'opportunity':
        return 'border-green-200 bg-green-50 dark:bg-green-900/40 dark:border-green-700';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/40 dark:border-yellow-700';
      case 'goal':
        return 'border-blue-200 bg-blue-50 dark:bg-blue-900/40 dark:border-blue-700';
      case 'trend':
        return 'border-purple-200 bg-purple-50 dark:bg-purple-900/40 dark:border-purple-700';
      default:
        return 'border-gray-200 bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700';
    }
  };

  const getTypeLabel = (type: Insight['type']) => {
    switch (type) {
      case 'opportunity': return 'Oportunidad';
      case 'warning': return 'Atención';
      case 'goal': return 'Meta';
      case 'trend': return 'Tendencia';
      default: return 'Insight';
    }
  };

  const getTypeColor = (type: Insight['type']) => {
    switch (type) {
      case 'opportunity':
        return 'bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-300';
      case 'warning':
        return 'bg-yellow-100 dark:bg-yellow-900/40 text-yellow-800 dark:text-yellow-300';
      case 'goal':
        return 'bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-300';
      case 'trend':
        return 'bg-purple-100 dark:bg-purple-900/40 text-purple-800 dark:text-purple-300';
      default:
        return 'bg-gray-100 dark:bg-neutral-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getProgressColor = (type: Insight['type']) => {
    switch (type) {
      case 'opportunity':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'goal':
        return 'bg-finanz-primary';
      case 'trend':
        return 'bg-purple-500';
      default:
        return 'bg-finanz-primary';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-finanz-primary" />
          Insights Inteligentes
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {insights.length === 0 ? (
          <div className="text-center py-6">
            <Brain className="w-12 h-12 mx-auto text-gray-400 mb-2" />
            <p className="text-finanz-text-secondary">No hay insights disponibles en este momento.</p>
          </div>
        ) : (
          insights.map((insight) => (
            <div
              key={insight.id}
              className={`border rounded-lg p-4 ${getInsightColor(insight.type)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <div className="mt-0.5">
                    {insight.icon}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium text-sm">{insight.title}</h4>
                      <Badge className={`text-xs ${getTypeColor(insight.type)}`}>
                        {getTypeLabel(insight.type)}
                      </Badge>
                      {insight.value && (
                        <span className="text-sm font-semibold text-finanz-primary">
                          {insight.value}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-sm text-finanz-text-secondary mb-3">
                      {insight.description}
                    </p>
                    
                    {insight.progress !== undefined && (
                      <div className="mb-3">
                        <div className="flex justify-between text-xs mb-1">
                          <span>Progreso</span>
                          <span>{insight.progress.toFixed(0)}%</span>
                        </div>
                        <Progress
                          value={insight.progress}
                          className="h-2"
                          indicatorClassName={getProgressColor(insight.type)}
                        />
                      </div>
                    )}
                    
                    {insight.action && (
                      <Button variant="outline" size="sm" className="text-xs">
                        {insight.actionLabel}
                        <ArrowRight className="w-3 h-3 ml-1" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
