
import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>2, <PERSON>, <PERSON> } from 'lucide-react';
import { useSmartIncomeReplication } from '../hooks/useSmartIncomeReplication';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/components/ui/numeric-input';

interface SmartReplicateButtonProps {
  onSuccess?: (recommendations: string[]) => void;
}

export function SmartReplicateButton({ onSuccess }: SmartReplicateButtonProps) {
  const { replicateLastMonth, canReplicate, isReplicating, getReplicationPreview } = useSmartIncomeReplication();
  const [showPreview, setShowPreview] = useState(false);
  const [recommendations, setRecommendations] = useState<string[]>([]);

  const preview = getReplicationPreview();

  const handleReplicate = async () => {
    const result = await replicateLastMonth();
    if (result.income && onSuccess) {
      setRecommendations(result.recommendations);
      onSuccess(result.recommendations);
    }
    setShowPreview(false);
  };

  const getTooltipContent = () => {
    if (!preview) {
      return 'No hay datos previos para replicar';
    }
    
    return `Replicación inteligente para ${preview.month}`;
  };

  return (
    <TooltipProvider>
      <div className="flex gap-2">
        {/* Vista previa */}
        {canReplicate && preview && (
          <Dialog open={showPreview} onOpenChange={setShowPreview}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <Eye className="w-4 h-4" />
                Vista Previa
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5 text-blue-500" />
                  Replicación Inteligente
                </DialogTitle>
                <DialogDescription>
                  Vista previa de los datos que se replicarán para {preview.month}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-600">Sueldo Fijo</p>
                    <p className="text-lg font-bold">
                      {formatCurrency(preview.fixedSalary, 'DOP')}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Variable</p>
                    <p className="text-lg font-bold">
                      {preview.variablePercentage.toFixed(1)}%
                    </p>
                  </div>
                  {preview.quarterlyIncentive > 0 && (
                    <div className="col-span-2">
                      <p className="font-medium text-gray-600">Incentivo Trimestral Predicho</p>
                      <p className="text-lg font-bold text-green-600">
                        {formatCurrency(preview.quarterlyIncentive, 'DOP')}
                      </p>
                    </div>
                  )}
                </div>

                {preview.recommendations.length > 0 && (
                  <div className="space-y-2">
                    <p className="font-medium text-gray-600">Recomendaciones:</p>
                    <div className="space-y-1">
                      {preview.recommendations.map((rec, index) => (
                        <Badge key={index} variant="secondary" className="text-xs block w-full p-2 h-auto">
                          {rec}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button onClick={handleReplicate} disabled={isReplicating} className="flex-1">
                    {isReplicating ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <Copy className="w-4 h-4 mr-2" />
                    )}
                    Replicar Datos
                  </Button>
                  <Button variant="outline" onClick={() => setShowPreview(false)}>
                    Cancelar
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Botón principal */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={handleReplicate}
              disabled={!canReplicate}
              variant="outline"
              className="gap-2"
            >
              {isReplicating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Brain className="w-4 h-4 text-blue-500" />
              )}
              Replicación Inteligente
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{getTooltipContent()}</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}
