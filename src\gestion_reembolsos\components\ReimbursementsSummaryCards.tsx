
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';

interface ReimbursementsSummaryCardsProps {
  pendingCount: number;
  processingCount: number;
  completedCount: number;
  totalCount: number;
  pendingAmount: number;
  processingAmount: number;
  completedAmount: number;
  totalAmount: number;
}

export function ReimbursementsSummaryCards({
  pendingCount,
  processingCount,
  completedCount,
  totalCount,
  pendingAmount,
  processingAmount,
  completedAmount,
  totalAmount
}: ReimbursementsSummaryCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Pendientes</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-warning">{pendingCount}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            {formatCurrency(pendingAmount)}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Procesando</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-blue-600">{processingCount}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            {formatCurrency(processingAmount)}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Completados</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-success">{completedCount}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            {formatCurrency(completedAmount)}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-finanz-text-secondary">Total Reembolsos</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-2xl font-bold text-finanz-primary">{totalCount}</div>
          <p className="text-xs text-finanz-text-secondary mt-1">
            {formatCurrency(totalAmount)}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
