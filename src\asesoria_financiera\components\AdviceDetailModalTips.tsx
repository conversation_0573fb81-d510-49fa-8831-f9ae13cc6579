
import React from 'react';

interface AdviceDetailModalTipsProps {
  tips: string[];
}

export const AdviceDetailModalTips: React.FC<AdviceDetailModalTipsProps> = ({
  tips
}) => {
  return (
    <div>
      <h4 className="text-lg font-semibold mb-4">Consejos Adicionales</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {tips.map((tip, index) => (
          <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-finanz-primary rounded-full mt-2 flex-shrink-0"></div>
            <p className="text-sm">{tip}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
