
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface NotificationSettings {
  paymentReminders: boolean;
  budgetAlerts: boolean;
  goalProgress: boolean;
  emailReports: boolean;
  pushNotifications: boolean;
  overduePayments: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
}

export class NotificationService {
  private static instance: NotificationService;
  private settings: NotificationSettings | null = null;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async loadSettings(): Promise<NotificationSettings> {
    try {
      const savedSettings = localStorage.getItem('finanz_notification_settings');
      if (savedSettings) {
        this.settings = JSON.parse(savedSettings);
        return this.settings!;
      }

      // Configuración por defecto
      this.settings = {
        paymentReminders: true,
        budgetAlerts: true,
        goalProgress: true,
        emailReports: false,
        pushNotifications: true,
        overduePayments: true,
        weeklyReports: false,
        monthlyReports: true,
      };

      return this.settings;
    } catch (error) {
      console.error('Error loading notification settings:', error);
      throw error;
    }
  }

  async saveSettings(settings: NotificationSettings): Promise<void> {
    try {
      localStorage.setItem('finanz_notification_settings', JSON.stringify(settings));
      this.settings = settings;
      
      // Programar notificaciones basadas en las configuraciones
      await this.scheduleNotifications(settings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
      throw error;
    }
  }

  private async scheduleNotifications(settings: NotificationSettings): Promise<void> {
    try {
      if (settings.paymentReminders) {
        await this.schedulePaymentReminders();
      }

      if (settings.budgetAlerts) {
        await this.scheduleBudgetAlerts();
      }

      if (settings.goalProgress) {
        await this.scheduleGoalProgressUpdates();
      }

      if (settings.weeklyReports) {
        await this.scheduleWeeklyReports();
      }

      if (settings.monthlyReports) {
        await this.scheduleMonthlyReports();
      }
    } catch (error) {
      console.error('Error scheduling notifications:', error);
    }
  }

  private async schedulePaymentReminders(): Promise<void> {
    try {
      const { data: paymentRecords } = await supabase
        .from('payment_records')
        .select('*')
        .eq('status', 'pending')
        .gte('due_date', new Date().toISOString().split('T')[0]);

      if (paymentRecords) {
        paymentRecords.forEach(payment => {
          const dueDate = new Date(payment.due_date);
          const today = new Date();
          const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

          if (daysUntilDue <= 3 && daysUntilDue >= 0) {
            this.showNotification('Recordatorio de Pago', 
              `Tienes un pago de ${payment.amount} ${payment.currency} que vence ${daysUntilDue === 0 ? 'hoy' : `en ${daysUntilDue} días`}`);
          }
        });
      }
    } catch (error) {
      console.error('Error scheduling payment reminders:', error);
    }
  }

  private async scheduleBudgetAlerts(): Promise<void> {
    try {
      const { data: expenses } = await supabase
        .from('expenses')
        .select('amount, currency, category_name')
        .eq('month', new Date().toISOString().slice(0, 7)); // YYYY-MM format

      if (expenses) {
        const categoryTotals = expenses.reduce((acc, expense) => {
          const key = `${expense.category_name}_${expense.currency}`;
          acc[key] = (acc[key] || 0) + Number(expense.amount);
          return acc;
        }, {} as Record<string, number>);

        // Ejemplo: alertar si se supera cierto límite por categoría
        Object.entries(categoryTotals).forEach(([categoryKey, total]) => {
          const [category, currency] = categoryKey.split('_');
          const limit = 50000; // Límite ejemplo
          
          if (total > limit * 0.8) {
            this.showNotification('Alerta de Presupuesto', 
              `Has gastado ${total} ${currency} en ${category}. Te acercas al límite mensual.`);
          }
        });
      }
    } catch (error) {
      console.error('Error scheduling budget alerts:', error);
    }
  }

  private async scheduleGoalProgressUpdates(): Promise<void> {
    try {
      const { data: goals } = await supabase
        .from('financial_goals')
        .select('*')
        .eq('is_active', true);

      if (goals) {
        goals.forEach(goal => {
          const progress = (goal.current_amount / goal.amount) * 100;
          
          if (progress >= 25 && progress < 30) {
            this.showNotification('Progreso de Meta', 
              `¡Has completado el 25% de tu meta "${goal.name}"! Sigue así.`);
          } else if (progress >= 50 && progress < 55) {
            this.showNotification('Progreso de Meta', 
              `¡Estás a la mitad de tu meta "${goal.name}"! Ya llevas 50%.`);
          } else if (progress >= 75 && progress < 80) {
            this.showNotification('Progreso de Meta', 
              `¡Casi lo logras! Has completado el 75% de "${goal.name}".`);
          } else if (progress >= 100) {
            this.showNotification('¡Meta Alcanzada!', 
              `¡Felicidades! Has completado tu meta "${goal.name}".`);
          }
        });
      }
    } catch (error) {
      console.error('Error scheduling goal progress updates:', error);
    }
  }

  private async scheduleWeeklyReports(): Promise<void> {
    // Verificar si es lunes para enviar reporte semanal
    const today = new Date();
    if (today.getDay() === 1) { // Lunes
      try {
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - 7);
        
        const { data: weeklyExpenses } = await supabase
          .from('expenses')
          .select('amount, currency')
          .gte('date', weekStart.toISOString().split('T')[0])
          .lt('date', today.toISOString().split('T')[0]);

        if (weeklyExpenses) {
          const total = weeklyExpenses.reduce((sum, expense) => sum + Number(expense.amount), 0);
          this.showNotification('Reporte Semanal', 
            `Esta semana gastaste ${total} en total. Revisa tu actividad financiera.`);
        }
      } catch (error) {
        console.error('Error generating weekly report:', error);
      }
    }
  }

  private async scheduleMonthlyReports(): Promise<void> {
    // Verificar si es el primer día del mes
    const today = new Date();
    if (today.getDate() === 1) {
      try {
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const monthStr = lastMonth.toISOString().slice(0, 7);
        
        const { data: monthlyData } = await supabase
          .from('expenses')
          .select('amount, currency, category_name')
          .eq('month', monthStr);

        if (monthlyData) {
          const total = monthlyData.reduce((sum, expense) => sum + Number(expense.amount), 0);
          this.showNotification('Reporte Mensual', 
            `El mes pasado gastaste ${total} en total. Tu reporte completo está disponible.`);
        }
      } catch (error) {
        console.error('Error generating monthly report:', error);
      }
    }
  }

  private showNotification(title: string, message: string): void {
    if (this.settings?.pushNotifications) {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, { body: message });
      } else {
        toast.info(`${title}: ${message}`);
      }
    }
  }

  async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  async checkOverduePayments(): Promise<void> {
    if (!this.settings?.overduePayments) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      const { data: overduePayments } = await supabase
        .from('payment_records')
        .select('*')
        .eq('status', 'pending')
        .lt('due_date', today);

      if (overduePayments && overduePayments.length > 0) {
        this.showNotification('Pagos Vencidos', 
          `Tienes ${overduePayments.length} pago(s) vencido(s). Revísalos urgentemente.`);
      }
    } catch (error) {
      console.error('Error checking overdue payments:', error);
    }
  }
}

export const notificationService = NotificationService.getInstance();
