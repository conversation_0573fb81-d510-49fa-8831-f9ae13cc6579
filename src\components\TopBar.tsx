
import React, { useEffect } from 'react';
import { UserMenu } from './UserMenu';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { SecurityProvider } from './security/SecurityProvider';
import { sessionSecurity } from '@/utils/security/sessionSecurity';
import { useAuth } from '@/contexts/AuthContext';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export const TopBar: React.FC = () => {
  const { user, loading } = useAuth();
  const { isMobile, isTablet } = useBreakpoint();

  useEffect(() => {
    // Start enhanced session monitoring only when user is authenticated
    if (user && !loading) {
      sessionSecurity.startSessionMonitoring();
    }
    
    // Setup security event listeners
    const handleSessionInactive = () => {
      console.warn('Session inactive - implementing security measures');
    };
    
    window.addEventListener('security:sessionInactive', handleSessionInactive);
    
    return () => {
      sessionSecurity.stopSessionMonitoring();
      window.removeEventListener('security:sessionInactive', handleSessionInactive);
    };
  }, [user, loading]);

  return (
    <SecurityProvider>
      <header className={`
        sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur 
        supports-[backdrop-filter]:bg-background/60
        ${isMobile ? 'h-12' : isTablet ? 'h-13' : 'h-14'}
      `}>
        <div className={`
          flex items-center justify-between w-full
          ${isMobile ? 'h-12 px-2' : isTablet ? 'h-13 px-3' : 'h-14 px-4'}
        `}>
          <div className="flex items-center">
            <SidebarTrigger className={`
              ${isMobile ? 'mr-1 h-8 w-8' : 'mr-2 h-9 w-9'}
            `} />
            {/* Logo/título para móvil */}
            {isMobile && (
              <h1 className="text-sm font-semibold text-finanz-primary truncate">
                FinanzApp
              </h1>
            )}
          </div>
          
          <div className="flex items-center space-x-1">
            <UserMenu />
          </div>
        </div>
      </header>
    </SecurityProvider>
  );
};
