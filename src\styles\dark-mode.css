

@layer base {
  /* Light mode base styles */
  html {
    background-color: #ffffff;
  }

  body {
    background-color: #ffffff;
    color: hsl(222.2 84% 4.9%);
  }

  #root {
    background-color: #ffffff;
  }

  .min-h-screen {
    background-color: #ffffff;
  }

  main {
    background-color: #ffffff;
  }

  div {
    border-color: hsl(214.3 31.8% 91.4%);
  }

  h1, h2, h3, h4, h5, h6 {
    color: rgb(17 24 39);
  }

  label {
    color: rgb(55 65 81);
  }

  .text-muted-foreground {
    color: rgb(75 85 99);
  }

  .border {
    border-color: rgb(229 231 235);
  }

  ::placeholder {
    color: rgb(156 163 175);
  }

  svg {
    color: currentColor;
  }

  [disabled] {
    color: rgb(156 163 175);
  }

  .bg-background {
    background-color: #ffffff !important;
  }

  .bg-white {
    background-color: #ffffff !important;
    color: black !important;
  }

  [data-sidebar-provider] {
    background-color: #ffffff;
  }

  [data-sidebar-provider] > div {
    background-color: #ffffff;
  }

  .bg-finanz-neutral {
    background-color: rgb(249 250 251);
  }
  
  .bg-finanz-neutral\/20 {
    background-color: rgba(249 250 251 / 0.2);
  }
  
  .text-finanz-text-secondary {
    color: rgb(75 85 99);
  }
  
  .border-finanz-border {
    border-color: rgb(229 231 235);
  }

  /* Dark mode base styles */
  .dark {
    color: hsl(0 0% 95%);
    background-color: #0a0a0a;
  }

  .dark html {
    background-color: #0a0a0a;
  }

  .dark body {
    background-color: #0a0a0a;
    color: hsl(0 0% 95%);
  }

  .dark #root {
    background-color: #0a0a0a;
  }

  .dark .min-h-screen {
    background-color: #0a0a0a;
  }

  .dark main {
    background-color: #0a0a0a;
  }

  .dark div {
    border-color: hsl(0 0% 25%);
  }

  .dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
    color: white;
  }

  .dark label {
    color: rgb(229 231 235);
  }

  .dark .text-muted-foreground {
    color: rgb(209 213 219);
  }

  .dark .border {
    border-color: rgb(55 65 81);
  }

  .dark ::placeholder {
    color: rgb(156 163 175);
  }

  .dark svg {
    color: currentColor;
  }

  .dark [disabled] {
    color: rgb(107 114 128);
  }

  .dark .bg-background {
    background-color: #0a0a0a !important;
  }

  .dark .bg-white {
    background-color: #1a1a1a !important;
    color: white !important;
  }

  .dark [data-sidebar-provider] {
    background-color: #0a0a0a;
  }

  .dark [data-sidebar-provider] > div {
    background-color: #0a0a0a;
  }

  .dark .bg-finanz-neutral {
    background-color: rgb(31 41 55);
  }
  
  .dark .bg-finanz-neutral\/20 {
    background-color: rgba(31 41 55 / 0.2);
  }
  
  .dark .text-finanz-text-secondary {
    color: rgb(209 213 219);
  }
  
  .dark .border-finanz-border {
    border-color: rgb(75 85 99);
  }

  /* Specific text color improvements for dark mode */
  .dark .text-gray-600 {
    color: rgb(209 213 219);
  }

  .dark .text-gray-700 {
    color: rgb(229 231 235);
  }

  .dark .text-gray-800 {
    color: rgb(243 244 246);
  }

  .dark .text-gray-900 {
    color: white;
  }

  /* Progress bars and badges */
  .dark .text-sm {
    color: rgb(229 231 235);
  }

  .dark .text-xs {
    color: rgb(209 213 219);
  }

  /* Ensure good contrast for all text elements */
  .dark p, .dark span, .dark div {
    color: inherit;
  }

  .dark .text-finanz-success {
    color: rgb(74 222 128);
  }

  .dark .text-finanz-danger {
    color: rgb(248 113 113);
  }

  .dark .text-finanz-warning {
    color: rgb(251 191 36);
  }

  .dark .text-finanz-primary {
    color: rgb(96 165 250);
  }

  .dark .bg-gray-50 {
    background-color: #1f2937;
  }

  .dark .bg-gray-100 {
    background-color: #374151;
  }

  .dark .bg-gray-200 {
    background-color: #374151;
  }

  .dark .bg-blue-50 {
    background-color: rgba(59,130,246,0.2);
  }

  .dark .bg-green-50 {
    background-color: rgba(34,197,94,0.2);
  }

  .dark .bg-yellow-50 {
    background-color: rgba(234,179,8,0.2);
  }

  .dark .bg-red-50 {
    background-color: rgba(239,68,68,0.2);
  }

  .dark .bg-purple-50 {
    background-color: rgba(139,92,246,0.2);
  }

  .dark .bg-orange-50 {
    background-color: rgba(249,115,22,0.2);
  }

  /* Colored badges and text */
  .dark .bg-green-100 { background-color: rgba(74,222,128,0.15); }
  .dark .text-green-800 { color: rgb(74 222 128); }
  .dark .text-green-700 { color: rgb(74 222 128); }

  .dark .bg-yellow-100 { background-color: rgba(234,179,8,0.15); }
  .dark .text-yellow-800 { color: rgb(253 224 71); }
  .dark .text-yellow-700 { color: rgb(253 224 71); }

  .dark .bg-red-100 { background-color: rgba(248,113,113,0.15); }
  .dark .text-red-800 { color: rgb(248 113 113); }
  .dark .text-red-700 { color: rgb(248 113 113); }

  .dark .bg-blue-100 { background-color: rgba(59,130,246,0.15); }
  .dark .text-blue-800 { color: rgb(96 165 250); }
  .dark .text-blue-700 { color: rgb(96 165 250); }

  .dark .bg-purple-100 { background-color: rgba(168,85,247,0.15); }
  .dark .text-purple-800 { color: rgb(192 132 252); }

  .dark .bg-orange-100 { background-color: rgba(249,115,22,0.15); }
  .dark .text-orange-800 { color: rgb(251 146 60); }
  .dark .text-orange-700 { color: rgb(251 146 60); }

  .dark .bg-amber-100 { background-color: rgba(245,158,11,0.15); }
  .dark .text-amber-800 { color: rgb(251 191 36); }
  .dark .text-amber-700 { color: rgb(251 191 36); }

  .dark .bg-emerald-100 { background-color: rgba(16,185,129,0.15); }
  .dark .text-emerald-600 { color: rgb(52 211 153); }
}

