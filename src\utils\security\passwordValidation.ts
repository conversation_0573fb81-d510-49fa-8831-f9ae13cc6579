
import { z } from 'zod';

export const passwordRequirements = {
  minLength: 8, // Cambiado de 12 a 8
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxLength: 128
};

// Enhanced password schema with strong requirements
export const strongPasswordSchema = z.string()
  .min(passwordRequirements.minLength, `La contraseña debe tener al menos ${passwordRequirements.minLength} caracteres`)
  .max(passwordRequirements.maxLength, `La contraseña no puede exceder ${passwordRequirements.maxLength} caracteres`)
  .regex(/[A-Z]/, 'La contraseña debe contener al menos una letra mayúscula')
  .regex(/[a-z]/, 'La contraseña debe contener al menos una letra minúscula')
  .regex(/[0-9]/, 'La contraseña debe contener al menos un número')
  .regex(/[^A-Za-z0-9]/, 'La contraseña debe contener al menos un carácter especial')
  .refine((password) => {
    // Check for common patterns
    const commonPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /admin/i,
      /letmein/i,
      /welcome/i,
      /monkey/i,
      /dragon/i
    ];
    return !commonPatterns.some(pattern => pattern.test(password));
  }, 'La contraseña contiene patrones comunes no permitidos')
  .refine((password) => {
    // Check for repeated characters (three or more consecutive characters)
    return !/(.)\1{3,}/.test(password);
  }, 'La contraseña no puede tener más de 3 caracteres repetidos consecutivos')
  .refine((password) => {
    // Check for sequential characters
    const sequences = ['0123456789', 'abcdefghijklmnopqrstuvwxyz', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm'];
    return !sequences.some(seq => 
      password.toLowerCase().includes(seq.slice(0, 4)) || 
      password.toLowerCase().includes(seq.slice(0, 4).split('').reverse().join(''))
    );
  }, 'La contraseña no puede contener secuencias de caracteres');

export const calculatePasswordStrength = (password: string): {
  score: number;
  label: string;
  color: string;
  feedback: string[];
} => {
  let score = 0;
  const feedback: string[] = [];
  
  // Length scoring
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;
  else if (password.length < 8) feedback.push('Usa al menos 8 caracteres'); // Cambiado de 12 a 8
  
  // Character variety scoring
  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push('Incluye letras mayúsculas');
  
  if (/[a-z]/.test(password)) score += 1;
  else feedback.push('Incluye letras minúsculas');
  
  if (/[0-9]/.test(password)) score += 1;
  else feedback.push('Incluye números');
  
  if (/[^A-Za-z0-9]/.test(password)) score += 1;
  else feedback.push('Incluye caracteres especiales (!@#$%^&*)');
  
  // Bonus points for extra complexity
  if (password.length >= 20) score += 1;
  if (/[^A-Za-z0-9\s]/.test(password) && password.length >= 16) score += 1;
  
  // Penalty for common patterns
  const commonPatterns = [/123456/, /password/i, /qwerty/i];
  if (commonPatterns.some(pattern => pattern.test(password))) {
    score -= 2;
    feedback.push('Evita patrones comunes');
  }
  
  // Determine label and color
  let label: string;
  let color: string;
  
  if (score <= 3) {
    label = 'Muy débil';
    color = 'text-red-600';
  } else if (score <= 5) {
    label = 'Débil';
    color = 'text-orange-600';
  } else if (score <= 7) {
    label = 'Media';
    color = 'text-yellow-600';
  } else if (score <= 9) {
    label = 'Fuerte';
    color = 'text-green-600';
  } else {
    label = 'Muy fuerte';
    color = 'text-emerald-600';
  }
  
  return { score: Math.max(0, score), label, color, feedback };
};

export const isPasswordCompromised = async (password: string): Promise<boolean> => {
  try {
    // Use Have I Been Pwned API to check for compromised passwords
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-1', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('').toUpperCase();
    
    const prefix = hashHex.slice(0, 5);
    const suffix = hashHex.slice(5);
    
    const response = await fetch(`https://api.pwnedpasswords.com/range/${prefix}`);
    if (!response.ok) return false;
    
    const hashes = await response.text();
    return hashes.includes(suffix);
  } catch (error) {
    console.warn('Could not check password compromise status:', error);
    return false; // Don't block if service is unavailable
  }
};
