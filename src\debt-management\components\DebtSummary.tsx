import { logger } from "@/utils/logger";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/components/ui/numeric-input';

interface DebtSummaryProps {
  totalDebt: number;
}

export function DebtSummary({ totalDebt }: DebtSummaryProps) {
  logger.debug('DebtSummary received totalDebt:', totalDebt);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Deuda Total</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-finanz-purple">
          {formatCurrency(totalDebt)}
        </div>
        <p className="text-sm text-finanz-text-secondary mt-1">
          Total adeudado en tarjetas, préstamos y deudas personales
        </p>
      </CardContent>
    </Card>
  );
}
