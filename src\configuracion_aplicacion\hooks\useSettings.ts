
import { useProfileSettings } from './useProfileSettings';
import { useNotificationSettings } from './useNotificationSettings';
import { useSecuritySettings } from './useSecuritySettings';
import { useAppearanceSettings } from './useAppearanceSettings';
import { useDataManagement } from './useDataManagement';

export const useSettings = () => {
  const profileSettings = useProfileSettings();
  const notificationSettings = useNotificationSettings();
  const securitySettings = useSecuritySettings();
  const appearanceSettings = useAppearanceSettings();
  const dataManagement = useDataManagement();

  return {
    // Profile
    profileForm: profileSettings.profileForm,
    saveProfile: profileSettings.saveProfile,
    
    // Notifications
    notificationForm: notificationSettings.notificationForm,
    saveNotifications: notificationSettings.saveNotifications,
    notificationService: notificationSettings.notificationService,
    
    // Security
    securityForm: securitySettings.securityForm,
    saveSecurity: securitySettings.saveSecurity,
    changePassword: securitySettings.changePassword,
    securityService: securitySettings.securityService,
    
    // Appearance
    appearanceForm: appearanceSettings.appearanceForm,
    saveAppearance: appearanceSettings.saveAppearance,
    applyThemeImmediately: appearanceSettings.applyThemeImmediately,
    
    // Data Management - only deleteAllData and currency settings
    deleteAllData: dataManagement.deleteAllData,
    defaultCurrency: dataManagement.defaultCurrency,
    setDefaultCurrency: dataManagement.setDefaultCurrency,
    
    // Combined loading state
    isLoading: profileSettings.isLoading || 
               notificationSettings.isLoading || 
               securitySettings.isLoading || 
               appearanceSettings.isLoading || 
               dataManagement.isLoading,
  };
};
