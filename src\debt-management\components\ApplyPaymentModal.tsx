
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { NumericInput, formatCurrency } from '@/components/ui/numeric-input';
import { DatePicker } from '@/components/ui/date-picker';
import { Textarea } from '@/components/ui/textarea';
import { CreditCard } from '@/types';
import { Loader2 } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

const paymentSchema = z.object({
  amount: z.number({ required_error: 'El monto es requerido.'}).positive('El monto debe ser mayor a cero.'),
  paidDate: z.date({ required_error: 'La fecha de pago es requerida.' }),
  notes: z.string().optional(),
});

export type PaymentFormData = z.infer<typeof paymentSchema>;

interface ApplyPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: PaymentFormData) => Promise<void>;
  card: CreditCard | null;
  isSubmitting: boolean;
}

export function ApplyPaymentModal({ isOpen, onClose, onSubmit, card, isSubmitting }: ApplyPaymentModalProps) {
  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      amount: undefined,
      paidDate: new Date(),
      notes: '',
    },
  });

  const [paymentOption, setPaymentOption] = React.useState<'custom' | 'minimum' | 'total'>('custom');

  React.useEffect(() => {
    if (isOpen) {
      setPaymentOption('custom');
      form.reset({
        amount: undefined,
        paidDate: new Date(),
        notes: '',
      });
    }
  }, [isOpen, form]);

  const handlePaymentOptionChange = (value: string) => {
    const option = value as 'custom' | 'minimum' | 'total';
    setPaymentOption(option);
    if (card) {
      if (option === 'minimum') {
        form.setValue('amount', card.minimumPayment, { shouldValidate: true });
      } else if (option === 'total') {
        form.setValue('amount', card.currentBalance, { shouldValidate: true });
      } else {
        form.setValue('amount', undefined, { shouldValidate: true });
      }
    }
  };

  const handleSubmit = async (data: PaymentFormData) => {
    await onSubmit(data);
  };

  if (!card) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Aplicar Pago a {card.name}</DialogTitle>
          <DialogDescription>
            El balance actual es {formatCurrency(card.currentBalance, card.currency)}.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 py-4">
            <FormItem>
              <span className="font-medium text-finanz-text-primary">Tipo de Pago</span>
              <RadioGroup
                value={paymentOption}
                onValueChange={handlePaymentOptionChange}
                className="pt-2 space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="minimum" id="payment-minimum" />
                  <Label htmlFor="payment-minimum" className="font-normal cursor-pointer">
                    Pago Mínimo ({formatCurrency(card.minimumPayment, card.currency)})
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="total" id="payment-total" />
                  <Label htmlFor="payment-total" className="font-normal cursor-pointer">
                    Pago Total ({formatCurrency(card.currentBalance, card.currency)})
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="custom" id="payment-custom" />
                  <Label htmlFor="payment-custom" className="font-normal cursor-pointer">
                    Otro Monto
                  </Label>
                </div>
              </RadioGroup>
            </FormItem>

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Monto del Pago</FormLabel>
                  <FormControl>
                    <NumericInput
                      placeholder="0.00"
                      currency={card.currency}
                      value={field.value}
                      onChange={field.onChange}
                      onBlur={field.onBlur}
                      disabled={paymentOption !== 'custom'}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="paidDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Fecha de Pago</FormLabel>
                  <FormControl>
                    <DatePicker date={field.value} onSelect={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notas (Opcional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Ej: Pago de la cuota mensual" {...field} value={field.value || ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Aplicar Pago
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
