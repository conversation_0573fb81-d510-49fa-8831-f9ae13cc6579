import React from 'react';
import { StatCard } from './cards/StatCard';
import type { LucideIcon } from 'lucide-react';

export interface StatCardData {
  icon: LucideIcon;
  title: string;
  value: string | number;
  subtitle: string;
  color: string;
}

interface PaymentStatsCardsProps {
  stats: StatCardData[];
}

const PaymentStatsCardsComponent: React.FC<PaymentStatsCardsProps> = ({ stats }) => {
  const numStats = stats.length;

  const getGridClasses = () => {
    if (numStats === 0) return "grid"; // Or handle empty state if necessary

    let classes = "grid gap-4 grid-cols-1 sm:grid-cols-2 "; // Base for mobile and small tablets

    // Medium screens (md) and up
    if (numStats === 1) {
      classes += "md:grid-cols-1";
    } else if (numStats === 2) {
      classes += "md:grid-cols-2";
    } else if (numStats === 3) {
      classes += "md:grid-cols-3";
    } else if (numStats === 4) {
      classes += "md:grid-cols-4"; // 4 cards in a row on md and larger
    } else if (numStats >= 5) {
      // For 5 or more cards:
      // md: 3 columns (e.g., 3 on top, 2 below for 5 cards)
      // lg: 5 columns (e.g., 5 in a row for 5 cards)
      classes += "md:grid-cols-3 lg:grid-cols-5";
    }
    return classes;
  };

  return (
    <div className={getGridClasses()}>
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

export const PaymentStatsCards = React.memo(PaymentStatsCardsComponent);
