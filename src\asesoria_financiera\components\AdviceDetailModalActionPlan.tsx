
import React from 'react';
import { Calendar } from 'lucide-react';

interface ActionStep {
  title: string;
  description: string;
  timeframe: string;
}

interface AdviceDetailModalActionPlanProps {
  overview: string;
  steps: ActionStep[];
}

export const AdviceDetailModalActionPlan: React.FC<AdviceDetailModalActionPlanProps> = ({
  overview,
  steps
}) => {
  return (
    <div>
      <h4 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <Calendar className="w-5 h-5 text-finanz-primary" />
        Plan de Acción Detallado
      </h4>
      <p className="text-finanz-text-secondary mb-4">{overview}</p>
      
      <div className="space-y-3">
        {steps.map((step, index) => (
          <div key={index} className="flex gap-4 p-4 border rounded-lg">
            <div className="bg-finanz-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
              {index + 1}
            </div>
            <div className="flex-1">
              <h5 className="font-medium mb-1">{step.title}</h5>
              <p className="text-sm text-finanz-text-secondary mb-2">{step.description}</p>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-finanz-text-secondary" />
                <span className="text-xs text-finanz-text-secondary">{step.timeframe}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
