import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { DashboardTabs } from '@/dashboard_principal/components/DashboardTabs'

// mock sub components
vi.mock('@/dashboard_principal/components/tabs/SummaryTab', () => ({ SummaryTab: () => <div>SummaryTab</div> }))
vi.mock('@/dashboard_principal/components/tabs/AnalysisTab', () => ({ AnalysisTab: () => <div>AnalysisTab</div> }))
vi.mock('@/dashboard_principal/components/tabs/CashFlowTab', () => ({ CashFlowTab: () => <div>CashFlowTab</div> }))
vi.mock('@/dashboard_principal/components/tabs/InsightsTab', () => ({ InsightsTab: () => <div>InsightsTab</div> }))
vi.mock('@/dashboard_principal/components/DashboardCustomizer', () => ({ DashboardCustomizer: ({isOpen}: {isOpen:boolean}) => isOpen ? <div>Customizer</div> : null }))

// mock breakpoint hook
vi.mock('@/hooks/useBreakpoint', () => ({ useBreakpoint: () => ({ isMobile: false, isTablet: false }) }))

describe('DashboardTabs', () => {
  it('renders and opens customizer', async () => {
    render(<DashboardTabs />)

    // Summary tab visible by default
    expect(screen.getByText('SummaryTab')).toBeInTheDocument()

    // open customizer
    fireEvent.click(screen.getByRole('button', { name: /personalizar/i }))
    expect(screen.getByText('Customizer')).toBeInTheDocument()
  })
})
