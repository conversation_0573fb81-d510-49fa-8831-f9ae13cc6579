import { addMonths, format } from 'date-fns';
import { PaymentItem } from '../types/paymentTypes';

export interface PaymentProjection {
  id: string;
  name: string;
  amount: number;
  currency: 'DOP' | 'USD';
  projectedDate: string;
  type: string;
  confidence: number; // 0-100
  basePattern: 'monthly' | 'weekly' | 'yearly' | 'irregular';
  sourcePayment?: PaymentItem;
}

export interface ProjectionAnalysis {
  projections: PaymentProjection[];
  totalProjected: number;
  confidenceScore: number;
  patternsSummary: {
    monthly: number;
    weekly: number;
    yearly: number;
    irregular: number;
  };
  recommendations: string[];
}

class PaymentProjectionService {
  
  generateProjections(
    historicalPayments: PaymentItem[], 
    targetMonths: number = 6
  ): ProjectionAnalysis {
    const projections: PaymentProjection[] = [];
    const patterns = this.analyzePaymentPatterns(historicalPayments);
    
    // Generar proyecciones basadas en patrones identificados
    patterns.forEach(pattern => {
      const monthlyProjections = this.generateMonthlyProjections(pattern, targetMonths);
      projections.push(...monthlyProjections);
    });

    const totalProjected = projections.reduce((sum, p) => sum + p.amount, 0);
    const confidenceScore = this.calculateOverallConfidence(projections);
    
    return {
      projections,
      totalProjected,
      confidenceScore,
      patternsSummary: this.summarizePatterns(projections),
      recommendations: this.generateRecommendations(projections, patterns)
    };
  }

  private analyzePaymentPatterns(payments: PaymentItem[]) {
    const paymentGroups = this.groupPaymentsByName(payments);
    const patterns = [];

    for (const [name, paymentHistory] of paymentGroups.entries()) {
      if (paymentHistory.length < 2) continue;

      const pattern = this.identifyPattern(name, paymentHistory);
      if (pattern.confidence > 60) {
        patterns.push(pattern);
      }
    }

    return patterns;
  }

  private groupPaymentsByName(payments: PaymentItem[]): Map<string, PaymentItem[]> {
    const groups = new Map<string, PaymentItem[]>();
    
    payments.forEach(payment => {
      const key = payment.name.toLowerCase().trim();
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(payment);
    });

    return groups;
  }

  private identifyPattern(name: string, payments: PaymentItem[]) {
    const sortedPayments = payments.sort((a, b) => 
      new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
    );

    // Analizar intervalos entre pagos
    const intervals = [];
    for (let i = 1; i < sortedPayments.length; i++) {
      const prev = new Date(sortedPayments[i - 1].dueDate);
      const curr = new Date(sortedPayments[i].dueDate);
      const daysDiff = Math.abs(curr.getTime() - prev.getTime()) / (1000 * 60 * 60 * 24);
      intervals.push(Math.round(daysDiff));
    }

    // Determinar patrón más probable
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = this.calculateVariance(intervals, avgInterval);
    
    let basePattern: 'monthly' | 'weekly' | 'yearly' | 'irregular';
    let confidence = 0;

    if (Math.abs(avgInterval - 30) <= 5 && variance < 50) {
      basePattern = 'monthly';
      confidence = 85;
    } else if (Math.abs(avgInterval - 7) <= 2 && variance < 10) {
      basePattern = 'weekly';
      confidence = 80;
    } else if (Math.abs(avgInterval - 365) <= 30 && variance < 100) {
      basePattern = 'yearly';
      confidence = 75;
    } else {
      basePattern = 'irregular';
      confidence = Math.max(30, 70 - variance);
    }

    const avgAmount = payments.reduce((sum, p) => sum + p.amount, 0) / payments.length;
    const mostRecentPayment = sortedPayments[sortedPayments.length - 1];

    return {
      name,
      basePattern,
      confidence,
      avgAmount,
      avgInterval,
      lastPayment: mostRecentPayment,
      currency: mostRecentPayment.currency,
      type: mostRecentPayment.type
    };
  }

  private calculateVariance(values: number[], mean: number): number {
    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    return squaredDifferences.reduce((sum, sq) => sum + sq, 0) / values.length;
  }

  private generateMonthlyProjections(pattern: any, targetMonths: number): PaymentProjection[] {
    const projections: PaymentProjection[] = [];
    const startDate = new Date();

    for (let month = 1; month <= targetMonths; month++) {
      const projectedDate = addMonths(startDate, month);
      
      let shouldInclude = false;
      
      switch (pattern.basePattern) {
        case 'monthly':
          shouldInclude = true;
          break;
        case 'weekly':
          // Incluir solo si el patrón semanal cae en este mes
          shouldInclude = Math.random() > 0.3; // Simplificado
          break;
        case 'yearly': {
          // Solo incluir si es el mes correspondiente del año
          const lastPaymentMonth = new Date(pattern.lastPayment.dueDate).getMonth();
          shouldInclude = projectedDate.getMonth() === lastPaymentMonth;
          break;
        }
        case 'irregular':
          // Probabilidad basada en confianza
          shouldInclude = Math.random() < (pattern.confidence / 100);
          break;
      }

      if (shouldInclude) {
        projections.push({
          id: `proj_${pattern.name}_${month}`,
          name: pattern.name,
          amount: pattern.avgAmount,
          currency: pattern.currency,
          projectedDate: format(projectedDate, 'yyyy-MM-dd'),
          type: pattern.type,
          confidence: pattern.confidence,
          basePattern: pattern.basePattern,
          sourcePayment: pattern.lastPayment
        });
      }
    }

    return projections;
  }

  private calculateOverallConfidence(projections: PaymentProjection[]): number {
    if (projections.length === 0) return 0;
    
    const totalConfidence = projections.reduce((sum, p) => sum + p.confidence, 0);
    return Math.round(totalConfidence / projections.length);
  }

  private summarizePatterns(projections: PaymentProjection[]) {
    return {
      monthly: projections.filter(p => p.basePattern === 'monthly').length,
      weekly: projections.filter(p => p.basePattern === 'weekly').length,
      yearly: projections.filter(p => p.basePattern === 'yearly').length,
      irregular: projections.filter(p => p.basePattern === 'irregular').length
    };
  }

  private generateRecommendations(projections: PaymentProjection[], patterns: any[]): string[] {
    const recommendations = [];

    // Recomendación basada en confianza
    const lowConfidenceCount = projections.filter(p => p.confidence < 70).length;
    if (lowConfidenceCount > 0) {
      recommendations.push(
        `${lowConfidenceCount} proyección(es) tienen baja confianza. Considera revisar los patrones de pago.`
      );
    }

    // Recomendación por cantidad de pagos irregulares
    const irregularCount = projections.filter(p => p.basePattern === 'irregular').length;
    if (irregularCount > 3) {
      recommendations.push(
        'Tienes varios pagos con patrones irregulares. Considera establecer fechas fijas para mejorar la predicción.'
      );
    }

    // Recomendación por total proyectado alto
    const monthlyAvg = projections.reduce((sum, p) => sum + p.amount, 0) / 6;
    if (monthlyAvg > 50000) {
      recommendations.push(
        'Las proyecciones indican pagos altos los próximos meses. Planifica tu flujo de caja con anticipación.'
      );
    }

    return recommendations;
  }
}

export const paymentProjectionService = new PaymentProjectionService();
