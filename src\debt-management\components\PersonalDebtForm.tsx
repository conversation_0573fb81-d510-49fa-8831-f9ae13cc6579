import React, { useState, useEffect } from 'react';
import { logger } from '@/utils/logger';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { NumericInput } from '@/components/ui/numeric-input';
import { PersonalDebt } from '@/types';

interface PersonalDebtFormProps {
  onSubmit: (debt: Omit<PersonalDebt, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  editingDebt?: PersonalDebt | null;
}

export function PersonalDebtForm({ onSubmit, onCancel, editingDebt }: PersonalDebtFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    currency: 'DOP' as 'DOP' | 'USD',
    amount: 0,
    remainingBalance: 0,
    paymentDate: new Date().toISOString().split('T')[0],
    monthlyBudget: 0,
    isActive: true
  });

  // Inicializar el formulario con los datos de edición
  useEffect(() => {
    if (editingDebt) {
      logger.debug('Loading debt data for editing:', editingDebt);
      setFormData({
        name: editingDebt.name,
        currency: editingDebt.currency,
        amount: editingDebt.amount,
        remainingBalance: editingDebt.remainingBalance !== undefined ? editingDebt.remainingBalance : editingDebt.amount,
        paymentDate: editingDebt.paymentDate,
        monthlyBudget: editingDebt.monthlyBudget || 0,
        isActive: editingDebt.isActive
      });
    } else {
      // Reset form for new debt
      setFormData({
        name: '',
        currency: 'DOP',
        amount: 0,
        remainingBalance: 0,
        paymentDate: new Date().toISOString().split('T')[0],
        monthlyBudget: 0,
        isActive: true
      });
    }
  }, [editingDebt]);

  // Sincronizar remainingBalance con amount cuando se crea una nueva deuda
  useEffect(() => {
    if (!editingDebt && formData.amount > 0) {
      setFormData(prev => ({
        ...prev,
        remainingBalance: prev.amount
      }));
    }
  }, [formData.amount, editingDebt]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || formData.amount <= 0) {
      logger.error('Form validation failed:', { name: formData.name, amount: formData.amount });
      return;
    }

    logger.debug('Submitting form data:', formData);
    onSubmit(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {editingDebt ? 'Editar Deuda Personal' : 'Nueva Deuda Personal'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre de la deuda</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ej: Préstamo personal Juan"
                required
                autoComplete="name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Moneda</Label>
              <Select 
                value={formData.currency} 
                onValueChange={(value: 'DOP' | 'USD') => setFormData({ ...formData, currency: value })}
              >
                <SelectTrigger id="currency" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOP">DOP (Pesos Dominicanos)</SelectItem>
                  <SelectItem value="USD">USD (Dólares)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Monto total</Label>
              <NumericInput
                id="amount"
                value={formData.amount}
                onChange={(value) => setFormData({ ...formData, amount: value || 0 })}
                currency={formData.currency}
                placeholder="0.00"
                min={0}
                autoComplete="off"
              />
            </div>

            {editingDebt && (
              <div className="space-y-2">
                <Label htmlFor="remainingBalance">Saldo restante</Label>
                <NumericInput
                  id="remainingBalance"
                  value={formData.remainingBalance}
                  onChange={(value) => setFormData({ ...formData, remainingBalance: value || 0 })}
                  currency={formData.currency}
                  placeholder="0.00"
                  min={0}
                  autoComplete="off"
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="monthlyBudget">Presupuesto mensual</Label>
              <NumericInput
                id="monthlyBudget"
                value={formData.monthlyBudget}
                onChange={(value) => setFormData({ ...formData, monthlyBudget: value || 0 })}
                currency={formData.currency}
                placeholder="0.00"
                min={0}
                autoComplete="off"
              />
              <p className="text-sm text-gray-500">
                Monto que planeas abonar mensualmente a esta deuda
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentDate">Fecha de próximo abono</Label>
              <Input
                id="paymentDate"
                type="date"
                value={formData.paymentDate}
                onChange={(e) => setFormData({ ...formData, paymentDate: e.target.value })}
                required
                autoComplete="off"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">Deuda activa</Label>
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1">
              {editingDebt ? 'Actualizar' : 'Agregar'} Deuda
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
