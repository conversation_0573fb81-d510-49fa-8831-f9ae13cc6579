
-- FASE 2: <PERSON><PERSON><PERSON><PERSON> DE METAS FINANCIERAS
-- Las contribuciones a las metas se guardaban localmente, lo que causaba
-- inconsistencias. Esta fase mueve las contribuciones a la base de datos
-- y asegura que las actualizaciones sean atómicas (todo o nada).

-- <PERSON><PERSON>, creamos una tabla para almacenar cada contribución individualmente.
-- Se conecta con la meta y el usuario correspondiente.
CREATE TABLE public.goal_contributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    goal_id UUID NOT NULL REFERENCES public.financial_goals(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    amount NUMERIC NOT NULL CHECK (amount > 0),
    date TIMESTAMPTZ NOT NULL DEFAULT now(),
    note TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Luego, aplicamos seguridad para que cada usuario solo vea y gestione sus propias contribuciones.
ALTER TABLE public.goal_contributions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own contributions"
ON public.goal_contributions FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own contributions"
ON public.goal_contributions FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contributions"
ON public.goal_contributions FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contributions"
ON public.goal_contributions FOR DELETE
USING (auth.uid() = user_id);

-- Finalmente, creamos una función especial (RPC).
-- Cuando se agrega una contribución, esta función hace dos cosas a la vez:
-- 1. Guarda la nueva contribución en la tabla `goal_contributions`.
-- 2. Actualiza el `monto actual` en la tabla `financial_goals`.
-- Esto garantiza que los datos siempre sean consistentes.
CREATE OR REPLACE FUNCTION public.add_goal_contribution(
    p_goal_id UUID,
    p_user_id UUID,
    p_amount NUMERIC,
    p_note TEXT
)
RETURNS VOID AS $$
BEGIN
    -- Insertar la nueva contribución
    INSERT INTO public.goal_contributions(goal_id, user_id, amount, note, date)
    VALUES (p_goal_id, p_user_id, p_amount, p_note, now());

    -- Actualizar el monto actual en la meta financiera
    UPDATE public.financial_goals
    SET current_amount = current_amount + p_amount,
        updated_at = now()
    WHERE id = p_goal_id AND user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Añadir un trigger para actualizar el campo 'updated_at' automáticamente
CREATE TRIGGER handle_updated_at_goal_contributions
BEFORE UPDATE ON public.goal_contributions
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();
