import { logger } from "@/utils/logger";

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { strongPasswordSchema, calculatePasswordStrength } from '@/utils/security/passwordValidation';
import { sanitizeInput } from '@/utils/security/sanitization';
import { emailSecuritySchema } from '@/utils/security/validationSchemas';
import { rateLimiter } from '@/utils/security/rateLimiter';
import { ZodError } from 'zod';

// Simple password compromise check function
const checkPasswordCompromise = async (password: string): Promise<boolean> => {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-1', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('').toUpperCase();
    
    const prefix = hashHex.slice(0, 5);
    const suffix = hashHex.slice(5);
    
    const response = await fetch(`https://api.pwnedpasswords.com/range/${prefix}`);
    if (!response.ok) return false;
    
    const hashes = await response.text();
    return hashes.includes(suffix);
  } catch (error) {
    console.warn('Could not check password compromise status:', error);
    return false;
  }
};

export const useSecureSignupForm = (isLoading: boolean, setIsLoading: (loading: boolean) => void) => {
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: '', feedback: [] });
  const [isPasswordCompromised, setIsPasswordCompromised] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [signupData, setSignupData] = useState({
    fullName: '',
    email: '',
    phone: '',
    profession: '',
    password: '',
    confirmPassword: ''
  });

  // Real-time password strength checking
  useEffect(() => {
    if (signupData.password) {
      const strength = calculatePasswordStrength(signupData.password);
      setPasswordStrength(strength);
      
      // Check if password is compromised (debounced)
      const timeoutId = setTimeout(async () => {
        if (signupData.password.length >= 8) {
          const compromised = await checkPasswordCompromise(signupData.password);
          setIsPasswordCompromised(compromised);
        }
      }, 1000);
      
      return () => clearTimeout(timeoutId);
    } else {
      setPasswordStrength({ score: 0, label: '', color: '', feedback: [] });
      setIsPasswordCompromised(false);
    }
  }, [signupData.password]);

  const validateInput = (field: string, value: string): string | null => {
    try {
      switch (field) {
        case 'email':
          emailSecuritySchema.parse(value);
          return null;
        case 'password':
          strongPasswordSchema.parse(value);
          return null;
        case 'fullName':
          if (!value.trim() || value.length < 2) return 'Nombre requerido (mínimo 2 caracteres)';
          if (value.length > 100) return 'Nombre demasiado largo';
          if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/.test(value)) return 'Nombre contiene caracteres inválidos';
          return null;
        case 'phone':
          if (value && !/^[\d\s+\-()]+$/.test(value)) return 'Formato de teléfono inválido';
          return null;
        case 'profession':
          if (value && value.length > 100) return 'Profesión demasiado larga';
          return null;
        default:
          return null;
      }
    } catch (error) {
      if (error instanceof ZodError) {
        return error.errors?.[0]?.message || 'Valor inválido';
      }
      return 'Valor inválido';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    const sanitizedValue = sanitizeInput(value);
    
    setSignupData(prev => ({ ...prev, [field]: sanitizedValue }));
    
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    if (sanitizedValue) {
      const error = validateInput(field, sanitizedValue);
      if (error) {
        setValidationErrors(prev => ({ ...prev, [field]: error }));
      }
    }
  };

  const handleSecureSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!rateLimiter.checkLimit('signup', 3, 300000)) {
      toast.error('Demasiados intentos de registro. Espera 5 minutos.');
      return;
    }

    const errors: {[key: string]: string} = {};
    
    Object.entries(signupData).forEach(([field, value]) => {
      if (field === 'phone' || field === 'profession') return;
      
      if (!value.trim()) {
        errors[field] = 'Campo requerido';
        return;
      }
      
      const error = validateInput(field, value);
      if (error) errors[field] = error;
    });

    if (signupData.password !== signupData.confirmPassword) {
      errors.confirmPassword = 'Las contraseñas no coinciden';
    }

    if (passwordStrength.score < 4) { // Reducido de 5 a 4 para aceptar contraseñas de 8 caracteres
      errors.password = 'La contraseña no cumple con los requisitos de seguridad';
    }

    if (isPasswordCompromised) {
      errors.password = 'Esta contraseña ha sido comprometida. Por favor elige otra.';
    }

    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      toast.error('Por favor corrige los errores antes de continuar');
      return;
    }

    setIsLoading(true);

    try {
      logger.debug('SecureSignupForm: Starting secure signup process');
      
      const { data, error } = await supabase.auth.signUp({
        email: signupData.email,
        password: signupData.password,
        options: {
          data: {
            full_name: signupData.fullName,
            phone: signupData.phone || null,
            profession: signupData.profession || null,
          }
        }
      });

      if (error) {
        console.error('SecureSignupForm: Signup error:', error);
        
        if (error.message.includes('already registered')) {
          toast.error('Este email ya está registrado');
        } else if (error.message.includes('rate limit')) {
          toast.error('Demasiados intentos. Inténtalo más tarde.');
        } else {
          toast.error('Error al crear la cuenta. Verifica tus datos.');
        }
        return;
      }

      if (data.user) {
        logger.debug('SecureSignupForm: Signup successful');
        rateLimiter.clearAttempts('signup');
        toast.success('¡Cuenta creada exitosamente! Revisa tu email para confirmar.');
        
        setSignupData({
          fullName: '',
          email: '',
          phone: '',
          profession: '',
          password: '',
          confirmPassword: ''
        });
      }
    } catch (error) {
      console.error('SecureSignupForm: Unexpected error:', error);
      toast.error('Error de conexión. Verifica tu internet.');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signupData,
    passwordStrength,
    isPasswordCompromised,
    validationErrors,
    handleInputChange,
    handleSecureSignUp
  };
};
