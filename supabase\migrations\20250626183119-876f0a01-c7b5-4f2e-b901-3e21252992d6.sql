
-- Agregar índices para optimizar consultas temporales en payment_records
CREATE INDEX IF NOT EXISTS idx_payment_records_user_date ON payment_records(user_id, due_date);
CREATE INDEX IF NOT EXISTS idx_payment_records_user_paid_date ON payment_records(user_id, paid_date) WHERE paid_date IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_payment_records_user_type_date ON payment_records(user_id, payment_type, due_date);

-- <PERSON>rear tabla para proyecciones de pagos futuros
CREATE TABLE IF NOT EXISTS payment_projections (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  reference_id UUID NOT NULL,
  payment_type TEXT NOT NULL,
  projected_date DATE NOT NULL,
  amount NUMERIC NOT NULL DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'DOP',
  recurrence_pattern TEXT, -- 'monthly', 'quarterly', 'yearly', 'custom'
  recurrence_metadata JSONB DEFAULT '{}'::jsonb,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Índices para payment_projections
CREATE INDEX IF NOT EXISTS idx_payment_projections_user_date ON payment_projections(user_id, projected_date);
CREATE INDEX IF NOT EXISTS idx_payment_projections_user_type ON payment_projections(user_id, payment_type);
CREATE INDEX IF NOT EXISTS idx_payment_projections_active ON payment_projections(user_id, is_active) WHERE is_active = true;

-- Tabla para tracking de cambios (auditoría)
CREATE TABLE IF NOT EXISTS payment_audit_log (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  payment_record_id UUID,
  action TEXT NOT NULL, -- 'created', 'updated', 'deleted', 'marked_paid', 'unmarked'
  old_data JSONB,
  new_data JSONB,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Índice para audit log
CREATE INDEX IF NOT EXISTS idx_payment_audit_log_user_date ON payment_audit_log(user_id, created_at);

-- Función para generar proyecciones de pagos recurrentes
CREATE OR REPLACE FUNCTION generate_payment_projections(
  p_user_id UUID,
  p_months_ahead INTEGER DEFAULT 6
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  current_date DATE := CURRENT_DATE;
  end_date DATE := current_date + (p_months_ahead || ' months')::INTERVAL;
  rec RECORD;
  projection_date DATE;
BEGIN
  -- Limpiar proyecciones existentes para este usuario
  DELETE FROM payment_projections 
  WHERE user_id = p_user_id 
  AND projected_date > current_date;

  -- Generar proyecciones para tarjetas de crédito
  FOR rec IN 
    SELECT id, name, minimum_payment, currency, 
           EXTRACT(DAY FROM payment_due_date) as payment_day
    FROM credit_cards 
    WHERE user_id = p_user_id AND is_active = true
  LOOP
    projection_date := current_date;
    WHILE projection_date <= end_date LOOP
      projection_date := DATE_TRUNC('month', projection_date) + INTERVAL '1 month' + (rec.payment_day - 1 || ' days')::INTERVAL;
      
      IF projection_date <= end_date THEN
        INSERT INTO payment_projections (
          user_id, reference_id, payment_type, projected_date, 
          amount, currency, recurrence_pattern
        ) VALUES (
          p_user_id, rec.id, 'credit-card', projection_date,
          rec.minimum_payment, rec.currency, 'monthly'
        );
      END IF;
    END LOOP;
  END LOOP;

  -- Generar proyecciones para préstamos
  FOR rec IN 
    SELECT id, name, monthly_payment, currency, payment_date
    FROM loans 
    WHERE user_id = p_user_id AND is_active = true
  LOOP
    projection_date := current_date;
    WHILE projection_date <= end_date LOOP
      projection_date := DATE_TRUNC('month', projection_date) + INTERVAL '1 month' + (EXTRACT(DAY FROM rec.payment_date::DATE) - 1 || ' days')::INTERVAL;
      
      IF projection_date <= end_date THEN
        INSERT INTO payment_projections (
          user_id, reference_id, payment_type, projected_date, 
          amount, currency, recurrence_pattern
        ) VALUES (
          p_user_id, rec.id, 'loan', projection_date,
          rec.monthly_payment, rec.currency, 'monthly'
        );
      END IF;
    END LOOP;
  END LOOP;

  -- Generar proyecciones para suscripciones
  FOR rec IN 
    SELECT id, name, amount, currency, billing_date::INTEGER as billing_day
    FROM subscriptions 
    WHERE user_id = p_user_id AND is_active = true
  LOOP
    projection_date := current_date;
    WHILE projection_date <= end_date LOOP
      projection_date := DATE_TRUNC('month', projection_date) + INTERVAL '1 month' + (rec.billing_day - 1 || ' days')::INTERVAL;
      
      IF projection_date <= end_date THEN
        INSERT INTO payment_projections (
          user_id, reference_id, payment_type, projected_date, 
          amount, currency, recurrence_pattern
        ) VALUES (
          p_user_id, rec.id, 'subscription', projection_date,
          rec.amount, rec.currency, 'monthly'
        );
      END IF;
    END LOOP;
  END LOOP;

END;
$$;

-- Trigger para actualizar updated_at en payment_projections
CREATE OR REPLACE TRIGGER update_payment_projections_updated_at
    BEFORE UPDATE ON payment_projections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para logging de cambios en payment_records
CREATE OR REPLACE FUNCTION log_payment_record_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO payment_audit_log (user_id, payment_record_id, action, new_data)
    VALUES (NEW.user_id, NEW.id, 'created', row_to_json(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO payment_audit_log (user_id, payment_record_id, action, old_data, new_data)
    VALUES (NEW.user_id, NEW.id, 'updated', row_to_json(OLD), row_to_json(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO payment_audit_log (user_id, payment_record_id, action, old_data)
    VALUES (OLD.user_id, OLD.id, 'deleted', row_to_json(OLD));
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$;

-- Crear trigger para audit log
DROP TRIGGER IF EXISTS payment_record_audit_trigger ON payment_records;
CREATE TRIGGER payment_record_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON payment_records
    FOR EACH ROW
    EXECUTE FUNCTION log_payment_record_changes();
