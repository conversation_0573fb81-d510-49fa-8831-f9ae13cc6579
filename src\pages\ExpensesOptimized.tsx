import { TrendingDown } from 'lucide-react';
import { useOptimizedFinanceData } from '@/hooks/useOptimizedFinanceData';
import { getAmountInDOP } from '@/components/ui/numeric-input';
import { ExpenseForm } from './expenses/components/ExpenseForm';
import { ExpenseStatsCards } from './expenses/components/ExpenseStatsCards';
import { ExpenseCategoryChart } from './expenses/components/ExpenseCategoryChart';
import { ExpenseList } from './expenses/components/ExpenseList';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { Expense } from '@/types'; // Assuming Expense type is available in @/types

export default function ExpensesOptimized() {
  // Solo cargar datos de gastos con cache optimizado
  const { expenses, addExpense, updateExpense, deleteExpense, defaultCurrency, isLoading } = useOptimizedFinanceData(['expenses']);
  const { isMobile } = useBreakpoint();
  
  // Optimizar clases de contenedor para scroll móvil
  const containerClasses = `space-y-4 md:space-y-6 ${
    isMobile 
      ? 'p-3 h-[calc(100vh-120px)] overflow-y-auto overflow-x-hidden' 
      : 'p-6 h-[calc(100vh-140px)] overflow-y-auto'
  }`;

  // Si está cargando por primera vez, mostrar un skeleton rápido
  if (isLoading && expenses.length === 0) {
    return (
      <div className={`animate-pulse ${containerClasses}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className={`bg-gray-200 rounded mb-2 ${isMobile ? 'h-6 w-32' : 'h-8 w-64'}`}></div>
            <div className={`bg-gray-200 rounded ${isMobile ? 'h-3 w-24' : 'h-4 w-48'}`}></div>
          </div>
          <div className={`bg-gray-200 rounded ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`}></div>
        </div>
        <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-4'}`}>
          {[1,2,3,4].map(i => (
            <div key={i} className={`bg-gray-200 rounded-lg ${isMobile ? 'h-20' : 'h-24'}`}></div>
          ))}
        </div>
      </div>
    );
  }

  // Calcular estadísticas del mes actual
  const currentMonth = new Date().toISOString().slice(0, 7);
  const currentMonthExpenses = expenses.filter(expense => {
    // 1. Si el campo month coincide, perfecto
    if (expense.month === currentMonth) return true;

    // 2. Si no tiene month, derivar del campo date
    if (!expense.month && expense.date && expense.date.startsWith(currentMonth)) return true;

    // 3. Si es recurrente, siempre se incluye porque se asume mensual
    if (expense.isRecurring) {
      return true;
    }

    return false;
  });
  
  const totalExpensesMonth = currentMonthExpenses.reduce((total, expense) => 
    total + getAmountInDOP(expense.amount, expense.currency), 0
  );

  const expensesByCategory = currentMonthExpenses.reduce((acc, expense) => {
    const amountInDOP = getAmountInDOP(expense.amount, expense.currency);
    acc[expense.categoryId] = (acc[expense.categoryId] || 0) + amountInDOP;
    return acc;
  }, {} as { [key: string]: number });

  const fixedExpenses = currentMonthExpenses
    .filter(expense => expense.type === 'Fijo')
    .reduce((total, expense) => total + getAmountInDOP(expense.amount, expense.currency), 0);

  const variableExpenses = currentMonthExpenses
    .filter(expense => expense.type === 'Variable')
    .reduce((total, expense) => total + getAmountInDOP(expense.amount, expense.currency), 0);

  // Wrapper function to handle the addExpense mutation properly
  const handleAddExpense = async (expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await addExpense(expense);
    } catch (error) {
      console.error('Error adding expense:', error);
    }
  };

  return (
    <div className={containerClasses}>
      <div className={`flex items-center justify-between ${isMobile ? 'flex-col space-y-2' : ''}`}>
        <div className={`${isMobile ? 'text-center' : ''}`}>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-3xl'}`}>
            {isMobile ? 'Gastos' : 'Gestión de Gastos'}
          </h1>
          <p className={`text-finanz-text-secondary ${isMobile ? 'text-xs' : ''}`}>
            {isMobile ? 'Registra y controla gastos' : 'Registra y controla tus gastos mensuales'}
          </p>
        </div>
        <TrendingDown className={`text-finanz-danger ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`} />
      </div>

      <ExpenseStatsCards
        totalExpensesMonth={totalExpensesMonth}
        fixedExpenses={fixedExpenses}
        variableExpenses={variableExpenses}
        transactionCount={currentMonthExpenses.length}
      />

      {/* Grid optimizada para escritorio y móvil */}
      <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'} w-full`}>
        <div className="w-full">
          <ExpenseForm
            onAddExpense={handleAddExpense}
            defaultCurrency={defaultCurrency}
          />
        </div>
        <div className="w-full">
          <ExpenseCategoryChart
            expensesByCategory={expensesByCategory}
            totalExpensesMonth={totalExpensesMonth}
          />
        </div>
      </div>

      {/* Lista de gastos ocupa todo el ancho con scroll optimizado */}
      <div className={`w-full ${isMobile ? 'flex-1 min-h-0' : ''}`}>
        <ExpenseList
          expenses={expenses}
          onUpdateExpense={updateExpense}
          onDeleteExpense={deleteExpense}
        />
      </div>
    </div>
  );
}
