import { useMemo } from 'react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { usePaymentsLogic } from '@/seguimiento_pagos/hooks/usePaymentsLogic';
import { useExchangeRate } from '@/hooks/useExchangeRate';

export const useDashboardPayments = () => {
  // Obtener el mes actual
  const currentDate = new Date();
  const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
  
  // Filtrar ingresos del mes actual y obtener tasa de cambio
  const { incomes } = useFinanceData(['incomes'], { currentMonth: true });
  const { totals: paymentTotals } = usePaymentsLogic();
  const { rate: exchangeRate } = useExchangeRate();

  const dashboardMetrics = useMemo(() => {
    // CORREGIDO: Calcular el ingreso neto solo del mes actual y convertir a DOP
    const currentMonthIncomes = incomes.filter(income => income.month === currentMonth);
    const netIncome = currentMonthIncomes.reduce((total, income) => {
      const amountInDOP = income.currency === 'USD' 
        ? (income.netIncome || 0) * exchangeRate
        : (income.netIncome || 0);
      return total + amountInDOP;
    }, 0);

    console.log('Dashboard Payments - Current month:', currentMonth);
    console.log('Dashboard Payments - Current month incomes:', currentMonthIncomes.length);
    console.log('Dashboard Payments - Net income:', netIncome);
    console.log('Dashboard Payments - Payment totals:', paymentTotals);
    console.log('Dashboard Payments - Exchange Rate:', exchangeRate);
    
    // Usar las propiedades correctas de PaymentTotals (ya están en DOP)
    const totalMonthlyPayments = paymentTotals.pending + paymentTotals.overdue + paymentTotals.paidThisMonth;
    const netBalance = netIncome - totalMonthlyPayments;
    const savingsRate = netIncome > 0 ? (netBalance / netIncome) * 100 : 0;
    const paymentToIncomeRatio = netIncome > 0 ? (totalMonthlyPayments / netIncome) * 100 : 0;

    return {
      netIncome,
      totalMonthlyPayments,
      netBalance,
      savingsRate,
      paymentToIncomeRatio,
      paymentBreakdown: {
        pending: paymentTotals.pending,
        overdue: paymentTotals.overdue,
        paid: paymentTotals.paidThisMonth
      }
    };
  }, [incomes, paymentTotals, currentMonth, exchangeRate]);

  return dashboardMetrics;
};
