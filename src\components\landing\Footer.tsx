
import React from 'react';
import { DollarSign, Mail, MapPin, Phone, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import { Link } from 'react-router-dom';

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white py-20 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-emerald-600/20 to-blue-600/20 rounded-full blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12">
          {/* Brand section */}
          <div className="md:col-span-5">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl flex items-center justify-center shadow-xl">
                <DollarSign className="w-8 h-8 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-black">FinanzApp</h3>
                <p className="text-gray-400 text-sm font-semibold">Tu gestor financiero inteligente</p>
              </div>
            </div>
            <p className="text-gray-300 mb-8 max-w-md text-lg leading-relaxed">
              La plataforma más completa y elegante para gestionar tus finanzas personales. 
              Toma el control de tu futuro económico con herramientas profesionales e inteligencia artificial.
            </p>
            
            {/* Social media */}
            <div className="flex space-x-4">
              {[
                { icon: Facebook, href: "#" },
                { icon: Twitter, href: "#" },
                { icon: Instagram, href: "#" },
                { icon: Linkedin, href: "#" }
              ].map(({ icon: Icon, href }, index) => (
                <a 
                  key={index}
                  href={href} 
                  className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110"
                >
                  <Icon className="w-5 h-5 text-white" />
                </a>
              ))}
            </div>
          </div>

          {/* Product links */}
          <div className="md:col-span-2">
            <h4 className="text-lg font-bold mb-6 text-white">Producto</h4>
            <ul className="space-y-4 text-gray-300">
              {[
                { label: "Características", href: "/features" },
                { label: "Dashboard", href: "/app/dashboard" },
                { label: "Gastos", href: "/app/expenses" },
                { label: "Ingresos", href: "/app/income" },
                { label: "Deudas", href: "/app/loans" },
                { label: "Metas", href: "/app/goals" }
              ].map((link, index) => (
                <li key={index}>
                  <Link to={link.href} className="hover:text-white transition-colors duration-200 hover:underline">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company links */}
          <div className="md:col-span-2">
            <h4 className="text-lg font-bold mb-6 text-white">Empresa</h4>
            <ul className="space-y-4 text-gray-300">
              {[
                { label: "Acerca de", href: "/about" },
                { label: "Blog", href: "/blog" },
                { label: "Carreras", href: "/careers" },
                { label: "Prensa", href: "/press" },
                { label: "Socios", href: "/partners" }
              ].map((link, index) => (
                <li key={index}>
                  <Link to={link.href} className="hover:text-white transition-colors duration-200 hover:underline">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal and support */}
          <div className="md:col-span-3">
            <h4 className="text-lg font-bold mb-6 text-white">Soporte</h4>
            <ul className="space-y-4 text-gray-300 mb-8">
              {[
                { label: "Centro de Ayuda", href: "/support" },
                { label: "Política de Privacidad", href: "/privacy-policy" },
                { label: "Términos de Servicio", href: "/terms-of-service" },
                { label: "Estado del Sistema", href: "/status" }
              ].map((link, index) => (
                <li key={index}>
                  <Link to={link.href} className="hover:text-white transition-colors duration-200 hover:underline">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
            
            {/* Contact info */}
            <div className="space-y-3 text-sm text-gray-400">
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4" />
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4" />
                <span>República Dominicana</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4" />
                <span>Soporte 24/7</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-gray-700 mt-16 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              © {new Date().getFullYear()} FinanzApp. Todos los derechos reservados. 
              <span className="ml-2 text-xs">Hecho con ❤️ en República Dominicana</span>
            </p>
            <div className="flex space-x-8">
              <Link to="/privacy-policy" className="text-gray-400 hover:text-white text-sm transition-colors">
                Privacidad
              </Link>
              <Link to="/terms-of-service" className="text-gray-400 hover:text-white text-sm transition-colors">
                Términos
              </Link>
              <Link to="/support" className="text-gray-400 hover:text-white text-sm transition-colors">
                Contacto
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
