import React from 'react';
import { PDFDataEngineProvider } from './core/PDFDataEngine';
import { ProfessionalTemplate } from './templates/ProfessionalTemplate';

interface OptimizedPDFReportProps {
  overallScore: number;
  userData?: Record<string, unknown>;
  format?: 'executive' | 'complete';
}

export const OptimizedPDFReport: React.FC<OptimizedPDFReportProps> = ({ 
  overallScore, 
  userData,
  format = 'complete'
}) => {
  return (
    <PDFDataEngineProvider overallScore={overallScore}>
      <ProfessionalTemplate format={format} />
    </PDFDataEngineProvider>
  );
};
