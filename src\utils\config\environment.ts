// Environment configuration with validation
interface SupabaseConfig {
  url: string;
  anonKey: string;
  projectId: string;
}

interface AppConfig {
  supabase: SupabaseConfig;
  isDevelopment: boolean;
  isProduction: boolean;
  version: string;
}

class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: AppConfig | null = null;

  private constructor() {}

  static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  getConfig(): AppConfig {
    if (!this.config) {
      this.config = this.loadConfiguration();
    }
    return this.config;
  }

  private loadConfiguration(): AppConfig {
    // Read values from the environment with sensible fallbacks
    const supabaseUrl =
      import.meta.env.VITE_SUPABASE_URL || 'https://qtxqvusyyjhylfqefzli.supabase.co';
    const supabaseAnonKey =
      import.meta.env.VITE_SUPABASE_ANON_KEY ||
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF0eHF2dXN5eWpoeWxmcWVmemxpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0MzExOTcsImV4cCI6MjA2NDAwNzE5N30.PJe7Gdv-bSPjMo88ypNEpZ8C1nJC6sWzF2Xa9S4asxw';
    const projectId =
      import.meta.env.VITE_SUPABASE_PROJECT_ID || 'qtxqvusyyjhylfqefzli';

    if (!supabaseUrl) {
      throw new Error(
        'Configuration Faltante: The VITE_SUPABASE_URL environment variable is not defined. Please configure it to connect with Supabase. You can find it in the Supabase project settings.'
      );
    }
    if (!supabaseAnonKey) {
      throw new Error(
        'Configuration Faltante: The VITE_SUPABASE_ANON_KEY environment variable is not defined. Please configure it to connect with Supabase. You can find it in the API section of your project.'
      );
    }
    if (!projectId) {
      throw new Error(
        'Configuration Faltante: The VITE_SUPABASE_PROJECT_ID environment variable is not defined. Please configure it to connect with Supabase. It is part of your project URL.'
      );
    }

    // Validate required configuration
    this.validateConfiguration({
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      projectId: projectId
    });

    return {
      supabase: {
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        projectId: projectId
      },
      isDevelopment: import.meta.env.DEV,
      isProduction: import.meta.env.PROD,
      version: '1.0.0'
    };
  }

  private validateConfiguration(supabase: SupabaseConfig): void {
    const errors: string[] = [];

    if (!supabase.url || !supabase.url.startsWith('https://')) {
      errors.push('Invalid Supabase URL');
    }

    if (!supabase.anonKey || supabase.anonKey.length < 50) {
      errors.push('Invalid Supabase anon key');
    }

    if (!supabase.projectId || supabase.projectId.length < 10) {
      errors.push('Invalid Supabase project ID');
    }

    if (errors.length > 0) {
      console.error('Configuration validation failed:', errors);
      throw new Error(`Configuration errors: ${errors.join(', ')}`);
    }
  }

  // Security headers for enhanced protection
  getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
    };
  }
}

export const configManager = ConfigurationManager.getInstance();
export type { AppConfig, SupabaseConfig };
