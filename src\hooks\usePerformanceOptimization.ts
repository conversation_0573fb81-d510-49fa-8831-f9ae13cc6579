import { useEffect, useCallback, useMemo, useRef } from 'react';
import { debounce, throttle, deferExecution, getAdaptiveConfig } from '@/utils/performance';

/**
 * Hook para optimizar el rendimiento de componentes pesados
 */
export const usePerformanceOptimization = () => {
  const config = useMemo(() => getAdaptiveConfig(), []);
  
  // Debounce optimizado
  const createDebouncedCallback = useCallback(
    <T extends (...args: any[]) => any>(fn: T, delay?: number) => {
      return debounce(fn, delay || config.debounceDelay);
    },
    [config.debounceDelay]
  );
  
  // Throttle optimizado
  const createThrottledCallback = useCallback(
    <T extends (...args: any[]) => any>(fn: T, limit = 100) => {
      return throttle(fn, limit);
    },
    []
  );
  
  // Ejecución diferida
  const createDeferredCallback = useCallback(
    <T extends (...args: any[]) => any>(fn: T) => {
      return deferExecution(fn);
    },
    []
  );
  
  return {
    config,
    createDebouncedCallback,
    createThrottledCallback,
    createDeferredCallback,
  };
};

/**
 * Hook para lazy loading de datos pesados
 */
export const useLazyData = <T>(
  fetchFn: () => Promise<T>,
  dependencies: any[] = [],
  options: {
    enabled?: boolean;
    delay?: number;
  } = {}
) => {
  const { enabled = true, delay = 0 } = options;
  const dataRef = useRef<T | null>(null);
  const loadingRef = useRef(false);
  const errorRef = useRef<Error | null>(null);
  const depsRef = useRef(dependencies);

  // Actualizar referencia de dependencias
  useEffect(() => {
    depsRef.current = dependencies;
  });

  const loadData = useCallback(async () => {
    if (loadingRef.current || !enabled) return;

    loadingRef.current = true;
    errorRef.current = null;

    try {
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      const data = await fetchFn();
      dataRef.current = data;
    } catch (error) {
      errorRef.current = error as Error;
    } finally {
      loadingRef.current = false;
    }
  }, [fetchFn, enabled, delay]);

  useEffect(() => {
    if (enabled) {
      deferExecution(loadData)();
    }
  }, [loadData, enabled]);
  
  return {
    data: dataRef.current,
    loading: loadingRef.current,
    error: errorRef.current,
    refetch: loadData,
  };
};

/**
 * Hook para optimizar re-renders innecesarios
 */
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T,
  dependencies: any[]
): T => {
  const callbackRef = useRef<T>(callback);
  const depsRef = useRef(dependencies);
  
  // Solo actualizar si las dependencias han cambiado
  if (!dependencies.every((dep, index) => dep === depsRef.current[index])) {
    callbackRef.current = callback;
    depsRef.current = dependencies;
  }
  
  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
};

/**
 * Hook para intersection observer optimizado
 */
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {},
  callback?: (entry: IntersectionObserverEntry) => void
) => {
  const elementRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const isIntersectingRef = useRef(false);
  
  const { threshold = 0.1, rootMargin = '50px' } = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          isIntersectingRef.current = entry.isIntersecting;
          callback?.(entry);
        });
      },
      { threshold, rootMargin, ...options }
    );

    observerRef.current.observe(element);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, rootMargin, callback, options]);
  
  return {
    ref: elementRef,
    isIntersecting: isIntersectingRef.current,
  };
};

/**
 * Hook para memoización avanzada con límite de cache
 */
export const useAdvancedMemo = <T>(
  factory: () => T,
  deps: any[],
  maxCacheSize = 10
) => {
  const cacheRef = useRef<Map<string, T>>(new Map());

  return useMemo(() => {
    const key = JSON.stringify(deps);
    const cache = cacheRef.current;

    if (cache.has(key)) {
      return cache.get(key)!;
    }

    const value = factory();
    cache.set(key, value);

    // Limitar el tamaño del cache
    if (cache.size > maxCacheSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }

    return value;
  }, [deps, factory, maxCacheSize]);
};

/**
 * Hook para detectar dispositivos de baja capacidad
 */
export const useDeviceCapabilities = () => {
  return useMemo(() => {
    const config = getAdaptiveConfig();
    
    return {
      isLowEnd: !config.enableHeavyFeatures,
      shouldEnableAnimations: config.enableAnimations,
      optimalChunkSize: config.chunkSize,
      preloadDistance: config.preloadDistance,
      debounceDelay: config.debounceDelay,
    };
  }, []);
};

/**
 * Hook para optimizar el scroll
 */
export const useOptimizedScroll = (
  callback: (scrollY: number) => void,
  throttleMs = 16 // ~60fps
) => {
  const { createThrottledCallback } = usePerformanceOptimization();
  
  const throttledCallback = useMemo(
    () => createThrottledCallback(() => {
      callback(window.scrollY);
    }, throttleMs),
    [callback, throttleMs, createThrottledCallback]
  );
  
  useEffect(() => {
    window.addEventListener('scroll', throttledCallback, { passive: true });
    return () => window.removeEventListener('scroll', throttledCallback);
  }, [throttledCallback]);
};

/**
 * Hook para optimizar resize events
 */
export const useOptimizedResize = (
  callback: (width: number, height: number) => void,
  debounceMs = 150
) => {
  const { createDebouncedCallback } = usePerformanceOptimization();
  
  const debouncedCallback = useMemo(
    () => createDebouncedCallback(() => {
      callback(window.innerWidth, window.innerHeight);
    }, debounceMs),
    [callback, debounceMs, createDebouncedCallback]
  );
  
  useEffect(() => {
    window.addEventListener('resize', debouncedCallback);
    return () => window.removeEventListener('resize', debouncedCallback);
  }, [debouncedCallback]);
};
