import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, TrendingDown, CreditCard, Calendar, Target, Bell } from 'lucide-react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constants/routes';

interface Alert {
  id: string;
  type: 'warning' | 'danger' | 'info' | 'success';
  title: string;
  description: string;
  action?: string;
  actionLabel?: string;
  icon: React.ReactNode;
  priority: 'high' | 'medium' | 'low';
}

export function SmartAlerts() {
  const navigate = useNavigate();
  const { 
    getNetBalance, 
    getTotalDebt, 
    getOverduePayments, 
    getSavingsRate,
    creditCards,
    loans,
    personalDebts,
    expenses
  } = useFinanceData();

  const generateAlerts = (): Alert[] => {
    const alerts: Alert[] = [];
    const netBalance = getNetBalance();
    const totalDebt = getTotalDebt();
    const overduePayments = getOverduePayments();
    const savingsRate = getSavingsRate();

    // Balance negativo
    if (netBalance < 0) {
      alerts.push({
        id: 'negative-balance',
        type: 'danger',
        title: 'Balance Negativo',
        description: `Tu balance actual es negativo (${Math.abs(netBalance).toLocaleString('es-DO')} RD$). Revisa tus gastos.`,
        action: 'review-expenses',
        actionLabel: 'Revisar Gastos',
        icon: <TrendingDown className="w-4 h-4" />,
        priority: 'high'
      });
    }

    // Pagos vencidos
    if (overduePayments > 0) {
      alerts.push({
        id: 'overdue-payments',
        type: 'danger',
        title: 'Pagos Vencidos',
        description: `Tienes ${overduePayments} pago(s) vencido(s). Ponlos al día para evitar intereses.`,
        action: 'view-payments',
        actionLabel: 'Ver Pagos',
        icon: <Calendar className="w-4 h-4" />,
        priority: 'high'
      });
    }

    // Tasa de ahorro baja
    if (savingsRate < 10) {
      alerts.push({
        id: 'low-savings',
        type: 'warning',
        title: 'Tasa de Ahorro Baja',
        description: `Tu tasa de ahorro es del ${savingsRate.toFixed(1)}%. Se recomienda al menos 20%.`,
        action: 'improve-savings',
        actionLabel: 'Mejorar Ahorros',
        icon: <Target className="w-4 h-4" />,
        priority: 'medium'
      });
    }

    // Tarjetas de crédito cerca del límite
    creditCards.forEach(card => {
      if (card.isActive && card.currentBalance > card.creditLimit * 0.8) {
        const utilization = (card.currentBalance / card.creditLimit) * 100;
        alerts.push({
          id: `credit-card-${card.id}`,
          type: 'warning',
          title: 'Tarjeta Cerca del Límite',
          description: `${card.name} está al ${utilization.toFixed(1)}% de su límite.`,
          action: `manage-credit:${card.id}`,
          actionLabel: 'Gestionar Crédito',
          icon: <CreditCard className="w-4 h-4" />,
          priority: 'medium'
        });
      }
    });

    // Gastos altos este mes
    const currentMonth = new Date().toISOString().slice(0, 7);
    const monthlyExpenses = expenses
      .filter(expense => expense.month === currentMonth)
      .reduce((total, expense) => total + expense.amount, 0);
    
    if (monthlyExpenses > netBalance * 0.8) {
      alerts.push({
        id: 'high-expenses',
        type: 'info',
        title: 'Gastos Elevados',
        description: 'Tus gastos este mes son altos comparados con tus ingresos.',
        action: 'analyze-expenses',
        actionLabel: 'Analizar Gastos',
        icon: <TrendingDown className="w-4 h-4" />,
        priority: 'low'
      });
    }

    return alerts.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  };

  const alerts = generateAlerts();

  const handleAlertAction = (action: string) => {
    const [actionType, payload] = action.split(':');

    switch (actionType) {
      case 'review-expenses':
        navigate(ROUTES.APP_EXPENSES);
        toast.success('Navegando a gestión de gastos...');
        break;
      case 'view-payments':
        navigate(ROUTES.APP_PAYMENTS);
        toast.success('Navegando a seguimiento de pagos...');
        break;
      case 'improve-savings':
        navigate(ROUTES.APP_GOALS);
        toast.success('Navegando a metas financieras...');
        break;
      case 'manage-credit':
        navigate(payload ? `/app/loans?cardId=${payload}` : '/app/loans');
        toast.success('Navegando a gestión de deudas...');
        break;
      case 'analyze-expenses':
        navigate(ROUTES.APP_EXPENSES);
        toast.success('Navegando a análisis de gastos...');
        break;
      default:
        toast.info('Acción no implementada');
    }
  };

  const getAlertColor = (type: Alert['type']) => {
    switch (type) {
      case 'danger': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'info': return 'bg-blue-50 border-blue-200';
      case 'success': return 'bg-green-50 border-green-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const getBadgeVariant = (type: Alert['type']) => {
    switch (type) {
      case 'danger': return 'destructive';
      case 'warning': return 'outline';
      case 'info': return 'secondary';
      case 'success': return 'default';
      default: return 'secondary';
    }
  };

  if (alerts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5 text-green-500" />
            Alertas Inteligentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <div className="text-green-500 mb-2">
              <Target className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-finanz-text-secondary">¡Excelente! No tienes alertas pendientes.</p>
            <p className="text-sm text-finanz-text-secondary mt-1">Tu situación financiera se ve bien.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5 text-finanz-primary" />
          Alertas Inteligentes
          <Badge variant="secondary">{alerts.length}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {alerts.slice(0, 5).map((alert) => (
          <div
            key={alert.id}
            className={`p-4 rounded-lg border ${getAlertColor(alert.type)}`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                <div className="mt-0.5">
                  {alert.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm">{alert.title}</h4>
                    <Badge variant={getBadgeVariant(alert.type)} className="text-xs">
                      {alert.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-finanz-text-secondary">{alert.description}</p>
                  {alert.action && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => handleAlertAction(alert.action!)}
                    >
                      {alert.actionLabel}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {alerts.length > 5 && (
          <div className="text-center pt-2">
            <Button variant="ghost" size="sm">
              Ver todas las alertas ({alerts.length - 5} más)
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
