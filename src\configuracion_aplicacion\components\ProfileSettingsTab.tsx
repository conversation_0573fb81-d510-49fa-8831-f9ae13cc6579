
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { User, Loader2 } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { ProfileFormData } from '../schemas/settingsSchemas';
// La importación de useFormField se elimina ya que FormControl lo maneja internamente.

interface ProfileSettingsTabProps {
  profileForm: UseFormReturn<ProfileFormData>;
  saveProfile: (data: ProfileFormData) => Promise<void>;
  defaultCurrency: 'DOP' | 'USD';
  setDefaultCurrency: (currency: 'DOP' | 'USD') => void;
  isLoading: boolean;
}

export const ProfileSettingsTab: React.FC<ProfileSettingsTabProps> = ({
  profileForm,
  saveProfile,
  defaultCurrency,
  setDefaultCurrency,
  isLoading,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          Información Personal
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...profileForm}>
          <form onSubmit={profileForm.handleSubmit(saveProfile)} className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={profileForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre Completo</FormLabel>
                    <FormControl>
                      <Input id={field.name} {...field} placeholder="Tu nombre completo" autoComplete="name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={profileForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Correo Electrónico</FormLabel>
                    <FormControl>
                      <Input id={field.name} {...field} type="email" placeholder="<EMAIL>" disabled autoComplete="email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={profileForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Teléfono</FormLabel>
                    <FormControl>
                      <Input id={field.name} {...field} placeholder="+****************" autoComplete="tel" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={profileForm.control}
                name="profession"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Profesión</FormLabel>
                    <FormControl>
                      <Input id={field.name} {...field} placeholder="Tu profesión" autoComplete="organization-title" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            <div className="space-y-2">
              <FormLabel htmlFor="currency">Moneda Predeterminada</FormLabel>
              <Select value={defaultCurrency} onValueChange={(value: 'DOP' | 'USD') => setDefaultCurrency(value)}>
                <SelectTrigger id="currency" autoComplete="off"> {/* Añadir id al SelectTrigger */}
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOP">Pesos Dominicanos (DOP)</SelectItem>
                  <SelectItem value="USD">Dólares Americanos (USD)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button type="submit" disabled={isLoading} className="w-full md:w-auto">
              {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Guardar Cambios
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
