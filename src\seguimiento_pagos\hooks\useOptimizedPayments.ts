
import { useMemo, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useExchangeRate } from '@/hooks/useExchangeRate';
import { useToast } from '@/hooks/use-toast';
import { logger } from "@/utils/logger";

import { PaymentGenerationService } from '../services/paymentGenerationService';
import { PaymentMergeService } from '../services/paymentMergeService';
import { PaymentCategorizationService } from '../services/paymentCategorizationService';
import { PaymentActionService } from '../services/paymentActionService';
import { PaymentsStateService } from '../services/paymentsStateService';
import { PaymentItem } from '../types/paymentTypes';

export const useOptimizedPayments = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();
  const { rate: exchangeRate } = useExchangeRate();
  
  const { 
    creditCards, 
    loans, 
    subscriptions, 
    personalDebts,
    personalDebtPayments,
    expenses,
    paymentRecords,
    addPaymentRecord,
    markPaymentAsPaid,
    updatePaymentRecord,
    addPersonalDebtPayment,
    updatePersonalDebt
  } = useFinanceData();

  // Servicios memoizados
  const services = useMemo(() => ({
    generation: new PaymentGenerationService(),
    merge: new PaymentMergeService(),
    categorization: new PaymentCategorizationService(),
    action: new PaymentActionService(),
    state: user?.id ? new PaymentsStateService(queryClient, user.id) : null
  }), [queryClient, user?.id]);

  // Generar pagos próximos con caching mejorado
  const upcomingPayments = useMemo(() => {
    logger.debug('useOptimizedPayments: Generating upcoming payments');
    
    const payments = services.generation.generateAllPayments(
      creditCards,
      loans,
      subscriptions,
      personalDebts,
      expenses
    );

    logger.debug('useOptimizedPayments: Generated payments', {
      total: payments.length,
      byType: {
        creditCards: payments.filter(p => p.type === 'credit-card').length,
        loans: payments.filter(p => p.type === 'loan').length,
        subscriptions: payments.filter(p => p.type === 'subscription').length,
        personalDebts: payments.filter(p => p.type === 'personal-debt').length,
        expenses: payments.filter(p => p.type === 'expense').length
      }
    });

    return payments;
  }, [creditCards, loans, subscriptions, personalDebts, expenses, services.generation]);

  // Fusionar con registros existentes
  const allPayments = useMemo(() => {
    logger.debug('useOptimizedPayments: Merging payments with records');
    
    const merged = services.merge.mergePaymentsWithRecords(upcomingPayments, paymentRecords);
    
    logger.debug('useOptimizedPayments: Merged payments', {
      total: merged.length,
      paid: merged.filter(p => p.status === 'paid').length,
      pending: merged.filter(p => p.status === 'pending').length,
      overdue: merged.filter(p => p.status === 'overdue').length
    });
    
    return merged;
  }, [upcomingPayments, paymentRecords, services.merge]);

  // Categorizar pagos
  const categorizedPayments = useMemo(() => {
    logger.debug('useOptimizedPayments: Categorizing payments');
    
    const categorized = services.categorization.categorizePayments(allPayments);
    
    logger.debug('useOptimizedPayments: Categorized payments', {
      pending: categorized.pending.length,
      paid: categorized.paid.length,
      overdue: categorized.overdue.length
    });
    
    return categorized;
  }, [allPayments, services.categorization]);

  // Calcular totales
  const totals = useMemo(() => {
    logger.debug('useOptimizedPayments: Calculating totals with exchange rate:', exchangeRate);
    return services.categorization.calculateTotals(categorizedPayments, exchangeRate);
  }, [categorizedPayments, services.categorization, exchangeRate]);

  // Marcar pago como pagado - optimizado
  const handleMarkAsPaid = useCallback(async (payment: PaymentItem, paymentData?: any) => {
    if (!user || !services.state) {
      logger.error('useOptimizedPayments: User not authenticated or state service unavailable');
      throw new Error("User not authenticated");
    }

    logger.info('useOptimizedPayments: Marking payment as paid', { paymentId: payment.id });

    const paidDate = new Date().toISOString().split('T')[0];
    const previousData = services.state.getPreviousPaymentRecords();

    // Actualización optimista
    if (payment.recordId) {
      services.state.updatePaymentOptimistically(payment, {
        status: 'paid',
        paidDate,
        notes: paymentData?.notes || null
      });
    }

    try {
      await services.action.handleMarkAsPaid(
        payment,
        personalDebts,
        addPersonalDebtPayment,
        updatePersonalDebt,
        markPaymentAsPaid,
        addPaymentRecord,
        paymentData
      );
      
      await services.state.invalidateAllPaymentQueries();

      toast({
        title: "Pago Completado",
        description: `El pago "${payment.name}" ha sido marcado como completado.`,
      });

      logger.info('useOptimizedPayments: Payment marked as paid successfully');

    } catch (error) {
      // Revertir actualización optimista
      services.state.revertOptimisticUpdate(previousData);
      
      logger.error('useOptimizedPayments: Error marking payment as paid', error);
      
      toast({
        title: "Error al Procesar Pago",
        description: `Hubo un problema al procesar el pago "${payment.name}". Por favor, inténtalo de nuevo.`,
        variant: "destructive"
      });
      throw error;
    }
  }, [user, services, personalDebts, addPersonalDebtPayment, updatePersonalDebt, markPaymentAsPaid, addPaymentRecord, toast]);

  // Desmarcar pago como pagado - optimizado
  const handleUnmarkAsPaid = useCallback(async (payment: PaymentItem) => {
    if (!user || !services.state) {
      logger.error('useOptimizedPayments: User not authenticated or state service unavailable');
      throw new Error("User not authenticated");
    }

    if (!payment.recordId) {
      logger.error('useOptimizedPayments: Cannot unmark payment without recordId');
      throw new Error("Cannot unmark payment without a recordId");
    }

    logger.info('useOptimizedPayments: Unmarking payment as paid', { paymentId: payment.id });

    const previousData = services.state.getPreviousPaymentRecords();

    // Actualización optimista
    services.state.updatePaymentOptimistically(payment, {
      status: 'pending',
      paidDate: null
    });

    try {
      const updatePaymentRecordAdapter = async (id: string, updates: { status: string; paidDate: string | null; notes?: string }) => {
        return await updatePaymentRecord(id, {
          ...updates,
          status: updates.status as 'pending' | 'paid' | 'overdue'
        });
      };

      await services.action.handleUnmarkAsPaid(payment, updatePaymentRecordAdapter);
      await services.state.invalidateAllPaymentQueries();

      toast({
        title: "Pago Desmarcado",
        description: `El pago "${payment.name}" ha sido marcado como pendiente.`,
      });

      logger.info('useOptimizedPayments: Payment unmarked successfully');

    } catch (error) {
      // Revertir actualización optimista
      services.state.revertOptimisticUpdate(previousData);
      
      logger.error('useOptimizedPayments: Error unmarking payment', error);

      toast({
        title: "Error al Desmarcar Pago",
        description: `Hubo un problema al desmarcar el pago "${payment.name}". Por favor, inténtalo de nuevo.`,
        variant: "destructive"
      });
      throw error;
    }
  }, [user, services, updatePaymentRecord, toast]);

  return {
    payments: categorizedPayments,
    totals,
    handleMarkAsPaid,
    handleUnmarkAsPaid,
    updatePaymentRecord,
    allPayments,
    isLoading: false // El loading se maneja en useFinanceData
  };
};
