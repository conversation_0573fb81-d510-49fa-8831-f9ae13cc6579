
import { Toaster } from "@/components/ui/toaster";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import React, { Suspense, lazy } from "react";
// QueryClientProvider is already applied in main.tsx
import { AuthProvider } from "@/contexts/AuthContext";
import { SecurityProvider } from "@/components/security/SecurityProvider";
import { SecurityMonitor } from "@/components/security/SecurityMonitor";
import { Toaster as SonnerToaster } from "@/components/ui/sonner";
import { SidebarProvider } from "@/components/ui/sidebar";
import { LayoutOptimized } from "@/components/LayoutOptimized";
import { AuthGuard } from "@/components/AuthGuard";
import { useApplyTheme } from "@/hooks/useApplyTheme";
import { ROUTES } from '@/constants/routes';

type LazyComponent<T extends React.ComponentType<Record<string, unknown>>> = React.LazyExoticComponent<T> & {
  preload?: () => Promise<unknown>;
};

// Lazy load pages for better performance - using optimized versions
const IndexPage = lazy(() => import("@/pages/Index")) as LazyComponent<React.ComponentType>;
const AuthPage = lazy(() => import("@/pages/Auth")) as LazyComponent<React.ComponentType>;
const FeaturesPage = lazy(() => import("@/pages/Features")) as LazyComponent<React.ComponentType>;
const PrivacyPolicyPage = lazy(() => import("@/pages/PrivacyPolicy")) as LazyComponent<React.ComponentType>;
const TermsOfServicePage = lazy(() => import("@/pages/TermsOfService")) as LazyComponent<React.ComponentType>;
const SupportPage = lazy(() => import("@/pages/Support")) as LazyComponent<React.ComponentType>;
const DashboardPage = lazy(() => import("@/pages/DashboardProfessional")) as LazyComponent<React.ComponentType>;
const ExpensesPage = lazy(() => import("@/pages/ExpensesOptimized")) as LazyComponent<React.ComponentType>;
const IncomePage = lazy(() => import("@/gestion_ingresos/pages/Income")) as LazyComponent<React.ComponentType>;
const LoansPage = lazy(() => import("@/pages/Debts")) as LazyComponent<React.ComponentType>;
const GoalsPage = lazy(() => import("@/pages/FinancialGoals")) as LazyComponent<React.ComponentType>;
const ConfigurationPage = lazy(() => import("@/pages/Settings")) as LazyComponent<React.ComponentType>;
const SubscriptionsPage = lazy(() => import("@/pages/Subscriptions")) as LazyComponent<React.ComponentType>;
const ReimbursementsPage = lazy(() => import("@/pages/Reimbursements")) as LazyComponent<React.ComponentType>;
const PaymentsPage = lazy(() => import("@/pages/Payments")) as LazyComponent<React.ComponentType>;
const FinancialAdvicePage = lazy(() => import("@/pages/FinancialAdvice")) as LazyComponent<React.ComponentType>;
const DiagnosticPage = lazy(() => import("@/pages/Diagnostic")) as LazyComponent<React.ComponentType>;

IndexPage.preload = () => import("@/pages/Index");
AuthPage.preload = () => import("@/pages/Auth");
FeaturesPage.preload = () => import("@/pages/Features");
PrivacyPolicyPage.preload = () => import("@/pages/PrivacyPolicy");
TermsOfServicePage.preload = () => import("@/pages/TermsOfService");
SupportPage.preload = () => import("@/pages/Support");
DashboardPage.preload = () => import("@/pages/DashboardProfessional");
ExpensesPage.preload = () => import("@/pages/ExpensesOptimized");
IncomePage.preload = () => import("@/gestion_ingresos/pages/Income");
LoansPage.preload = () => import("@/pages/Debts");
GoalsPage.preload = () => import("@/pages/FinancialGoals");
ConfigurationPage.preload = () => import("@/pages/Settings");
SubscriptionsPage.preload = () => import("@/pages/Subscriptions");
ReimbursementsPage.preload = () => import("@/pages/Reimbursements");
PaymentsPage.preload = () => import("@/pages/Payments");
FinancialAdvicePage.preload = () => import("@/pages/FinancialAdvice");
DiagnosticPage.preload = () => import("@/pages/Diagnostic");

const ThemeListener = () => {
  useApplyTheme();
  return null;
};

// Loading component optimizado
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center space-y-4">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-finanz-primary mx-auto"></div>
      <p className="text-gray-600">Cargando...</p>
    </div>
  </div>
);

function App() {
  return (
      <SecurityProvider>
        <AuthProvider>
          <Router>
            <ThemeListener />
            <AuthGuard>
              <div className="min-h-screen bg-background">
                <SecurityMonitor />
                <Suspense fallback={<LoadingFallback />}>
                  <Routes>
                    <Route path={ROUTES.ROOT} element={<IndexPage />} />
                    <Route path={ROUTES.AUTH} element={<AuthPage />} />
                    <Route path={ROUTES.FEATURES} element={<FeaturesPage />} />
                    <Route path={ROUTES.PRIVACY_POLICY} element={<PrivacyPolicyPage />} />
                    <Route path={ROUTES.TERMS_OF_SERVICE} element={<TermsOfServicePage />} />
                    <Route path={ROUTES.SUPPORT} element={<SupportPage />} />
                    <Route path={ROUTES.APP} element={
                      <SidebarProvider>
                        <LayoutOptimized />
                      </SidebarProvider>
                    }>
                      <Route path="dashboard" element={<DashboardPage />} />
                      <Route path="expenses" element={<ExpensesPage />} />
                      <Route path="income" element={<IncomePage />} />
                      <Route path="loans" element={<LoansPage />} />
                      <Route path="goals" element={<GoalsPage />} />
                      <Route path="configuration" element={<ConfigurationPage />} />
                      <Route path="subscriptions" element={<SubscriptionsPage />} />
                      <Route path="reimbursements" element={<ReimbursementsPage />} />
                      <Route path="payments" element={<PaymentsPage />} />
                      <Route path="financial-advice" element={<FinancialAdvicePage />} />
                      <Route path="diagnostic" element={<DiagnosticPage />} />
                    </Route>
                    <Route path="*" element={<Navigate to={ROUTES.ROOT} replace />} />
                  </Routes>
                </Suspense>
              </div>
            </AuthGuard>
            <Toaster />
            <SonnerToaster />
            </Router>
          </AuthProvider>
        </SecurityProvider>
  );
}

export default App;

export {
  IndexPage,
  AuthPage,
  FeaturesPage,
  PrivacyPolicyPage,
  TermsOfServicePage,
  SupportPage,
  DashboardPage,
  ExpensesPage,
  IncomePage,
  LoansPage,
  GoalsPage,
  ConfigurationPage,
  SubscriptionsPage,
  ReimbursementsPage,
  PaymentsPage,
  FinancialAdvicePage,
  DiagnosticPage,
};
