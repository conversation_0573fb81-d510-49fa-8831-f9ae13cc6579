
import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from 'lucide-react';
import { Expense } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { ExpenseEditForm } from './list/ExpenseEditForm';
import { ExpenseItem } from './list/ExpenseItem';
import { logger } from '@/utils/logger';
import { QUERY_KEYS } from '@/constants/queryKeys';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface ExpenseListProps {
  expenses: Expense[];
  onUpdateExpense: (id: string, updates: Partial<Expense>) => void;
  onDeleteExpense: (id: string) => void;
}

export const ExpenseList = React.memo(function ExpenseList({ expenses, onUpdateExpense, onDeleteExpense }: ExpenseListProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { isMobile } = useBreakpoint();
  const [editingExpense, setEditingExpense] = useState<string | null>(null);

  const handleEdit = useCallback((expense: Expense) => {
    setEditingExpense(expense.id);
  }, []);

  const handleSaveEdit = useCallback(async (id: string, updates: Partial<Expense>) => {
    try {
      await onUpdateExpense(id, updates);
      setEditingExpense(null);
      
      // Sincronización mejorada e inmediata para pagos
      if (user?.id) {
        logger.debug('Expense updated, triggering immediate payment synchronization...');
        
        // Invalidar y refetch inmediato de queries relacionadas con pagos
        const relatedQueries = [
          [QUERY_KEYS.PAYMENT_RECORDS, user.id],
          [QUERY_KEYS.DEBTS, user.id],
          [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, user.id],
          [QUERY_KEYS.OTHER_DATA, user.id],
          [QUERY_KEYS.EXPENSES, user.id]
        ];

        await Promise.all(
          relatedQueries.map(queryKey =>
            queryClient.invalidateQueries({ queryKey, exact: true })
          )
        );

        // Forzar refetch inmediato sin delay
        await queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user.id],
          exact: true
        });
        
        // Refetch adicional de expenses para asegurar sincronización
        await queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.EXPENSES, user.id],
          exact: true
        });
      }
    } catch (error) {
      logger.error('Error updating expense:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el gasto",
        variant: "destructive"
      });
    }
  }, [onUpdateExpense, queryClient, user?.id, toast]);

  const handleCancelEdit = useCallback(() => {
    setEditingExpense(null);
  }, []);

  const handleDelete = useCallback(async (expenseId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este gasto?')) {
      try {
        await onDeleteExpense(expenseId);
        
        // Sincronización inmediata para pagos después de eliminar
        if (user?.id) {
          logger.debug('Expense deleted, triggering immediate payment synchronization...');
          
          const relatedQueries = [
            [QUERY_KEYS.PAYMENT_RECORDS, user.id],
            [QUERY_KEYS.DEBTS, user.id],
            [QUERY_KEYS.PERSONAL_DEBT_PAYMENTS, user.id],
            [QUERY_KEYS.OTHER_DATA, user.id],
            [QUERY_KEYS.EXPENSES, user.id]
          ];

          await Promise.all(
            relatedQueries.map(queryKey =>
              queryClient.invalidateQueries({ queryKey, exact: true })
            )
          );

          // Refetch inmediato
          await queryClient.refetchQueries({
            queryKey: [QUERY_KEYS.PAYMENT_RECORDS, user.id],
            exact: true
          });
        }
        
        toast({
          title: "Éxito",
          description: "Gasto eliminado correctamente",
        });
      } catch (error) {
        logger.error('Error deleting expense:', error);
        toast({
          title: "Error",
          description: "Hubo un problema al eliminar el gasto",
          variant: "destructive"
        });
      }
    }
  }, [onDeleteExpense, toast, queryClient, user?.id]);

  return (
    <Card className="border-finanz-border h-full flex flex-col">
      <CardHeader className="flex-shrink-0">
        <CardTitle className="text-finanz-primary">Todos los Gastos</CardTitle>
        <CardDescription>
          Gestiona y edita tus gastos registrados
        </CardDescription>
      </CardHeader>
      <CardContent className={`flex-1 ${isMobile ? 'max-h-[400px] overflow-y-auto' : 'max-h-[500px] overflow-y-auto'}`}>
        {expenses.length > 0 ? (
          <div className="space-y-3 pb-4">
            {expenses
              .sort((a, b) => {
                const dateA = new Date(a.paymentDate ?? a.date).getTime();
                const dateB = new Date(b.paymentDate ?? b.date).getTime();
                return dateB - dateA;
              })
              .map((expense) => (
                <div key={expense.id} className="p-4 bg-finanz-neutral/10 rounded-lg border border-finanz-border">
                  {editingExpense === expense.id ? (
                    <ExpenseEditForm
                      expense={expense}
                      onSave={handleSaveEdit}
                      onCancel={handleCancelEdit}
                    />
                  ) : (
                    <ExpenseItem
                      expense={expense}
                      onEdit={() => handleEdit(expense)}
                      onDelete={() => handleDelete(expense.id)}
                    />
                  )}
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-finanz-text-secondary mx-auto mb-4" />
            <p className="text-finanz-text-secondary">
              Aún no tienes gastos registrados
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
});
