
import React, { useState, useMemo } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
// import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'; // Unused
import { formatCurrency } from '@/components/ui/numeric-input';
import { Calendar as CalendarIcon, DollarSign, Clock, AlertTriangle, Check } from 'lucide-react';
import { PaymentItem } from '../types/paymentTypes';
import { format, parseISO } from 'date-fns'; // Removed isSameDay
import { es } from 'date-fns/locale';

interface PaymentCalendarViewProps {
  payments: PaymentItem[];
  onPaymentSelect?: (payment: PaymentItem) => void;
  selectedDate?: Date;
  onDateSelect?: (date: Date | undefined) => void;
}

interface DayPayments {
  date: Date;
  payments: PaymentItem[];
  totalAmount: number;
  hasOverdue: boolean;
  hasPending: boolean;
  hasPaid: boolean;
}

export const PaymentCalendarView: React.FC<PaymentCalendarViewProps> = ({
  payments,
  onPaymentSelect,
  selectedDate,
  onDateSelect
}) => {
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);

  // Agrupar pagos por fecha
  const paymentsByDate = useMemo((): Map<string, DayPayments> => {
    const grouped = new Map<string, DayPayments>();

    payments.forEach(payment => {
      const paymentDate = parseISO(payment.dueDate);
      const dateKey = format(paymentDate, 'yyyy-MM-dd');

      if (!grouped.has(dateKey)) {
        grouped.set(dateKey, {
          date: paymentDate,
          payments: [],
          totalAmount: 0,
          hasOverdue: false,
          hasPending: false,
          hasPaid: false
        });
      }

      const dayData = grouped.get(dateKey)!;
      dayData.payments.push(payment);
      dayData.totalAmount += payment.amount;
      
      if (payment.status === 'overdue') dayData.hasOverdue = true;
      if (payment.status === 'pending') dayData.hasPending = true;
      if (payment.status === 'paid') dayData.hasPaid = true;
    });

    return grouped;
  }, [payments]);

  // Obtener datos para la fecha seleccionada o hover
  const getSelectedDayData = (date: Date | null): DayPayments | null => {
    if (!date) return null;
    const dateKey = format(date, 'yyyy-MM-dd');
    return paymentsByDate.get(dateKey) || null;
  };

  const selectedDayData = getSelectedDayData(selectedDate || hoveredDate);

  // Unused function modifyDay removed

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <Check className="w-3 h-3 text-green-600" />;
      case 'overdue': return <AlertTriangle className="w-3 h-3 text-red-600" />;
      case 'pending': return <Clock className="w-3 h-3 text-yellow-600" />;
      default: return <Clock className="w-3 h-3" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Calendario principal */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarIcon className="w-5 h-5" />
              Vista de Calendario
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={onDateSelect}
              className="w-full"
              modifiers={{
                hasPayments: (date) => {
                  const dateKey = format(date, 'yyyy-MM-dd');
                  return paymentsByDate.has(dateKey);
                },
                overdue: (date) => {
                  const dateKey = format(date, 'yyyy-MM-dd');
                  const dayData = paymentsByDate.get(dateKey);
                  return dayData?.hasOverdue || false;
                },
                pending: (date) => {
                  const dateKey = format(date, 'yyyy-MM-dd');
                  const dayData = paymentsByDate.get(dateKey);
                  return dayData?.hasPending || false;
                },
                paid: (date) => {
                  const dateKey = format(date, 'yyyy-MM-dd');
                  const dayData = paymentsByDate.get(dateKey);
                  return dayData?.hasPaid || false;
                }
              }}
              modifiersStyles={{
                overdue: { backgroundColor: '#fecaca', color: '#991b1b' },
                pending: { backgroundColor: '#fef3c7', color: '#92400e' },
                paid: { backgroundColor: '#dcfce7', color: '#166534' },
              }}
              onDayMouseEnter={(date) => setHoveredDate(date)}
              onDayMouseLeave={() => setHoveredDate(null)}
            />

            {/* Leyenda */}
            <div className="mt-4 flex flex-wrap gap-2">
              <div className="flex items-center gap-1 text-xs">
                <div className="w-3 h-3 bg-red-200 rounded"></div>
                <span>Vencidos</span>
              </div>
              <div className="flex items-center gap-1 text-xs">
                <div className="w-3 h-3 bg-yellow-200 rounded"></div>
                <span>Pendientes</span>
              </div>
              <div className="flex items-center gap-1 text-xs">
                <div className="w-3 h-3 bg-green-200 rounded"></div>
                <span>Pagados</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Panel de detalles */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              {selectedDayData 
                ? format(selectedDayData.date, 'dd MMMM yyyy', { locale: es })
                : 'Selecciona una fecha'
              }
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedDayData ? (
              <div className="space-y-4">
                {/* Resumen del día */}
                <div className="bg-finanz-background rounded-lg p-3">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Total del día:</span>
                    <span className="font-semibold">
                      {formatCurrency(selectedDayData.totalAmount)}
                    </span>
                  </div>
                  <div className="text-xs text-finanz-text-secondary">
                    {selectedDayData.payments.length} pago(s) programado(s)
                  </div>
                </div>

                {/* Lista de pagos del día */}
                <div className="space-y-2">
                  {selectedDayData.payments.map((payment) => (
                    <div
                      key={payment.id}
                      className="border rounded-lg p-3 hover:bg-finanz-background/50 cursor-pointer transition-colors"
                      onClick={() => onPaymentSelect?.(payment)}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{payment.name}</span>
                        <Badge className={`text-xs ${getStatusBadgeColor(payment.status)}`}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(payment.status)}
                            {payment.status === 'paid' ? 'Pagado' :
                             payment.status === 'overdue' ? 'Vencido' : 'Pendiente'}
                          </span>
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-finanz-text-secondary">
                          {payment.type.replace('-', ' ')}
                        </span>
                        <span className="font-semibold">
                          {formatCurrency(payment.amount, payment.currency)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Acciones rápidas */}
                {selectedDayData.payments.some(p => p.status === 'pending') && (
                  <Button className="w-full" size="sm">
                    Marcar todos como pagados
                  </Button>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <CalendarIcon className="w-12 h-12 text-finanz-text-secondary mx-auto mb-3" />
                <p className="text-finanz-text-secondary mb-2">
                  Selecciona una fecha en el calendario
                </p>
                <p className="text-xs text-finanz-text-secondary">
                  para ver los pagos programados
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
