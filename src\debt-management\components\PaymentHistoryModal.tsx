
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table'; // Unused Table components
import { PaymentRecord } from '@/types';
import { formatCurrency } from '@/components/ui/numeric-input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Calendar, Receipt, DollarSign } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface PaymentHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  cardName: string;
  payments: PaymentRecord[];
}

export function PaymentHistoryModal({ isOpen, onClose, cardName, payments }: PaymentHistoryModalProps) {
  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const currency = payments[0]?.currency || 'DOP';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh]">
        <DialogHeader className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Receipt className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold">Historial de Pagos</DialogTitle>
              <DialogDescription className="text-sm text-muted-foreground">
                {cardName}
              </DialogDescription>
            </div>
          </div>
          
          {payments.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-green-100 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-700" />
                <span className="font-semibold text-green-900">Total Pagado</span>
              </div>
              <Badge className="bg-green-600 text-base font-bold text-white px-3 py-1">
                {formatCurrency(totalPaid, currency)}
              </Badge>
            </div>
          )}
        </DialogHeader>

        <ScrollArea className="max-h-[50vh]">
          {payments.length > 0 ? (
            <div className="space-y-3">
              {payments.map((payment, index) => (
                <div
                  key={payment.id}
                  className="p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow bg-white"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <Calendar className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="space-y-1">
                        <div className="font-medium text-gray-900">
                          Pago #{payments.length - index}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <span>{new Date(payment.paidDate!).toLocaleDateString('es-ES', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}</span>
                        </div>
                        {/* {payment.notes && (
                          <div className="text-sm text-gray-500 bg-gray-50 px-2 py-1 rounded mt-2">
                            {payment.notes}
                          </div>
                        )} */}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-green-600">
                        {formatCurrency(payment.amount, payment.currency)}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        Aplicado
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="p-3 bg-gray-100 rounded-full mb-4">
                <Receipt className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No hay pagos registrados
              </h3>
              <p className="text-gray-500 max-w-sm">
                Los pagos que apliques a esta tarjeta aparecerán aquí con todos los detalles.
              </p>
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
