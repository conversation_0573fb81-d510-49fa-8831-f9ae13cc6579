/* eslint-disable react-refresh/only-export-components */

import React, { createContext, useContext, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { useAuthState } from './auth/useAuthState';
import { useAuthActions } from './auth/useAuthActions';
import { useAuthSecurity } from '@/hooks/useAuthSecurity';
import { useAuthInitialization } from './auth/useAuthInitialization';

interface UserProfile {
  id: string;
  email: string;
  full_name?: string | null;
  avatar_url?: string | null;
  phone?: string | null;
  profession?: string | null;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  loading: boolean;
  isLocked: boolean;
  sessionTimeLeft: number;
  signInWithGoogle: (forceAccountSelection?: boolean) => Promise<void>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  unlockScreen: (password: string, userEmail?: string) => Promise<boolean>;
  isValidSession: boolean;
  shouldLockScreen: boolean;
  timeUntilExpiry: number;
  securityConfigsLoaded: boolean;
  deviceTrusted: boolean;
  updateActivity: () => void;
  validateSessionWithSecurity: () => Promise<void>;
  markDeviceAsTrusted: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { user, session, profile, loading, setLoading, clearAuthState, isLocked } = useAuthState();
  
  // Initialize auth state (delegated to useAuthState now)
  useAuthInitialization({ setLoading });
  
  // Security features
  const {
    isValidSession,
    shouldLockScreen,
    timeUntilExpiry,
    securityConfigsLoaded,
    deviceTrusted,
    updateActivity,
    validateSessionWithSecurity,
    markDeviceAsTrusted,
  } = useAuthSecurity(session);

  // Auth actions
  const { signInWithGoogle, signOut, refreshSession, unlockScreen } = useAuthActions(
    setLoading,
    clearAuthState,
    updateActivity
  );

  // Calculate session time left
  const sessionTimeLeft = timeUntilExpiry;

  const value: AuthContextType = {
    user,
    session,
    profile,
    loading,
    isLocked,
    sessionTimeLeft,
    signInWithGoogle,
    signOut,
    refreshSession,
    unlockScreen,
    isValidSession,
    shouldLockScreen,
    timeUntilExpiry,
    securityConfigsLoaded,
    deviceTrusted,
    updateActivity,
    validateSessionWithSecurity,
    markDeviceAsTrusted,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
