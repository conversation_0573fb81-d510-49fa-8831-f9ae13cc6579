
import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Clock, Shield, X } from 'lucide-react';
import { useEnhancedSecurity } from '@/hooks/useEnhancedSecurity';

export const SecurityAlerts: React.FC = () => {
  const { 
    suspiciousActivity, 
    sessionWarning,
    clearSecurityWarning,
    refreshSession,
    performSecurityHealthCheck
  } = useEnhancedSecurity();
  
  const healthCheck = performSecurityHealthCheck();

  if (healthCheck.isHealthy) {
    return null;
  }

  return (
    <div className="space-y-2">
      {suspiciousActivity && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800 flex items-center justify-between">
            <span>
              Actividad sospechosa detectada. Se han registrado múltiples intentos de acceso.
            </span>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearSecurityWarning}
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                <Shield className="w-3 h-3 mr-1" />
                Marcar como seguro
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {sessionWarning && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <Clock className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800 flex items-center justify-between">
            <span>
              Tu sesión expirará pronto. ¿Deseas renovarla?
            </span>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={refreshSession}
                className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Renovar sesión
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {healthCheck.issues.length > 0 && !suspiciousActivity && !sessionWarning && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <div className="space-y-1">
              <span className="font-medium">Problemas de seguridad detectados:</span>
              <ul className="text-sm space-y-1">
                {healthCheck.issues.map((issue, index) => (
                  <li key={index} className="flex items-center space-x-1">
                    <X className="w-3 h-3" />
                    <span>{issue}</span>
                  </li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
