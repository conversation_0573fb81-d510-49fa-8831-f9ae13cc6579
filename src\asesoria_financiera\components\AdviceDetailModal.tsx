
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { AdviceDetailModalHeader } from './AdviceDetailModalHeader';
import { AdviceDetailModalMetrics } from './AdviceDetailModalMetrics';
import { AdviceDetailModalActionPlan } from './AdviceDetailModalActionPlan';
import { AdviceDetailModalTips } from './AdviceDetailModalTips';
import { AdviceDetailModalActions } from './AdviceDetailModalActions';
import { getDetailedPlan } from '../utils/adviceDetailPlanGenerator';

interface AdviceItem {
  id: string;
  title: string;
  description: string;
  type: 'success' | 'warning' | 'danger' | 'info';
  category: 'savings' | 'debt' | 'budget' | 'investment' | 'emergency';
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
}

interface AdviceDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  advice: AdviceItem | null;
  financialData: {
    netIncome: number;
    totalExpenses: number;
    totalDebt: number;
    savingsRate: number;
    debtToIncomeRatio: number;
    emergencyFundMonths: number;
  };
}

export const AdviceDetailModal: React.FC<AdviceDetailModalProps> = ({
  isOpen,
  onClose,
  advice,
  financialData
}) => {
  const { toast } = useToast();
  const [isActionPlanStarted, setIsActionPlanStarted] = useState(false);

  if (!advice) return null;

  const startActionPlan = () => {
    if (!advice.actionable) return;

    // Guardar el plan en localStorage
    const activePlans = JSON.parse(localStorage.getItem('activeAdvicePlans') || '[]');
    const newPlan = {
      id: advice.id,
      title: advice.title,
      category: advice.category,
      priority: advice.priority,
      startDate: new Date().toISOString(),
      status: 'active',
      progress: 0,
      completedSteps: [],
      nextAction: 'Comenzar con el primer paso del plan'
    };

    // Evitar duplicados
    const existingPlanIndex = activePlans.findIndex((plan: { id: string }) => plan.id === advice.id);
    if (existingPlanIndex >= 0) {
      activePlans[existingPlanIndex] = newPlan;
    } else {
      activePlans.push(newPlan);
    }

    localStorage.setItem('activeAdvicePlans', JSON.stringify(activePlans));
    setIsActionPlanStarted(true);

    toast({
      title: "Plan de Acción Iniciado",
      description: `Has iniciado el plan: ${advice.title}. Revisa tu progreso en el dashboard.`,
    });
  };

  const detailedPlan = getDetailedPlan(advice, financialData);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            <AdviceDetailModalHeader advice={advice} />
          </DialogTitle>
          <DialogDescription>
            {/* Empty as content is handled in header */}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <AdviceDetailModalMetrics
            potentialSavings={detailedPlan.potentialSavings}
            targetAmount={detailedPlan.targetAmount}
          />

          <AdviceDetailModalActionPlan
            overview={detailedPlan.overview}
            steps={detailedPlan.steps}
          />

          <AdviceDetailModalTips tips={detailedPlan.tips} />

          <AdviceDetailModalActions
            advice={advice}
            onClose={onClose}
            onStartActionPlan={startActionPlan}
            isActionPlanStarted={isActionPlanStarted}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
