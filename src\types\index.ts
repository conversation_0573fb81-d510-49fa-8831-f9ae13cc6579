export interface Income {
  id: string;
  month: string; // Formato: "YYYY-MM"
  currency: "DOP" | "USD";
  fixedSalary: number;
  variablePercentage: number; // 0-100
  variableAmount: number; // Calculado
  quarterlyIncentive: number;
  performancePercentage: number; // 0-100
  vehicleDepreciation: number;
  legalDeductions: number;
  payrollLoan: number;
  grossIncome: number; // Calculado
  netIncome: number; // Calculado
  otherIncomeItems: Array<{
    id: string;
    description: string;
    amount: number;
    currency: "DOP" | "USD";
  }>;
  variableScenarios: {
    base: number;
    medium: number;
    optimal: number;
  };
  isProjected?: boolean; // Add optional isProjected property
}

export interface Expense {
  id: string;
  date: string; // Formato: "YYYY-MM-DD"
  month: string; // Calculado automáticamente de 'date' (YYYY-MM)
  type: "Fijo" | "Variable";
  categoryId: string;
  categoryName: string;
  amount: number;
  description: string;
  paymentMethod: "cash" | "debit-card" | "credit-card" | "transfer";
  status: "pending" | "paid";
  currency: "DOP" | "USD";
  paymentDate?: string; // Nueva fecha de pago opcional
  isRecurring: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreditCard {
  id: string;
  name: string;
  currency: "DOP" | "USD";
  creditLimit: number;
  currentBalance: number;
  minimumPayment: number;
  cutoffDate: string; // Fecha de corte
  paymentDueDate: string; // Fecha límite de pago
  paymentDate: string; // Fecha de pago (mantenemos por compatibilidad)
  interestRate: number;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Loan {
  id: string;
  type: string;
  name: string;
  currency: "DOP" | "USD";
  totalAmount: number;
  monthlyPayment: number;
  interestRate: number; // 0-100
  loanTerm: number; // meses
  startDate: string;
  dueDate: string;
  paymentDate: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface PersonalDebt {
  id: string;
  name: string;
  amount: number;
  remainingBalance?: number;
  currency: 'DOP' | 'USD';
  paymentDate: string;
  monthlyBudget?: number; // Nueva propiedad para el presupuesto mensual
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface PersonalDebtPayment {
  id: string;
  personalDebtId: string;
  paymentDate: string;
  totalAmount: number;
  interestAmount: number;
  principalAmount: number;
  remainingBalance: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Subscription {
  id: string;
  name: string;
  amount: number;
  categoryId: string;
  categoryName: string;
  billingDate: string;
  isActive: boolean;
  currency: "DOP" | "USD";
  createdAt: string;
  updatedAt: string;
}

export interface PaymentRecord {
  id: string;
  paymentType: "credit-card" | "loan" | "subscription" | "personal-debt" | "expense";
  referenceId: string;
  dueDate: string;
  amount: number;
  currency: "DOP" | "USD";
  status: "pending" | "paid" | "overdue";
  paidDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Reimbursement {
  id: string;
  date: string;
  categoryId: string;
  categoryName: string;
  amount: number;
  currency: "DOP" | "USD";
  status: "Pendiente" | "Procesando" | "Completado" | "Rechazado";
  reimbursementDate: string | null;
  description: string;
  attachments?: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FinancialGoal {
  id: string;
  name: string;
  amount: number;
  currentAmount: number;
  monthlyContribution: number;
  targetDate: string;
  currency: "DOP" | "USD";
  isActive: boolean;
  category?: string;
  priority?: "high" | "medium" | "low";
  createdAt: string;
  updatedAt: string;
}

export interface GoalContribution {
  id: string;
  goal_id: string;
  user_id: string;
  amount: number;
  date: string;
  note?: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
}

export const defaultCategories: Category[] = [
  { id: 'alquiler', name: 'Alquiler' },
  { id: 'luz', name: 'Servicio de electricidad' },
  { id: 'gas', name: 'Suministro de Gas' },
  { id: 'telefono', name: 'Servicio Telefónico' },
  { id: 'internet', name: 'Servicio de Internet' },
  { id: 'alimentacion', name: 'Alimentación' },
  { id: 'transporte', name: 'Transporte' },
  { id: 'salud', name: 'Salud' },
  { id: 'colegio', name: 'Colegio' },
  { id: 'universidad', name: 'Universidad' },
  { id: 'entretenimiento', name: 'Entretenimiento' },
  { id: 'vestimenta', name: 'Vestimenta' },
  { id: 'otros', name: 'Otros' }
];

export const reimbursementCategories: Category[] = [
  { id: 'combustible', name: 'Combustible' },
  { id: 'hospedaje', name: 'Hospedaje' },
  { id: 'alimentacion', name: 'Alimentación' },
  { id: 'representacion', name: 'Gastos de Representación' },
  { id: 'parqueo', name: 'Parqueo' },
  { id: 'peajes', name: 'Peajes' },
  { id: 'otros', name: 'Otros' }
];
