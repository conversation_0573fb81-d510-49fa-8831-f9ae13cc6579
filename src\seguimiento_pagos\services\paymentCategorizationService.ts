
import { logger } from "@/utils/logger";
import { PaymentItem, CategorizedPayments, PaymentTotals } from '../types/paymentTypes';

export class PaymentCategorizationService {
  private today: Date;
  private currentMonth: number;
  private currentYear: number;

  constructor() {
    this.today = new Date();
    this.today.setHours(0, 0, 0, 0);
    this.currentMonth = this.today.getMonth();
    this.currentYear = this.today.getFullYear();
  }

  categorizePayments(payments: PaymentItem[]): CategorizedPayments {
    const pending: PaymentItem[] = [];
    const paid: PaymentItem[] = [];
    const overdue: PaymentItem[] = [];

    logger.debug('Categorizing payments for date:', this.today.toISOString().split('T')[0]);
    logger.debug('Total payments to categorize:', payments.length);

    payments.forEach(payment => {
      const dueDate = new Date(payment.dueDate);
      dueDate.setHours(0, 0, 0, 0);

      // Determinar el estado correcto basado en la fecha actual
      let currentStatus = payment.status;
      
      // Solo cambiar de pending a overdue si realmente está vencido
      if (currentStatus === 'pending' && dueDate < this.today) {
        currentStatus = 'overdue';
        logger.debug('Payment status updated to overdue:', {
          name: payment.name,
          dueDate: payment.dueDate,
          currentDate: this.today.toISOString().split('T')[0]
        });
      }

      const categorizedPayment = {
        ...payment,
        status: currentStatus,
        // Preservar flag de arrastre si existe.
        isCarriedOver: (payment as any).isCarriedOver ?? undefined
      } as PaymentItem & { isCarriedOver?: boolean };

      // Categorizar según el estado actual con prioridad para pagos marcados como completados
      switch (currentStatus) {
        case 'paid':
          paid.push(categorizedPayment);
          logger.debug('Payment categorized as paid:', {
            name: payment.name,
            paidDate: payment.paidDate,
            recordId: payment.recordId
          });
          break;
        case 'pending':
          pending.push(categorizedPayment);
          break;
        case 'overdue':
          overdue.push(categorizedPayment);
          break;
        default:
          logger.warn('Unknown payment status:', currentStatus, 'for payment:', payment.name);
          // Agregar a pending por defecto
          pending.push({ ...categorizedPayment, status: 'pending' });
      }
    });

    // Ordenar por fecha de vencimiento
    const sortByDueDate = (a: PaymentItem, b: PaymentItem) => 
      new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();

    // Ordenar pagos completados por fecha de pago (más recientes primero)
    const sortPaidByDate = (a: PaymentItem, b: PaymentItem) => {
      const dateA = new Date(a.paidDate || a.dueDate).getTime();
      const dateB = new Date(b.paidDate || b.dueDate).getTime();
      return dateB - dateA;
    };

    const categorized = {
      pending: pending.sort(sortByDueDate),
      paid: paid.sort(sortPaidByDate),
      overdue: overdue.sort(sortByDueDate)
    };

    logger.debug('Payments categorized successfully:', {
      pending: categorized.pending.length,
      paid: categorized.paid.length,
      overdue: categorized.overdue.length
    });

    return categorized;
  }

  calculateTotals(categorizedPayments: CategorizedPayments, exchangeRate: number): PaymentTotals {
    const getAmountInDOP = (amount: number, currency: 'DOP' | 'USD') => {
      if (currency === 'USD') {
        return amount * exchangeRate;
      }
      return amount;
    };
    
    const pendingTotal = categorizedPayments.pending.reduce((sum, payment) => sum + getAmountInDOP(payment.amount, payment.currency), 0);
    const overdueTotal = categorizedPayments.overdue.reduce((sum, payment) => sum + getAmountInDOP(payment.amount, payment.currency), 0);
    
    // Calcular pagos del mes actual basado en la fecha de pago
    const paidThisMonth = categorizedPayments.paid.filter(payment => {
      const paidDate = new Date(payment.paidDate || payment.dueDate);
      const isCurrentMonth = paidDate.getMonth() === this.currentMonth && paidDate.getFullYear() === this.currentYear;
      
      if (isCurrentMonth) {
        logger.debug('Payment counted for current month:', {
          name: payment.name,
          paidDate: payment.paidDate || payment.dueDate,
          amount: payment.amount
        });
      }
      
      return isCurrentMonth;
    }).reduce((sum, payment) => sum + getAmountInDOP(payment.amount, payment.currency), 0);

    const totals = {
      pending: pendingTotal,
      overdue: overdueTotal,
      paidThisMonth
    };

    logger.debug('Payment totals calculated (in DOP):', { ...totals, exchangeRate });

    return totals;
  }
}
