
export interface FinancialRecommendation {
  title: string;
  description: string;
  impact: string;
  effort: 'Bajo' | 'Medio' | 'Alto';
  timeframe: 'immediate' | 'short' | 'medium' | 'long';
  priority: number;
}

export interface RecommendationParameters {
  savingsRate: number;
  emergencyFundMonths: number;
  debtToIncomeRatio: number;
  netBalance: number;
  totalExpenses: number;
  uncategorizedExpensePercentage: number;
}

export const generateRecommendations = (params: RecommendationParameters): FinancialRecommendation[] => {
  const recommendations: FinancialRecommendation[] = [];

  // Recomendaciones inmediatas
  if (params.uncategorizedExpensePercentage > 20) {
    recommendations.push({
      title: 'Categorizar Gastos "Otros"',
      description: `El ${params.uncategorizedExpensePercentage}% de sus gastos están sin categorizar. Identifique estos gastos para mejor control.`,
      impact: `Reducción potencial de 8-12% en gastos innecesarios`,
      effort: 'Bajo',
      timeframe: 'immediate',
      priority: 1
    });
  }

  if (params.emergencyFundMonths < 3) {
    const monthlyAmount = Math.round(params.totalExpenses * 0.15);
    recommendations.push({
      title: 'Establecer Fondo de Emergencia',
      description: `Destinar automáticamente RD$${monthlyAmount.toLocaleString()} mensuales hasta alcanzar 3 meses de gastos`,
      impact: 'Protección financiera crítica',
      effort: 'Medio',
      timeframe: 'immediate',
      priority: 2
    });
  }

  if (params.savingsRate < 15) {
    recommendations.push({
      title: 'Implementar Ahorro Automático',
      description: 'Configurar transferencia automática del 15% de ingresos a cuenta de ahorros',
      impact: 'Incremento patrimonial garantizado',
      effort: 'Bajo',
      timeframe: 'immediate',
      priority: 3
    });
  }

  // Recomendaciones a corto plazo
  if (params.debtToIncomeRatio > 25) {
    const annualSavings = params.totalExpenses * 12 * 0.08;
    recommendations.push({
      title: 'Consolidar Deudas de Alto Interés',
      description: 'Transferir saldos de tarjetas de crédito a préstamo personal con menor tasa',
      impact: `Ahorro aproximado de RD$${annualSavings.toLocaleString()} anuales`,
      effort: 'Medio',
      timeframe: 'short',
      priority: 4
    });
  }

  recommendations.push({
    title: 'Optimizar Contratos de Servicios',
    description: 'Revisar y renegociar seguros, telefonía e internet para optimizar costos',
    impact: 'Reducción 10-15% en servicios',
    effort: 'Medio',
    timeframe: 'short',
    priority: 5
  });

  // Recomendaciones a mediano plazo
  if (params.savingsRate >= 15) {
    recommendations.push({
      title: 'Estrategia de Inversión Diversificada',
      description: 'Implementar portafolio balanceado: 60% renta variable, 30% renta fija, 10% alternativos',
      impact: 'ROI proyectado 8-12% anual',
      effort: 'Medio',
      timeframe: 'medium',
      priority: 6
    });
  }

  const taxSavings = params.netBalance * 12 * 0.05;
  recommendations.push({
    title: 'Planificación Tributaria Avanzada',
    description: 'Maximizar deducciones fiscales y contribuciones a pensiones',
    impact: `Ahorro fiscal estimado RD$${taxSavings.toLocaleString()} anuales`,
    effort: 'Medio',
    timeframe: 'medium',
    priority: 7
  });

  return recommendations.sort((a, b) => a.priority - b.priority);
};

export const groupRecommendationsByTimeframe = (recommendations: FinancialRecommendation[]) => {
  return {
    immediate: recommendations.filter(r => r.timeframe === 'immediate'),
    short: recommendations.filter(r => r.timeframe === 'short'),
    medium: recommendations.filter(r => r.timeframe === 'medium'),
    long: recommendations.filter(r => r.timeframe === 'long')
  };
};
