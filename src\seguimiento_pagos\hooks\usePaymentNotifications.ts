import { useEffect, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { PaymentItem, CategorizedPayments } from '../types/paymentTypes';
import { differenceInDays, isToday, isTomorrow } from 'date-fns';

interface NotificationSettings {
  enableOverdueAlerts: boolean;
  enableUpcomingAlerts: boolean;
  daysBeforeDue: number;
}

export const usePaymentNotifications = (
  payments: CategorizedPayments,
  settings: NotificationSettings = {
    enableOverdueAlerts: true,
    enableUpcomingAlerts: true,
    daysBeforeDue: 3
  }
) => {
  const { toast } = useToast();

  // Calcular pagos que requieren notificación
  const notificationData = useMemo(() => {
    const today = new Date();
    
    const upcomingPayments = payments.pending.filter(payment => {
      const dueDate = new Date(payment.dueDate);
      const daysUntilDue = differenceInDays(dueDate, today);
      return daysUntilDue <= settings.daysBeforeDue && daysUntilDue >= 0;
    });

    const dueTodayPayments = payments.pending.filter(payment => 
      isToday(new Date(payment.dueDate))
    );

    const dueTomorrowPayments = payments.pending.filter(payment => 
      isTomorrow(new Date(payment.dueDate))
    );

    return {
      overdue: payments.overdue,
      upcomingPayments,
      dueTodayPayments,
      dueTomorrowPayments,
      totalOverdueAmount: payments.overdue.reduce((sum, p) => sum + p.amount, 0),
      totalUpcomingAmount: upcomingPayments.reduce((sum, p) => sum + p.amount, 0)
    };
  }, [payments, settings.daysBeforeDue]);

  // Mostrar notificaciones críticas
  useEffect(() => {
    if (!settings.enableOverdueAlerts) return;

    if (notificationData.overdue.length > 0) {
      toast({
        title: "⚠️ Pagos Vencidos",
        description: `Tienes ${notificationData.overdue.length} pago(s) vencido(s) por un total de $${notificationData.totalOverdueAmount.toFixed(2)}`,
        variant: "destructive",
      });
    }
  }, [
    notificationData.overdue.length,
    notificationData.totalOverdueAmount,
    settings.enableOverdueAlerts,
    toast,
  ]);

  // Mostrar notificaciones de pagos próximos
  useEffect(() => {
    if (!settings.enableUpcomingAlerts) return;

    if (notificationData.dueTodayPayments.length > 0) {
      toast({
        title: "📅 Pagos Vencen Hoy",
        description: `${notificationData.dueTodayPayments.length} pago(s) vencen hoy`,
        variant: "default",
      });
    }

    if (notificationData.dueTomorrowPayments.length > 0) {
      toast({
        title: "⏰ Pagos Vencen Mañana", 
        description: `${notificationData.dueTomorrowPayments.length} pago(s) vencen mañana`,
        variant: "default",
      });
    }
  }, [notificationData.dueTodayPayments.length, notificationData.dueTomorrowPayments.length, settings.enableUpcomingAlerts, toast]);

  return {
    notificationData,
    hasOverduePayments: notificationData.overdue.length > 0,
    hasUpcomingPayments: notificationData.upcomingPayments.length > 0,
    criticalPaymentsCount: notificationData.overdue.length + notificationData.dueTodayPayments.length
  };
};
