
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Loan } from '@/types';
import { calculateMonthlyPayment, calculateDueDate } from '../utils/loanCalculations';
import { LoanBasicInfoFields } from './LoanBasicInfoFields';
import { LoanCalculationFields } from './LoanCalculationFields';
import { LoanDateFields } from './LoanDateFields';

interface LoanFormProps {
  onSubmit: (loan: Omit<Loan, 'id'>) => void;
  onCancel: () => void;
  editingLoan?: Loan | null;
}

export function LoanForm({ onSubmit, onCancel, editingLoan }: LoanFormProps) {
  const [formData, setFormData] = useState({
    type: '',
    name: '',
    currency: 'DOP' as 'DOP' | 'USD',
    totalAmount: 0,
    monthlyPayment: 0,
    interestRate: 0,
    loanTerm: 0,
    startDate: new Date().toISOString().split('T')[0],
    dueDate: new Date().toISOString().split('T')[0],
    paymentDate: new Date().toISOString().split('T')[0],
    isActive: true
  });

  // Inicializar el formulario con los datos de edición
  useEffect(() => {
    if (editingLoan) {
      setFormData({
        type: editingLoan.type,
        name: editingLoan.name,
        currency: editingLoan.currency,
        totalAmount: editingLoan.totalAmount,
        monthlyPayment: editingLoan.monthlyPayment,
        interestRate: editingLoan.interestRate,
        loanTerm: editingLoan.loanTerm,
        startDate: editingLoan.startDate,
        dueDate: editingLoan.dueDate,
        paymentDate: editingLoan.paymentDate,
        isActive: editingLoan.isActive
      });
    } else {
      // Reset form for new loan
      setFormData({
        type: '',
        name: '',
        currency: 'DOP',
        totalAmount: 0,
        monthlyPayment: 0,
        interestRate: 0,
        loanTerm: 0,
        startDate: new Date().toISOString().split('T')[0],
        dueDate: new Date().toISOString().split('T')[0],
        paymentDate: new Date().toISOString().split('T')[0],
        isActive: true
      });
    }
  }, [editingLoan]);

  // Calcular automáticamente el pago mensual cuando cambien los valores relevantes
  useEffect(() => {
    if (formData.totalAmount > 0 && formData.loanTerm > 0 && formData.interestRate >= 0) {
      const calculatedPayment = calculateMonthlyPayment(
        formData.totalAmount,
        formData.interestRate,
        formData.loanTerm
      );
      
      setFormData(prev => ({
        ...prev,
        monthlyPayment: Math.round(calculatedPayment * 100) / 100 // Redondear a 2 decimales
      }));
    }
  }, [formData.totalAmount, formData.interestRate, formData.loanTerm]);

  // Calcular automáticamente la fecha de vencimiento cuando cambien la fecha de inicio o el plazo
  useEffect(() => {
    if (formData.startDate && formData.loanTerm > 0) {
      const calculatedDueDate = calculateDueDate(formData.startDate, formData.loanTerm);
      
      setFormData(prev => ({
        ...prev,
        dueDate: calculatedDueDate
      }));
    }
  }, [formData.startDate, formData.loanTerm]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || !formData.name || formData.totalAmount <= 0) {
      return;
    }
    
    onSubmit(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {editingLoan ? 'Editar Préstamo' : 'Nuevo Préstamo'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <LoanBasicInfoFields
              type={formData.type}
              name={formData.name}
              currency={formData.currency}
              onTypeChange={(value) => setFormData({ ...formData, type: value })}
              onNameChange={(value) => setFormData({ ...formData, name: value })}
              onCurrencyChange={(value) => setFormData({ ...formData, currency: value })}
            />

            <LoanCalculationFields
              totalAmount={formData.totalAmount}
              loanTerm={formData.loanTerm}
              interestRate={formData.interestRate}
              monthlyPayment={formData.monthlyPayment}
              currency={formData.currency}
              onTotalAmountChange={(value) => setFormData({ ...formData, totalAmount: value || 0 })}
              onLoanTermChange={(value) => setFormData({ ...formData, loanTerm: value || 0 })}
              onInterestRateChange={(value) => setFormData({ ...formData, interestRate: value || 0 })}
            />

            <LoanDateFields
              startDate={formData.startDate}
              dueDate={formData.dueDate}
              paymentDate={formData.paymentDate}
              onStartDateChange={(value) => setFormData({ ...formData, startDate: value })}
              onPaymentDateChange={(value) => setFormData({ ...formData, paymentDate: value })}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
            />
            <Label htmlFor="isActive">Préstamo activo</Label>
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1">
              {editingLoan ? 'Actualizar' : 'Agregar'} Préstamo
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
