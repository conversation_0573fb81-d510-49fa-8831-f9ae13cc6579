
import React from 'react';
import { usePDFDataEngine } from '../../core/PDFDataEngine';
import { createPageStyle, createHeaderStyle, createTableStyle, createTableCellStyle, createCardStyle, createBaseStyle, pdfStyles } from '../../styles/pdfStyles';

export const ProjectionsPage: React.FC = () => {
  const { 
    projections,
    milestones,
    formatCurrency, 
    reportMetadata 
  } = usePDFDataEngine();

  return (
    <div style={createPageStyle()}>
      {/* Header */}
      <div style={createHeaderStyle({ textAlign: 'center' })}>
        <h1 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize['3xl'], 
          fontWeight: '700',
          margin: '0 0 6px 0'
        })}>
          Proyecciones Financieras y Metas
        </h1>
        <p style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.lg,
          color: pdfStyles.colors.neutral[500],
          margin: 0
        })}>
          Panorama financiero proyectado a 5 años con retornos de inversión considerados
        </p>
      </div>

      {/* Tabla de Proyecciones */}
      <div style={{ marginBottom: pdfStyles.spacing['4xl'] }}>
        <h2 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center',
          borderBottom: `2px solid ${pdfStyles.colors.primary}`,
          paddingBottom: '5px'
        })}>
          📊 Proyecciones Financieras a 5 Años
        </h2>
        
        <table style={createTableStyle()}>
          <thead>
            <tr style={{ backgroundColor: pdfStyles.colors.neutral[100] }}>
              <th style={createTableCellStyle('center')}>Año</th>
              <th style={createTableCellStyle('right')}>Ingresos Anuales</th>
              <th style={createTableCellStyle('right')}>Gastos Anuales</th>
              <th style={createTableCellStyle('right')}>Ahorro Neto</th>
              <th style={createTableCellStyle('right')}>Patrimonio Total</th>
            </tr>
          </thead>
          <tbody>
            {projections.map((projection, index) => (
              <tr key={index}>
                <td style={createTableCellStyle('center')}>
                  {projection.year}
                </td>
                <td style={createTableCellStyle('right')}>
                  {formatCurrency(projection.income)}
                </td>
                <td style={createTableCellStyle('right')}>
                  {formatCurrency(projection.expenses)}
                </td>
                <td style={createTableCellStyle('right')}>
                  {formatCurrency(projection.savings)}
                </td>
                <td style={createTableCellStyle('right')}>
                  {formatCurrency(projection.totalAssets)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Hitos Financieros */}
      <div style={{ marginBottom: pdfStyles.spacing['4xl'] }}>
        <h2 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center',
          borderBottom: `2px solid ${pdfStyles.colors.primary}`,
          paddingBottom: '5px'
        })}>
          🎯 Hitos Financieros y Cronograma
        </h2>
        
        {milestones.map((milestone, index) => {
          const progressPercentage = Math.min(100, (milestone.currentProgress / milestone.target) * 100);
          
          return (
            <div key={index} style={{
              backgroundColor: 'white',
              border: `2px solid ${pdfStyles.colors.neutral[200]}`,
              borderRadius: '8px',
              padding: pdfStyles.spacing.lg,
              marginBottom: pdfStyles.spacing.md
            }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '8px'
              }}>
                <h3 style={createBaseStyle({ 
                  fontSize: pdfStyles.typography.fontSize.lg, 
                  fontWeight: '600',
                  margin: 0
                })}>
                  {milestone.milestone}
                </h3>
                <span style={{
                  backgroundColor: milestone.status === 'achieved' ? pdfStyles.colors.success : 
                                 milestone.status === 'pending' ? pdfStyles.colors.warning : pdfStyles.colors.neutral[500],
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: pdfStyles.typography.fontSize.xs,
                  fontWeight: '600'
                }}>
                  {milestone.status === 'achieved' ? 'Logrado' : 
                   milestone.status === 'pending' ? `${milestone.timeToGoal} años` : 'Largo Plazo'}
                </span>
              </div>
              
              <div style={{ marginBottom: '8px' }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  fontSize: pdfStyles.typography.fontSize.sm,
                  color: pdfStyles.colors.neutral[500]
                }}>
                  <span>Progreso: {formatCurrency(milestone.currentProgress)}</span>
                  <span>Meta: {formatCurrency(milestone.target)}</span>
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  backgroundColor: pdfStyles.colors.neutral[200],
                  borderRadius: '4px',
                  overflow: 'hidden',
                  marginTop: '4px'
                }}>
                  <div style={{
                    width: `${progressPercentage}%`,
                    height: '100%',
                    backgroundColor: milestone.status === 'achieved' ? pdfStyles.colors.success : pdfStyles.colors.primary,
                    borderRadius: '4px'
                  }}></div>
                </div>
              </div>
              
              <div style={createBaseStyle({ 
                fontSize: pdfStyles.typography.fontSize.xs, 
                color: pdfStyles.colors.neutral[500]
              })}>
                Progreso: {progressPercentage.toFixed(1)}% completado
              </div>
            </div>
          );
        })}
      </div>

      {/* Estrategias de Aceleración */}
      <div style={createCardStyle({
        backgroundColor: '#EFF6FF',
        borderColor: pdfStyles.colors.primary,
        marginBottom: pdfStyles.spacing['2xl']
      })}>
        <h3 style={createBaseStyle({ 
          fontSize: pdfStyles.typography.fontSize.xl, 
          fontWeight: '700', 
          color: '#1E40AF',
          marginBottom: pdfStyles.spacing.lg,
          textAlign: 'center'
        })}>
          🚀 Estrategias de Aceleración
        </h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: pdfStyles.spacing.lg }}>
          <div>
            <h4 style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.base, fontWeight: '600', color: '#1E40AF', marginBottom: '8px' })}>
              💰 Incremento de Ingresos
            </h4>
            <ul style={{ margin: 0, paddingLeft: '15px', fontSize: pdfStyles.typography.fontSize.xs, color: '#1E3A8A' }}>
              <li>Desarrollo de habilidades premium (+15% ingresos)</li>
              <li>Ingresos pasivos por inversiones (+8% anual)</li>
              <li>Consultoría o servicios profesionales</li>
            </ul>
          </div>
          
          <div>
            <h4 style={createBaseStyle({ fontSize: pdfStyles.typography.fontSize.base, fontWeight: '600', color: '#1E40AF', marginBottom: '8px' })}>
              📉 Optimización de Gastos
            </h4>
            <ul style={{ margin: 0, paddingLeft: '15px', fontSize: pdfStyles.typography.fontSize.xs, color: '#1E3A8A' }}>
              <li>Renegociación de servicios (-10% costos fijos)</li>
              <li>Automatización de ahorros (+5% tasa)</li>
              <li>Inversión en educación financiera</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Footer con recordatorios */}
      <div style={{
        borderTop: `2px solid ${pdfStyles.colors.neutral[200]}`,
        paddingTop: pdfStyles.spacing.lg,
        display: 'flex',
        justifyContent: 'space-between',
        fontSize: pdfStyles.typography.fontSize.xs,
        color: pdfStyles.colors.neutral[500]
      }}>
        <div>
          <div style={{ fontWeight: '600', marginBottom: '3px' }}>📋 Recordatorios de Revisión</div>
          <div>• Revisar proyecciones cada 6 meses</div>
          <div>• Ajustar metas según cambios de ingresos</div>
          <div>• Próxima evaluación: {reportMetadata.nextReviewDate}</div>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div style={{ fontWeight: '600', marginBottom: '3px' }}>⚠️ Consideraciones Importantes</div>
          <div>• Proyecciones incluyen ROI del 8% anual</div>
          <div>• Considerar factores económicos externos</div>
          <div>• Mantener flexibilidad en la planificación</div>
        </div>
      </div>
    </div>
  );
};
