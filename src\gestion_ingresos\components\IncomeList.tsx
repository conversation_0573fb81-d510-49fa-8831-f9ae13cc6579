
import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, DollarSign } from 'lucide-react';
import { formatCurrency } from '@/components/ui/numeric-input';
import { Income } from '@/types';

interface IncomeListProps {
  incomes: Income[];
  onEdit: (income: Income) => void;
  onDelete: (id: string) => void;
}

export function IncomeList({ incomes, onEdit, onDelete }: IncomeListProps) {
  const getMonthName = (monthStr: string) => {
    // Asegurar que el formato sea YYYY-MM y crear la fecha correctamente
    const [year, month] = monthStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);
    return date.toLocaleDateString('es-ES', { 
      month: 'long', 
      year: 'numeric' 
    });
  };

  const formatMonthForDisplay = (monthStr: string) => {
    // Mostrar el mes exacto que se seleccionó
    return getMonthName(monthStr);
  };

  if (incomes.length === 0) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <DollarSign className="w-12 h-12 text-finanz-text-secondary mx-auto mb-4" />
          <p className="text-finanz-text-secondary">No hay ingresos registrados</p>
          <p className="text-sm text-finanz-text-secondary mt-1">
            Comienza agregando tu primer ingreso mensual
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {incomes.map((income) => (
        <Card key={income.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {formatMonthForDisplay(income.month)}
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-finanz-success/10 text-finanz-success border-finanz-success">
                  {income.currency}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {income.month}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-finanz-text-secondary">Sueldo Fijo</p>
                <p className="font-semibold text-finanz-success">
                  {formatCurrency(income.fixedSalary, income.currency)}
                </p>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-finanz-text-secondary">Ingreso Bruto</p>
                <p className="font-semibold text-finanz-primary">
                  {formatCurrency(income.grossIncome, income.currency)}
                </p>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-finanz-text-secondary">Ingreso Neto</p>
                <p className="font-bold text-lg text-finanz-success">
                  {formatCurrency(income.netIncome, income.currency)}
                </p>
              </div>
            </div>
            
            {income.otherIncomeItems && income.otherIncomeItems.length > 0 && (
              <div className="mt-4 pt-4 border-t">
                <p className="text-sm text-finanz-text-secondary mb-2">Otros Ingresos:</p>
                <div className="flex flex-wrap gap-2">
                  {income.otherIncomeItems.map((item) => (
                    <Badge key={item.id} variant="secondary" className="text-xs">
                      {item.description}: {formatCurrency(item.amount, item.currency)}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <div className="flex gap-2 mt-4 pt-4 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(income)}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Editar
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onDelete(income.id)}
                className="flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                Eliminar
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
