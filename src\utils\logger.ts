export const debug = (...args: unknown[]): void => {
  if (!import.meta.env.PROD) {
    console.debug(...args);
  }
};

export const info = (...args: unknown[]): void => {
  if (!import.meta.env.PROD) {
    console.info(...args);
  }
};

export const warn = (...args: unknown[]): void => {
  if (!import.meta.env.PROD) {
    console.warn(...args);
  }
};

export const error = (...args: unknown[]): void => {
  if (!import.meta.env.PROD) {
    console.error(...args);
  }
};

export const logger = { debug, info, warn, error };
export default logger;
