import React, { useEffect } from 'react';
import { Income as IncomeType } from '@/types';
import { useSecureFormValidation } from '@/hooks/useSecureFormValidation';
import { incomeFormSchema } from '@/utils/security/formValidation';
import { SecurityAlerts } from '@/components/security/SecurityAlerts';
import { BasicInfoSection } from './BasicInfoSection';
import { MainIncomeSection } from './MainIncomeSection';
import { OtherIncomeSection } from './OtherIncomeSection';
import { VariableComponentSection } from './VariableComponentSection';
import { DeductionsSection } from './DeductionsSection';
import { SummarySection } from './SummarySection';
import { FormActions } from './FormActions';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface IncomeCalculations {
  netIncome: number;
  totalDeductions: number;
  variableComponents: number;
  [key: string]: number;
}

interface IncomeFormDataProps {
  formData: Partial<IncomeType>;
  setFormData: React.Dispatch<React.SetStateAction<Partial<IncomeType>>>;
  defaultCurrency: 'DOP' | 'USD';
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  editingIncome: IncomeType | null;
  calculations: IncomeCalculations;
  isSubmitting?: boolean;
}

export function IncomeFormData({
  formData,
  setFormData,
  defaultCurrency,
  onSubmit,
  onCancel,
  editingIncome,
  calculations,
  isSubmitting
}: IncomeFormDataProps) {
  const { isMobile, isTablet } = useBreakpoint();
  const {
    errors,
    isValidating,
    csrfToken,
    validateForm,
    initializeCSRF,
    clearErrors
  } = useSecureFormValidation(incomeFormSchema, true);

  useEffect(() => {
    initializeCSRF();
  }, [initializeCSRF]);

  const handleSecureSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = await validateForm({
      ...formData,
      csrf_token: csrfToken
    });

    if (!validation.isValid) {
      return; // Errors will be displayed automatically
    }

    // Call the original onSubmit with the validated data
    onSubmit(e);
  };

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors.length > 0) {
      clearErrors();
    }
  };

  return (
    <div className={`
      ${isMobile ? 'space-y-3' : isTablet ? 'space-y-4' : 'space-y-6'}
    `}>
      <SecurityAlerts />
      
      <form onSubmit={handleSecureSubmit} className={`
        ${isMobile ? 'space-y-3' : isTablet ? 'space-y-4' : 'space-y-6'}
      `}>
        <input type="hidden" name="csrf_token" value={csrfToken} />
        
        {errors.length > 0 && (
          <div className={`
            bg-red-50 border border-red-200 rounded-md
            ${isMobile ? 'p-3' : 'p-4'}
          `}>
            <h4 className={`
              text-red-800 font-medium
              ${isMobile ? 'text-sm' : 'text-base'}
            `}>
              Errores de Validación:
            </h4>
            <ul className={`
              mt-2 text-red-700
              ${isMobile ? 'text-xs' : 'text-sm'}
            `}>
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        <BasicInfoSection 
          formData={formData}
          setFormData={setFormData}
          isSubmitting={isSubmitting}
        />

        <MainIncomeSection 
          formData={formData}
          setFormData={setFormData}
          isSubmitting={isSubmitting}
        />

        <OtherIncomeSection 
          formData={formData}
          setFormData={setFormData}
          defaultCurrency={defaultCurrency}
          isSubmitting={isSubmitting}
        />

        <VariableComponentSection 
          formData={formData}
          setFormData={setFormData}
          calculations={calculations}
          isSubmitting={isSubmitting}
        />

        <DeductionsSection 
          formData={formData}
          setFormData={setFormData}
          calculations={calculations}
          isSubmitting={isSubmitting}
        />

        <SummarySection 
          formData={formData}
          calculations={calculations}
        />

        <FormActions 
          onCancel={onCancel}
          editingIncome={editingIncome}
          isValidating={isValidating}
          hasErrors={errors.length > 0}
          isSubmitting={isSubmitting}
        />
      </form>
    </div>
  );
}
