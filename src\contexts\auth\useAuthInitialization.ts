import { logger } from "@/utils/logger";

import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client'; // Added import

// interface UseAuthInitializationProps {
//   // setLoading: (loading: boolean) => void; // Not used
// }

const SESSION_ACTIVE_KEY = 'finanz_session_active_flag'; // Added session key

export const useAuthInitialization = () => {
  useEffect(() => {
    const initializeAuth = async () => {
      // Check for active session flag in sessionStorage
      const sessionActive = sessionStorage.getItem(SESSION_ACTIVE_KEY);

      if (sessionActive !== 'true') {
        logger.debug(
          'useAuthInitialization: No active session flag. Checking existing session before signing out.'
        );

        try {
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (!session) {
            await supabase.auth.signOut(); // Sign out only when no session exists
          }
        } catch (error) {
          console.error('useAuthInitialization: Error checking session:', error);
        } finally {
          // Avoid repeated checks across tab reloads
          sessionStorage.setItem(SESSION_ACTIVE_KEY, 'true');
        }
      } else {
        logger.debug('useAuthInitialization: Active session flag found.');
      }

      logger.debug('useAuthInitialization: Auth initialization previously delegated to useAuthState');
      // La inicialización principal ahora se maneja en useAuthState
      // Este hook puede usarse para lógica adicional de inicialización si es necesaria
    };

    initializeAuth();
  }, []); // Ensure this runs only once on mount by using an empty dependency array
};
