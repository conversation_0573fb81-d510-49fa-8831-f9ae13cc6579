
import React from 'react';
import { ProfessionalDashboard } from '@/dashboard_principal/components/ProfessionalDashboard';
import { useBreakpoint } from '@/hooks/useBreakpoint';

export type DashboardProfessionalProps = Record<string, never>; // Explicitly no props

const DashboardProfessional: React.FC<DashboardProfessionalProps> = () => {
  const { isMobile } = useBreakpoint();
  
  return (
    <div className={`${isMobile ? 'min-h-screen' : ''}`}>
      <ProfessionalDashboard />
    </div>
  );
};

export default DashboardProfessional;
