
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { NumericInput } from '@/components/ui/numeric-input';
import { Subscription } from '@/types';
import { useToast } from '@/hooks/use-toast';

interface SubscriptionFormProps {
  onSubmit: (subscription: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  editingSubscription?: Subscription | null;
}

export function SubscriptionForm({ onSubmit, onCancel, editingSubscription }: SubscriptionFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    name: editingSubscription?.name || '',
    amount: editingSubscription?.amount || 0,
    categoryId: editingSubscription?.categoryId || '',
    categoryName: editingSubscription?.categoryName || '',
    billingDate: editingSubscription?.billingDate || '1',
    isActive: editingSubscription?.isActive ?? true,
    currency: editingSubscription?.currency || 'DOP' as 'DOP' | 'USD'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.categoryId || formData.amount <= 0) {
      toast({
        title: "Error",
        description: "Por favor complete todos los campos requeridos",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting subscription:', error);
      toast({
        title: "Error",
        description: "No se pudo guardar la suscripción",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const categories = [
    { id: 'streaming', name: 'Streaming' },
    { id: 'software', name: 'Software' },
    { id: 'utilities', name: 'Servicios Públicos' },
    { id: 'other', name: 'Otros' }
  ];

  const handleCategoryChange = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    setFormData({ 
      ...formData, 
      categoryId, 
      categoryName: category?.name || '' 
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {editingSubscription ? 'Editar Suscripción' : 'Nueva Suscripción'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nombre de la suscripción"
                required
                disabled={isSubmitting}
                autoComplete="name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Categoría *</Label>
              <Select 
                value={formData.categoryId} 
                onValueChange={handleCategoryChange}
                disabled={isSubmitting}
              >
                <SelectTrigger id="category" autoComplete="off">
                  <SelectValue placeholder="Seleccionar categoría" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Monto *</Label>
              <NumericInput
                id="amount"
                value={formData.amount}
                onChange={(value) => setFormData({ ...formData, amount: value || 0 })}
                currency={formData.currency}
                showCurrency
                disabled={isSubmitting}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Moneda</Label>
              <Select 
                value={formData.currency} 
                onValueChange={(value: 'DOP' | 'USD') => setFormData({ ...formData, currency: value })}
                disabled={isSubmitting}
              >
                <SelectTrigger id="currency" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOP">Pesos (DOP)</SelectItem>
                  <SelectItem value="USD">Dólares (USD)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="billingDate">Día de Facturación *</Label>
              <Select 
                value={formData.billingDate} 
                onValueChange={(value) => setFormData({ ...formData, billingDate: value })}
                disabled={isSubmitting}
              >
                <SelectTrigger id="billingDate" autoComplete="off">
                  <SelectValue placeholder="Seleccionar día" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                    <SelectItem key={day} value={day.toString()}>
                      {day}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="isActive">Estado</Label>
              <Select 
                value={String(formData.isActive)} 
                onValueChange={(value: string) => setFormData({ ...formData, isActive: value === 'true' })}
                disabled={isSubmitting}
              >
                <SelectTrigger id="isActive" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Activa</SelectItem>
                  <SelectItem value="false">Inactiva</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button 
              type="submit" 
              className="flex-1"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Guardando...' : editingSubscription ? 'Actualizar' : 'Agregar'} Suscripción
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
