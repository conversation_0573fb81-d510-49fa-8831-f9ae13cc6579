// Utilidades de rendimiento para optimizar el trabajo del hilo principal

/**
 * Ejecuta una función de forma diferida usando requestIdleCallback
 * para no bloquear el hilo principal
 */
export const deferExecution = <T extends (...args: any[]) => any>(
  fn: T,
  options?: IdleRequestOptions
): ((...args: Parameters<T>) => void) => {
  return (...args: Parameters<T>) => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => fn(...args), options);
    } else {
      // Fallback para navegadores que no soportan requestIdleCallback
      setTimeout(() => fn(...args), 0);
    }
  };
};

/**
 * Debounce optimizado para reducir el trabajo del hilo principal
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
};

/**
 * Throttle optimizado para limitar la frecuencia de ejecución
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Ejecuta tareas en lotes pequeños para no bloquear el hilo principal
 */
export const batchProcess = async <T, R>(
  items: T[],
  processor: (item: T) => R | Promise<R>,
  batchSize = 10,
  delay = 0
): Promise<R[]> => {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(processor));
    results.push(...batchResults);
    
    // Dar tiempo al navegador para otras tareas
    if (delay > 0 && i + batchSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return results;
};

/**
 * Memoización optimizada para evitar recálculos innecesarios
 */
export const memoize = <T extends (...args: any[]) => any>(
  fn: T,
  getKey?: (...args: Parameters<T>) => string
): T => {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const result = fn(...args);
    cache.set(key, result);
    
    // Limitar el tamaño del cache para evitar memory leaks
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return result;
  }) as T;
};

/**
 * Observador de rendimiento para detectar tareas pesadas
 */
export class PerformanceMonitor {
  private observer: PerformanceObserver | null = null;
  private longTaskThreshold = 50; // ms
  
  constructor(threshold = 50) {
    this.longTaskThreshold = threshold;
  }
  
  start(callback?: (entry: PerformanceEntry) => void) {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver no está disponible');
      return;
    }
    
    this.observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > this.longTaskThreshold) {
          console.warn(`Tarea larga detectada: ${entry.name} (${entry.duration}ms)`);
          callback?.(entry);
        }
      }
    });
    
    try {
      this.observer.observe({ entryTypes: ['longtask', 'measure'] });
    } catch (e) {
      console.warn('No se pudo iniciar el monitor de rendimiento:', e);
    }
  }
  
  stop() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
  
  measure(name: string, fn: () => void | Promise<void>) {
    performance.mark(`${name}-start`);
    
    const result = fn();
    
    if (result instanceof Promise) {
      return result.finally(() => {
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
      });
    } else {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      return result;
    }
  }
}

/**
 * Utilidad para cargar scripts de forma asíncrona sin bloquear
 */
export const loadScriptAsync = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    script.defer = true;
    
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    
    document.head.appendChild(script);
  });
};

/**
 * Utilidad para precargar recursos críticos
 */
export const preloadResource = (href: string, as: string, type?: string) => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (type) link.type = type;
  
  document.head.appendChild(link);
};

/**
 * Detectar si el dispositivo tiene capacidades limitadas
 */
export const isLowEndDevice = (): boolean => {
  // Detectar basado en memoria disponible
  if ('deviceMemory' in navigator) {
    return (navigator as any).deviceMemory <= 2;
  }
  
  // Detectar basado en número de cores
  if ('hardwareConcurrency' in navigator) {
    return navigator.hardwareConcurrency <= 2;
  }
  
  // Detectar basado en connection
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return connection.effectiveType === 'slow-2g' || 
           connection.effectiveType === '2g' ||
           connection.saveData;
  }
  
  return false;
};

/**
 * Configuración adaptativa basada en las capacidades del dispositivo
 */
export const getAdaptiveConfig = () => {
  const isLowEnd = isLowEndDevice();
  
  return {
    enableAnimations: !isLowEnd,
    chunkSize: isLowEnd ? 5 : 10,
    preloadDistance: isLowEnd ? 25 : 50,
    debounceDelay: isLowEnd ? 300 : 150,
    enableHeavyFeatures: !isLowEnd,
  };
};

// Instancia global del monitor de rendimiento
export const performanceMonitor = new PerformanceMonitor();

// Inicializar en desarrollo
if (import.meta.env.DEV) {
  performanceMonitor.start();
}
