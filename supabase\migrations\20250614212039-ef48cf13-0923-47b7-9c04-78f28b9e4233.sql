
CREATE OR REPLACE FUNCTION public.apply_credit_card_payment(
    p_card_id uuid,
    p_user_id uuid,
    p_amount numeric,
    p_paid_date date,
    p_notes text,
    p_currency text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    -- 1. Actualizar el balance de la tarjeta de crédito
    UPDATE public.credit_cards
    SET current_balance = current_balance - p_amount,
        updated_at = now()
    WHERE id = p_card_id AND user_id = p_user_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Credit card not found or user not authorized';
    END IF;

    -- 2. Insertar el registro del pago
    INSERT INTO public.payment_records(user_id, reference_id, payment_type, amount, currency, status, paid_date, notes)
    VALUES (p_user_id, p_card_id, 'credit_card', p_amount, p_currency, 'paid', p_paid_date, p_notes);
END;
$$;
