
-- Fases 1 y 6 del Plan: Limpiar registros de pago duplicados y prevenir futuros duplicados.
-- Identificamos duplicados por la combinación de `user_id`, `payment_type` y `reference_id`,
-- y conservamos solo el registro actualizado más recientemente para cada grupo.

-- Primero, usamos una CTE (Common Table Expression) para numerar las filas dentro de cada grupo de duplicados.
-- El más reciente obtiene el número 1.
WITH duplicates AS (
  SELECT
    id,
    ROW_NUMBER() OVER(
      PARTITION BY user_id, payment_type, reference_id
      ORDER BY updated_at DESC, created_at DESC
    ) as rn
  FROM
    public.payment_records
)
-- Luego, eliminamos todos los registros que no son el más reciente (rn > 1).
DELETE FROM public.payment_records
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Finalmente, para evitar que se creen nuevos duplicados, agregamos una restricción de unicidad (unique constraint) a la tabla.
-- Esto asegura que la combinación de `user_id`, `payment_type` y `reference_id` sea siempre única.
-- Si la restricción ya existe por alguna razón, este comando podría fallar, lo cual es seguro y no afectará la limpieza ya realizada.
ALTER TABLE public.payment_records
ADD CONSTRAINT payment_records_unique_payment_key UNIQUE (user_id, payment_type, reference_id);

