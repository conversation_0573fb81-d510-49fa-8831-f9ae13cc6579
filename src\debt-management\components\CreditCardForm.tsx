
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { NumericInput, PercentageInput } from '@/components/ui/numeric-input';
import { CreditCard } from '@/types';
import { Loader2 } from 'lucide-react';

interface CreditCardFormProps {
  onSubmit: (creditCard: Omit<CreditCard, 'id' | 'userId'>) => Promise<void> | void; // Adjusted Omit and added Promise
  onCancel: () => void;
  editingCreditCard?: CreditCard | null;
  isSubmitting?: boolean;
}

export function CreditCardForm({ onSubmit, onCancel, editingCreditCard, isSubmitting = false }: CreditCardFormProps) {
  const [formData, setFormData] = useState({
    name: editingCreditCard?.name || '',
    currency: editingCreditCard?.currency || 'DOP' as 'DOP' | 'USD',
    creditLimit: editingCreditCard?.creditLimit || 0,
    currentBalance: editingCreditCard?.currentBalance || 0,
    minimumPayment: editingCreditCard?.minimumPayment || 0,
    cutoffDate: editingCreditCard?.cutoffDate || new Date().toISOString().split('T')[0],
    paymentDueDate: editingCreditCard?.paymentDueDate || new Date().toISOString().split('T')[0],
    paymentDate: editingCreditCard?.paymentDate || new Date().toISOString().split('T')[0],
    interestRate: editingCreditCard?.interestRate || 0,
    isActive: editingCreditCard?.isActive ?? true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {editingCreditCard ? 'Editar Tarjeta de Crédito' : 'Nueva Tarjeta de Crédito'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ej: Popular Visa"
                required
                disabled={isSubmitting}
                autoComplete="cc-name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Moneda</Label>
              <Select
                value={formData.currency}
                onValueChange={(value: 'DOP' | 'USD') => setFormData({ ...formData, currency: value })}
                disabled={isSubmitting}
              >
                <SelectTrigger id="currency" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DOP">Pesos (DOP)</SelectItem>
                  <SelectItem value="USD">Dólares (USD)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="creditLimit">Límite de Crédito *</Label>
              <NumericInput
                id="creditLimit"
                value={formData.creditLimit}
                onChange={(value) => setFormData({ ...formData, creditLimit: value || 0 })}
                currency={formData.currency}
                showCurrency
                disabled={isSubmitting}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currentBalance">Balance Actual</Label>
              <NumericInput
                id="currentBalance"
                value={formData.currentBalance}
                onChange={(value) => setFormData({ ...formData, currentBalance: value || 0 })}
                currency={formData.currency}
                showCurrency
                disabled={isSubmitting}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="minimumPayment">Pago Mínimo</Label>
              <NumericInput
                id="minimumPayment"
                value={formData.minimumPayment}
                onChange={(value) => setFormData({ ...formData, minimumPayment: value || 0 })}
                currency={formData.currency}
                showCurrency
                disabled={isSubmitting}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="interestRate">Tasa de Interés (%)</Label>
              <PercentageInput
                id="interestRate"
                value={formData.interestRate}
                onChange={(value) => setFormData({ ...formData, interestRate: value || 0 })}
                placeholder="Ej: 20.99"
                disabled={isSubmitting}
                autoComplete="off"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cutoffDate">Fecha de Corte</Label>
              <Input
                id="cutoffDate"
                type="date"
                value={formData.cutoffDate}
                onChange={(e) => setFormData({ ...formData, cutoffDate: e.target.value })}
                disabled={isSubmitting}
                autoComplete="off"
              />
              <p className="text-xs text-gray-500">Día en que cierra el ciclo de facturación</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentDueDate">Fecha Límite de Pago</Label>
              <Input
                id="paymentDueDate"
                type="date"
                value={formData.paymentDueDate}
                onChange={(e) => setFormData({ ...formData, paymentDueDate: e.target.value })}
                disabled={isSubmitting}
                autoComplete="off"
              />
              <p className="text-xs text-gray-500">Último día para pagar sin mora</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentDate">Fecha de Pago Programado</Label>
              <Input
                id="paymentDate"
                type="date"
                value={formData.paymentDate}
                onChange={(e) => setFormData({ ...formData, paymentDate: e.target.value })}
                disabled={isSubmitting}
                autoComplete="off"
              />
              <p className="text-xs text-gray-500">Fecha en que planeas realizar el pago</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="isActive">Estado</Label>
              <Select
                value={String(formData.isActive)}
                onValueChange={(value: string) => setFormData({ ...formData, isActive: value === 'true' })}
                disabled={isSubmitting}
              >
                <SelectTrigger id="isActive" autoComplete="off">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Activa</SelectItem>
                  <SelectItem value="false">Inactiva</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1 min-w-[180px]" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {editingCreditCard ? 'Actualizando...' : 'Agregando...'}
                </>
              ) : (
                `${editingCreditCard ? 'Actualizar' : 'Agregar'} Tarjeta`
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
