
import { GoogleGenerativeAI } from "npm:@google/generative-ai@^0.15.0";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// CORS headers más permisivos para solucionar el problema definitivamente
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "*, Authorization", // ✅ Añadir 'Authorization' explícitamente
  "Access-Control-Allow-Methods": "*",
  "Access-Control-Max-Age": "86400",
};

serve(async (req) => {
  // Manejar preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { 
      headers: corsHeaders,
      status: 200
    });
  }

  try {
    const apiKey = Deno.env.get("GEMINI_API_KEY");
    if (!apiKey) {
      console.error("GEMINI_API_KEY no está configurada en los secretos de la Edge Function.");
      throw new Error("La clave de API de Gemini no está configurada en el servidor.");
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    // Usar modelo actual disponible en lugar del deprecado gemini-pro
    const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
    const prompt = `Responde EXCLUSIVAMENTE con un objeto JSON válido que tenga esta estructura: {"rate": NUMERO}. El número es la tasa de cambio actual de USD a DOP. No incluyas ningún otro texto ni saltos de línea.`;

    const result = await model.generateContent(prompt);
    const text = result.response.text();
    
    // Limpiar el texto para asegurarnos de que sea un JSON válido
    const cleanedText = text.replace(/```json/g, "").replace(/```/g, "").trim();
    const data = JSON.parse(cleanedText);

    if (typeof data.rate !== "number") {
      console.error("La respuesta de la IA no fue un número válido:", cleanedText);
      return new Response(JSON.stringify({ error: "La IA no devolvió un número válido." }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
    
    console.info("Tasa de cambio obtenida exitosamente:", data.rate);
    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    console.error("Error en la Edge Function get-exchange-rate:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
