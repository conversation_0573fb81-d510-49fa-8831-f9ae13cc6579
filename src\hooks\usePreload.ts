import { useCallback } from 'react';

// Mapeo de rutas a componentes para preload inteligente
const ROUTE_PRELOAD_MAP: Record<string, Array<() => Promise<unknown>>> = {};

// Hook para preload manual
export const usePreload = () => {
  const preloadRoute = useCallback((path: string) => {
    const preloaders = ROUTE_PRELOAD_MAP[path];
    if (preloaders) {
      preloaders.forEach(preloader => {
        preloader().catch(console.warn);
      });
    }
  }, []);

  return { preloadRoute };
};
