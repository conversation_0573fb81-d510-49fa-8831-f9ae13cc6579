
import React, { forwardRef, useImperativeHandle } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Loader2 } from 'lucide-react';
import { Expense } from '@/types';
import { ExpenseFormFields } from './forms/ExpenseFormFields';
import { useExpenseForm } from './forms/useExpenseForm';

export interface ExpenseFormProps {
  onAddExpense: (expense: Omit<Expense, 'id' | 'month' | 'createdAt' | 'updatedAt'>) => Promise<void>; // Made async
  defaultCurrency: 'DOP' | 'USD';
  isAddingExpense?: boolean;
  isLoadingExpenses?: boolean; // Though not directly used here for now, kept as per instruction
}

export interface ExpenseFormHandle {
  resetForm: () => void;
}

export const ExpenseForm = React.memo(
  forwardRef<ExpenseFormHandle, ExpenseFormProps>(function ExpenseForm(
    { onAddExpense, defaultCurrency, isAddingExpense = false, isLoadingExpenses = false },
    ref
  ) {
    const { formData, handleFormDataChange, handleSubmit, resetForm } = useExpenseForm(onAddExpense, defaultCurrency);

    useImperativeHandle(ref, () => ({
      resetForm
    }), [resetForm]);

    return (
      <Card className="border-finanz-border">
        <CardHeader>
          <CardTitle className="text-finanz-danger flex items-center space-x-2">
            <Plus className="w-5 h-5" />
            <span>Registrar Gasto</span>
          </CardTitle>
          <CardDescription>
            Ingresa los detalles de tu nuevo gasto
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <ExpenseFormFields
              formData={formData}
              onFormDataChange={handleFormDataChange}
              disabled={isAddingExpense}
            />
            <Button
              type="submit"
              className="w-full bg-finanz-danger hover:bg-finanz-danger/90 min-h-[40px]" // Added min-h for consistent height
              disabled={isAddingExpense}
            >
              {isAddingExpense ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Registrando Gasto...
                </>
              ) : (
                'Registrar Gasto'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    );
  })
);
