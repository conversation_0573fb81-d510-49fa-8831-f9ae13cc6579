
import { useFinanceData } from '@/hooks/useFinanceData';
import { useDashboardPayments } from './useDashboardPayments';

export const useDashboardData = () => {
  const {
    getTotalDebt,
    getPendingReimbursements,
    getDebtToIncomeRatio,
    getEmergencyFundMonths
  } = useFinanceData();

  const {
    netIncome,
    totalMonthlyPayments, // Este es el valor correcto para "pagos del mes"
    netBalance,
    savingsRate,
    paymentToIncomeRatio,
    paymentBreakdown
  } = useDashboardPayments();

  const totalDebt = getTotalDebt();
  const pendingReimbursements = getPendingReimbursements();
  const debtToIncomeRatio = getDebtToIncomeRatio();
  const emergencyFundMonths = getEmergencyFundMonths();

  return {
    netIncome,
    totalMonthlyPayments, // Este representa los "pagos del mes"
    netBalance,
    totalDebt,
    pendingReimbursements,
    savingsRate,
    debtToIncomeRatio,
    emergencyFundMonths,
    paymentToIncomeRatio,
    paymentBreakdown
  };
};
