import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Shield, Eye, Database, Lock, Mail } from 'lucide-react';

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver al inicio
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Política de Privacidad</h1>
          <p className="text-gray-600 text-lg">
            Última actualización: {new Date().toLocaleDateString('es-ES')}
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 space-y-8">
          <section>
            <div className="flex items-center mb-4">
              <Shield className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">1. Información que recopilamos</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>
                En FinanzApp recopilamos únicamente la información necesaria para proporcionarte 
                nuestros servicios de gestión financiera personal:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Información de cuenta:</strong> Email, nombre completo y foto de perfil (si usas Google OAuth)</li>
                <li><strong>Datos financieros:</strong> Ingresos, gastos, deudas y metas financieras que ingreses voluntariamente</li>
                <li><strong>Información técnica:</strong> Dirección IP, tipo de navegador y datos de uso para mejorar el servicio</li>
              </ul>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <Eye className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">2. Cómo usamos tu información</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>Utilizamos tus datos para:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Proporcionar y mejorar nuestros servicios de gestión financiera</li>
                <li>Generar análisis y recomendaciones personalizadas</li>
                <li>Mantener la seguridad de tu cuenta</li>
                <li>Comunicarnos contigo sobre actualizaciones del servicio</li>
              </ul>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <Database className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">3. Almacenamiento y seguridad</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>
                Tus datos se almacenan de forma segura utilizando Supabase, que cumple con 
                estándares de seguridad de nivel bancario:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Cifrado en tránsito y en reposo</li>
                <li>Acceso restringido mediante autenticación</li>
                <li>Respaldos automáticos y seguros</li>
                <li>Cumplimiento con GDPR y otras regulaciones</li>
              </ul>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <Lock className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">4. Tus derechos</h2>
            </div>
            <div className="text-gray-700 space-y-4">
              <p>Tienes derecho a:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Acceder a tus datos personales</li>
                <li>Rectificar información incorrecta</li>
                <li>Eliminar tu cuenta y datos</li>
                <li>Exportar tus datos</li>
                <li>Revocar consentimientos</li>
              </ul>
            </div>
          </section>

          <section>
            <div className="flex items-center mb-4">
              <Mail className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-2xl font-semibold text-gray-900">5. Contacto</h2>
            </div>
            <div className="text-gray-700">
              <p>
                Si tienes preguntas sobre esta política de privacidad, puedes contactarnos en:
              </p>
              <p className="mt-2">
                <strong>Email:</strong> <EMAIL><br />
                <strong>Dirección:</strong> República Dominicana
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
