
import { logger } from "@/utils/logger";
import { PersonalDebt } from '@/types';
import { PaymentItem } from '../types/paymentTypes';

export class PaymentActionService {
  async handlePersonalDebtPayment(
    payment: PaymentItem,
    personalDebts: PersonalDebt[],
    addPersonalDebtPayment: (payment: any) => Promise<any>,
    updatePersonalDebt: (id: string, updates: any) => Promise<any>,
    paymentData?: any
  ): Promise<void> {
    const paidDate = new Date().toISOString().split('T')[0];
    
    logger.info('handlePersonalDebtPayment: Processing personal debt payment for payment ID:', payment.id);
    
    const debt = personalDebts.find(d => d.id === payment.referenceId);
    if (!debt) {
      logger.error('handlePersonalDebtPayment: Personal debt not found for payment reference ID:', payment.referenceId);
      throw new Error(`Personal debt with ID ${payment.referenceId} not found.`);
    }

    const currentBalance = debt.remainingBalance !== undefined ? debt.remainingBalance : debt.amount;
    
    const interestAmount = paymentData?.interestAmount || 0;
    const principalAmount = paymentData?.principalAmount || payment.amount;
    const totalAmount = paymentData?.totalAmount || payment.amount;
    const notes = paymentData?.notes || null;
    
    const newBalance = Math.max(0, currentBalance - principalAmount);
    
    const debtPaymentPayload = {
      personalDebtId: debt.id,
      paymentDate: paidDate,
      totalAmount,
      interestAmount,
      principalAmount,
      remainingBalance: newBalance,
      notes: notes,
    };

    try {
      await addPersonalDebtPayment(debtPaymentPayload);
      logger.info('handlePersonalDebtPayment: Successfully added personal debt payment record');
    } catch (error) {
      logger.error('handlePersonalDebtPayment: addPersonalDebtPayment failed', { error });
      throw error;
    }

    try {
      const debtUpdatePayload = { remainingBalance: newBalance };
      await updatePersonalDebt(debt.id, debtUpdatePayload);
      logger.info('handlePersonalDebtPayment: Successfully updated personal debt balance');
    } catch (error) {
      logger.error('handlePersonalDebtPayment: updatePersonalDebt failed', { error });
      logger.warn(`handlePersonalDebtPayment: CRITICAL INCONSISTENCY - Added debt payment record but FAILED to update debt balance`);
      throw error;
    }
  }

  async handleMarkAsPaid(
    payment: PaymentItem,
    personalDebts: PersonalDebt[],
    addPersonalDebtPaymentAdapter: (payment: any) => Promise<any>,
    updatePersonalDebtAdapter: (id: string, updates: any) => Promise<any>,
    markPaymentAsPaidAdapter: (id: string, paidDate: string, notes?: string | null) => Promise<any>,
    addPaymentRecordAdapter: (record: any) => Promise<any>,
    paymentData?: any
  ): Promise<void> {
    logger.info('handleMarkAsPaid: Starting to mark payment as paid for payment ID:', payment.id);
    const paidDate = new Date().toISOString().split('T')[0];

    try {
      if (payment.type === 'personal-debt') {
        logger.info('handleMarkAsPaid: Processing personal debt payment');
        await this.handlePersonalDebtPayment(
          payment,
          personalDebts,
          addPersonalDebtPaymentAdapter,
          updatePersonalDebtAdapter,
          paymentData
        );
        
        if (payment.recordId) {
          const finalNotesForRecord = paymentData?.notes || null;
          try {
            await markPaymentAsPaidAdapter(payment.recordId, paidDate, finalNotesForRecord);
            logger.info('handleMarkAsPaid: Successfully updated general payment record');
          } catch (error) {
            logger.error('handleMarkAsPaid: markPaymentAsPaidAdapter failed', { error });
            throw error;
          }
        } else {
          logger.info('handleMarkAsPaid: Creating new payment record for personal debt');
          const finalAmount = paymentData?.totalAmount || payment.amount;
          const finalNotes = paymentData?.notes || null;

          const recordData: any = {
            paymentType: payment.type,
            referenceId: payment.referenceId,
            dueDate: payment.dueDate,
            amount: finalAmount,
            currency: payment.currency,
            status: 'paid',
            paidDate: paidDate,
          };
          if (finalNotes) {
            recordData.notes = finalNotes;
          }

          try {
            await addPaymentRecordAdapter(recordData);
            logger.info('handleMarkAsPaid: Successfully created new payment record');
          } catch (error) {
            logger.error('handleMarkAsPaid: addPaymentRecordAdapter failed', { error });
            throw error;
          }
        }
      } else {
        // Logic for other payment types
        const finalAmount = paymentData?.totalAmount || payment.amount;
        const finalNotes = paymentData?.notes || null;

        if (payment.recordId) {
          logger.info('handleMarkAsPaid: Updating existing payment record:', payment.recordId);
          try {
            await markPaymentAsPaidAdapter(payment.recordId, paidDate, finalNotes);
            logger.info('handleMarkAsPaid: Successfully updated payment record');
          } catch (error) {
            logger.error('handleMarkAsPaid: markPaymentAsPaidAdapter failed', { error });
            throw error;
          }
        } else {
          logger.info('handleMarkAsPaid: Creating new payment record');
          const recordData: any = {
            paymentType: payment.type,
            referenceId: payment.referenceId,
            dueDate: payment.dueDate,
            amount: finalAmount,
            currency: payment.currency,
            status: 'paid',
            paidDate,
          };
          if (finalNotes) {
            recordData.notes = finalNotes;
          }
          
          try {
            await addPaymentRecordAdapter(recordData);
            logger.info('handleMarkAsPaid: Successfully created new payment record');
          } catch (error) {
            logger.error('handleMarkAsPaid: addPaymentRecordAdapter failed', { error });
            throw error;
          }
        }
      }
      logger.info('handleMarkAsPaid: Payment processing completed successfully');
    } catch (error) {
      logger.error('handleMarkAsPaid: General error during payment processing', { error });
      throw error;
    }
  }

  async handleUnmarkAsPaid(
    payment: PaymentItem,
    updatePaymentRecordAdapter: (id: string, updates: { status: string; paidDate: string | null; notes?: string }) => Promise<any>
  ): Promise<void> {
    logger.info('handleUnmarkAsPaid: Starting to unmark payment for payment ID:', payment.id);

    try {
      if (!payment.recordId) {
        logger.error('handleUnmarkAsPaid: Cannot unmark payment without a recordId');
        throw new Error('Cannot unmark payment without a recordId.');
      }

      const updatePayload = {
        status: 'pending',
        paidDate: null,
        notes: `Payment unmarked by user on ${new Date().toLocaleDateString()}`
      };

      try {
        await updatePaymentRecordAdapter(payment.recordId, updatePayload);
        logger.info('handleUnmarkAsPaid: Successfully updated payment record to pending');
      } catch (adapterError) {
        logger.error('handleUnmarkAsPaid: updatePaymentRecordAdapter failed', { error: adapterError });
        throw adapterError;
      }

      if (payment.type === 'personal-debt') {
        logger.warn('handleUnmarkAsPaid: Personal debt payment unmarked. Manual adjustment of debt ledger may be required.');
      }
      
      logger.info('handleUnmarkAsPaid: Payment record processed for unmarking successfully');

    } catch (error) {
      logger.error('handleUnmarkAsPaid: General error during unmarking payment', { error });
      throw error;
    }
  }
}
