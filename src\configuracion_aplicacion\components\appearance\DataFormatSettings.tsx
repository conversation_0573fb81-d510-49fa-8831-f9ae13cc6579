
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import type { AppearanceFormData } from '../../schemas/settingsSchemas';

interface DataFormatSettingsProps {
  appearanceForm: UseFormReturn<AppearanceFormData>;
  isLoading: boolean;
}

export const DataFormatSettings: React.FC<DataFormatSettingsProps> = ({
  appearanceForm,
  isLoading,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Formato de Datos</CardTitle>
        <CardDescription>
          Configura cómo se muestran las fechas y números
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={appearanceForm.control}
          name="dateFormat"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Formato de Fecha</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un formato" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                  <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                  <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={appearanceForm.control}
          name="numberFormat"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Formato de Números</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un formato" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="es-DO">Español (RD) - 1.234,56</SelectItem>
                  <SelectItem value="en-US">English (US) - 1,234.56</SelectItem>
                  <SelectItem value="es-ES">Español (ES) - 1.234,56</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={appearanceForm.control}
          name="showCurrency"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  Mostrar símbolo de moneda
                </FormLabel>
                <div className="text-sm text-muted-foreground">
                  Mostrar el símbolo de moneda en los valores
                </div>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <Button 
          type="submit" 
          disabled={isLoading} 
          className="w-full md:w-auto"
        >
          {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          Guardar Configuración
        </Button>
      </CardContent>
    </Card>
  );
};
