
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';

interface LoanDateFieldsProps {
  startDate: string;
  dueDate: string;
  paymentDate: string;
  onStartDateChange: (value: string) => void;
  onPaymentDateChange: (value: string) => void;
}

export function LoanDateFields({
  startDate,
  dueDate,
  paymentDate,
  onStartDateChange,
  onPaymentDateChange
}: LoanDateFieldsProps) {
  const isDarkMode = useIsDarkMode();

  // Función para convertir string a Date
  const parseDate = (dateString: string): Date | undefined => {
    if (!dateString) return undefined;
    return new Date(dateString);
  };

  // Función para convertir Date a string
  const formatDate = (date: Date | undefined): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="startDate">Fecha de Inicio</Label>
        {isDarkMode ? (
          <DatePicker
            id="startDate"
            date={parseDate(startDate)}
            onSelect={(date) => onStartDateChange(formatDate(date))}
            placeholder="Selecciona fecha de inicio"
            autoComplete="off"
          />
        ) : (
          <Input
            id="startDate"
            type="date"
            value={startDate}
            onChange={(e) => onStartDateChange(e.target.value)}
            autoComplete="off"
          />
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="dueDate">Fecha de Vencimiento (Calculada Automáticamente)</Label>
        <Input
          id="dueDate"
          type="date"
          value={dueDate}
          disabled
          className="bg-gray-50 dark:bg-gray-800"
          autoComplete="off"
        />
        <p className="text-xs text-gray-600 dark:text-gray-400">
          Esta fecha se calcula automáticamente basada en la fecha de inicio y el plazo
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="paymentDate">Fecha de Pago Mensual</Label>
        {isDarkMode ? (
          <DatePicker
            id="paymentDate"
            date={parseDate(paymentDate)}
            onSelect={(date) => onPaymentDateChange(formatDate(date))}
            placeholder="Selecciona fecha de pago"
            autoComplete="off"
          />
        ) : (
          <Input
            id="paymentDate"
            type="date"
            value={paymentDate}
            onChange={(e) => onPaymentDateChange(e.target.value)}
            autoComplete="off"
          />
        )}
      </div>
    </>
  );
}
