
import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { AdvancedRecommendations } from '../AdvancedRecommendations';

const ActionableInsights = React.lazy(() => 
  import('../ActionableInsights').then(module => ({ default: module.ActionableInsights }))
);

export const InsightsTab: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Recomendaciones avanzadas */}
      <AdvancedRecommendations />

      {/* Insights accionables con lazy loading */}
      <Suspense fallback={<Skeleton className="h-64 w-full" />}>
        <ActionableInsights />
      </Suspense>
    </div>
  );
};
