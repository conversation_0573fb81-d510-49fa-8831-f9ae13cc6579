import { useCallback } from 'react';
import { useSupabaseIncomes } from './useSupabaseIncomes';
import { auditLog } from '@/utils/securityUtils';
import { useAuth } from '@/contexts/AuthContext';
import type { Income } from '@/types';

export interface UseIncomeDataOptions {
  enabled?: boolean;
}

export const useIncomeData = ({ enabled = true }: UseIncomeDataOptions = {}) => {
  const { user } = useAuth();
  const incomeData = useSupabaseIncomes({ enabled });

  const secureAddIncome = useCallback(
    async (income: Omit<Income, 'id'>) => {
      try {
        auditLog('income_add_attempt', { userId: user?.id });
        const result = await incomeData.addIncome(income);
        auditLog('income_add_success', { userId: user?.id });
        return result;
      } catch (error) {
        auditLog('income_add_error', {
          userId: user?.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    },
    [incomeData, user?.id]
  );

  const secureUpdateIncome = useCallback(
    async (id: string, updates: Partial<Income>) => {
      try {
        auditLog('income_update_attempt', { userId: user?.id, incomeId: id });
        const result = await incomeData.updateIncome(id, updates);
        auditLog('income_update_success', { userId: user?.id, incomeId: id });
        return result;
      } catch (error) {
        auditLog('income_update_error', {
          userId: user?.id,
          incomeId: id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    },
    [incomeData, user?.id]
  );

  const secureDeleteIncome = useCallback(
    (id: string) => {
      try {
        auditLog('income_delete_attempt', { userId: user?.id, incomeId: id });
        incomeData.deleteIncome(id);
        auditLog('income_delete_success', { userId: user?.id, incomeId: id });
      } catch (error) {
        auditLog('income_delete_error', {
          userId: user?.id,
          incomeId: id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    },
    [incomeData, user?.id]
  );

  return {
    ...incomeData,
    addIncome: secureAddIncome,
    updateIncome: secureUpdateIncome,
    deleteIncome: secureDeleteIncome,
  };
};

