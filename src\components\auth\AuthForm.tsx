import type React from 'react';
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield } from 'lucide-react';
import { SecureLoginForm } from './SecureLoginForm';
import { SecureSignupForm } from './SecureSignupForm';
import { PasswordRecoveryForm } from './PasswordRecoveryForm';
import { EnhancedSecurityInfo } from './EnhancedSecurityInfo';
import { GoogleSignInButton } from './GoogleSignInButton';
import { AccountStatusHandler } from './AccountStatusHandler';
import { ConnectivityIndicator } from './ConnectivityIndicator';
import { SmartEmailInput } from './SmartEmailInput';
import { RegistrationProgress } from './RegistrationProgress';
import { EnhancedAuthModal } from './EnhancedAuthModal';
import { useAuth } from '@/contexts/AuthContext';

export const AuthForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('login');
  const [showSecurityModal, setShowSecurityModal] = useState(false);
  const [email, setEmail] = useState('');
  const [registrationStep, setRegistrationStep] = useState(1);
  const { user } = useAuth();

  const handleBackToLogin = () => {
    setActiveTab('login');
    setRegistrationStep(1);
  };

  const registrationSteps = [
    {
      id: '1',
      label: 'Información personal',
      completed: registrationStep > 1,
      current: registrationStep === 1,
    },
    {
      id: '2',
      label: 'Credenciales de acceso',
      completed: registrationStep > 2,
      current: registrationStep === 2,
    },
    {
      id: '3',
      label: 'Verificación de seguridad',
      completed: registrationStep > 3,
      current: registrationStep === 3,
    },
    {
      id: '4',
      label: 'Configuración inicial',
      completed: registrationStep > 4,
      current: registrationStep === 4,
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Header con indicador de conectividad */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <Shield className="w-12 h-12 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">FinanzApp</h1>
          <p className="text-gray-600 mt-2">Plataforma Segura de Gestión Financiera</p>
          <div className="flex justify-center mt-3">
            <ConnectivityIndicator />
          </div>
        </div>

        {/* Mostrar alertas de estado de cuenta si hay usuario autenticado */}
        {user && (
          <AccountStatusHandler user={user} showInline={true} />
        )}

        <Card className="border-0 shadow-xl">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-2xl">
              {activeTab === 'recovery' ? 'Recuperar Contraseña' : 'Acceso Seguro'}
            </CardTitle>
            <CardDescription>
              {activeTab === 'recovery' 
                ? 'Restablece tu contraseña de forma segura'
                : 'Protección avanzada para tus datos financieros'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeTab !== 'recovery' && (
              <>
                {/* Google Sign-In Button */}
                <div className="mb-6">
                  <GoogleSignInButton isLoading={isLoading} setIsLoading={setIsLoading} />
                </div>

                {/* Divider */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">O continúa con email</span>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'recovery' ? (
              <PasswordRecoveryForm onBackToLogin={handleBackToLogin} />
            ) : (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="login">Iniciar Sesión</TabsTrigger>
                  <TabsTrigger value="signup">Crear Cuenta</TabsTrigger>
                </TabsList>
                
                <TabsContent value="login" className="space-y-4">
                  <div className="space-y-4">
                    <SmartEmailInput
                      value={email}
                      onChange={setEmail}
                      label="Correo Electrónico"
                      required
                    />
                    <SecureLoginForm 
                      isLoading={isLoading} 
                      setIsLoading={setIsLoading}
                      onForgotPassword={() => setActiveTab('recovery')}
                      prefilledEmail={email}
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="signup" className="space-y-4">
                  {/* Progreso del registro */}
                  <RegistrationProgress
                    steps={registrationSteps}
                    currentStep={registrationStep}
                    totalSteps={4}
                  />
                  
                  <SecureSignupForm 
                    isLoading={isLoading} 
                    setIsLoading={setIsLoading}
                    onStepChange={setRegistrationStep}
                  />
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>

        <EnhancedSecurityInfo />

        {/* Modal de seguridad avanzada */}
        <EnhancedAuthModal
          isOpen={showSecurityModal}
          onClose={() => setShowSecurityModal(false)}
        />
      </div>
    </div>
  );
};
