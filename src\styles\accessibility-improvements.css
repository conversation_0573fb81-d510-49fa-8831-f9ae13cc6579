/* Mejoras de accesibilidad y contraste */
@layer base {
  :root {
    /* Colores con mejor contraste - Modo claro */
    --muted-foreground: 215.4 16.3% 35%; /* Más oscuro para mejor contraste */
    --secondary-foreground: 222.2 47.4% 8%; /* Más oscuro */
    
    /* Colores específicos para finanzas con mejor contraste */
    --finanz-primary: 142 76% 25%; /* Verde más oscuro */
    --finanz-primary-foreground: 0 0% 100%;
    --finanz-secondary: 210 40% 85%; /* Gris con mejor contraste */
    --finanz-secondary-foreground: 222.2 47.4% 8%;
    
    /* Estados con mejor contraste */
    --success: 142 76% 25%; /* Verde más oscuro */
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 35%; /* Amarillo más oscuro */
    --warning-foreground: 0 0% 100%;
    --error: 0 84.2% 45%; /* Rojo más oscuro */
    --error-foreground: 0 0% 100%;
    --info: 217 91% 35%; /* Azul más oscuro */
    --info-foreground: 0 0% 100%;
    
    /* Texto con mejor contraste */
    --text-primary: 222.2 84% 4.9%; /* Negro casi puro */
    --text-secondary: 215.4 16.3% 25%; /* Gris muy oscuro */
    --text-muted: 215.4 16.3% 35%; /* Gris oscuro */
    --text-disabled: 215.4 16.3% 50%; /* Gris medio */
  }

  .dark {
    /* Colores con mejor contraste - Modo oscuro */
    --muted-foreground: 215.4 16.3% 75%; /* Más claro para mejor contraste */
    --secondary-foreground: 210 40% 95%; /* Más claro */
    
    /* Colores específicos para finanzas con mejor contraste en modo oscuro */
    --finanz-primary: 142 76% 45%; /* Verde más claro */
    --finanz-primary-foreground: 0 0% 0%;
    --finanz-secondary: 210 40% 25%; /* Gris más oscuro */
    --finanz-secondary-foreground: 210 40% 95%;
    
    /* Estados con mejor contraste en modo oscuro */
    --success: 142 76% 45%; /* Verde más claro */
    --success-foreground: 0 0% 0%;
    --warning: 38 92% 55%; /* Amarillo más claro */
    --warning-foreground: 0 0% 0%;
    --error: 0 84.2% 65%; /* Rojo más claro */
    --error-foreground: 0 0% 0%;
    --info: 217 91% 55%; /* Azul más claro */
    --info-foreground: 0 0% 0%;
    
    /* Texto con mejor contraste en modo oscuro */
    --text-primary: 0 0% 98%; /* Blanco casi puro */
    --text-secondary: 215.4 16.3% 85%; /* Gris muy claro */
    --text-muted: 215.4 16.3% 75%; /* Gris claro */
    --text-disabled: 215.4 16.3% 60%; /* Gris medio */
  }
}

@layer components {
  /* Clases de utilidad para mejor contraste */
  .text-high-contrast {
    color: hsl(var(--text-primary));
  }
  
  .text-medium-contrast {
    color: hsl(var(--text-secondary));
  }
  
  .text-low-contrast {
    color: hsl(var(--text-muted));
  }
  
  .text-disabled {
    color: hsl(var(--text-disabled));
  }
  
  /* Botones con mejor contraste */
  .btn-high-contrast {
    background-color: hsl(var(--finanz-primary));
    color: hsl(var(--finanz-primary-foreground));
    border: 2px solid hsl(var(--finanz-primary));
  }
  
  .btn-high-contrast:hover {
    background-color: hsl(var(--finanz-primary) / 0.9);
    border-color: hsl(var(--finanz-primary) / 0.9);
  }
  
  .btn-high-contrast:focus {
    outline: 2px solid hsl(var(--finanz-primary));
    outline-offset: 2px;
  }
  
  /* Enlaces con mejor contraste */
  .link-high-contrast {
    color: hsl(var(--finanz-primary));
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 2px;
  }
  
  .link-high-contrast:hover {
    color: hsl(var(--finanz-primary) / 0.8);
  }
  
  .link-high-contrast:focus {
    outline: 2px solid hsl(var(--finanz-primary));
    outline-offset: 2px;
    border-radius: 2px;
  }
  
  /* Estados de éxito, advertencia y error con mejor contraste */
  .status-success {
    background-color: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
    border: 1px solid hsl(var(--success) / 0.3);
  }
  
  .status-warning {
    background-color: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
    border: 1px solid hsl(var(--warning) / 0.3);
  }
  
  .status-error {
    background-color: hsl(var(--error) / 0.1);
    color: hsl(var(--error));
    border: 1px solid hsl(var(--error) / 0.3);
  }
  
  .status-info {
    background-color: hsl(var(--info) / 0.1);
    color: hsl(var(--info));
    border: 1px solid hsl(var(--info) / 0.3);
  }
  
  /* Mejoras para formularios */
  .form-input-high-contrast {
    border: 2px solid hsl(var(--border));
    background-color: hsl(var(--background));
    color: hsl(var(--text-primary));
  }
  
  .form-input-high-contrast:focus {
    border-color: hsl(var(--finanz-primary));
    outline: 2px solid hsl(var(--finanz-primary) / 0.2);
    outline-offset: 0;
  }
  
  .form-label-high-contrast {
    color: hsl(var(--text-primary));
    font-weight: 600;
  }
  
  /* Mejoras para tarjetas */
  .card-high-contrast {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    color: hsl(var(--text-primary));
  }
  
  .card-header-high-contrast {
    color: hsl(var(--text-primary));
    font-weight: 600;
  }
  
  .card-content-high-contrast {
    color: hsl(var(--text-secondary));
  }
  
  /* Mejoras para navegación */
  .nav-link-high-contrast {
    color: hsl(var(--text-secondary));
    font-weight: 500;
  }
  
  .nav-link-high-contrast:hover {
    color: hsl(var(--finanz-primary));
    background-color: hsl(var(--finanz-primary) / 0.1);
  }
  
  .nav-link-high-contrast.active {
    color: hsl(var(--finanz-primary));
    background-color: hsl(var(--finanz-primary) / 0.15);
    font-weight: 600;
  }
  
  /* Mejoras para tablas */
  .table-high-contrast {
    border: 1px solid hsl(var(--border));
  }
  
  .table-high-contrast th {
    background-color: hsl(var(--muted));
    color: hsl(var(--text-primary));
    font-weight: 600;
    border-bottom: 2px solid hsl(var(--border));
  }
  
  .table-high-contrast td {
    color: hsl(var(--text-secondary));
    border-bottom: 1px solid hsl(var(--border));
  }
  
  .table-high-contrast tr:hover {
    background-color: hsl(var(--muted) / 0.5);
  }
}

/* Mejoras específicas para elementos existentes */
@layer utilities {
  /* Override de clases de Tailwind con mejor contraste */
  .text-muted-foreground {
    color: hsl(var(--text-muted)) !important;
  }
  
  .text-secondary-foreground {
    color: hsl(var(--text-secondary)) !important;
  }
  
  /* Asegurar que los elementos interactivos tengan suficiente contraste */
  button:not(:disabled) {
    color: hsl(var(--text-primary));
  }
  
  a:not(.no-contrast) {
    color: hsl(var(--finanz-primary));
  }
  
  /* Mejoras para focus visible */
  *:focus-visible {
    outline: 2px solid hsl(var(--finanz-primary));
    outline-offset: 2px;
  }
}
